[package]
name = "app"
version.workspace = true
edition.workspace = true

[dependencies]
shared = { path = "../shared" }
chain-listener = { path = "../chain-listener" }
data-parser = { path = "../data-parser" }
state-manager = { path = "../state-manager" }
arbitrage-engine = { path = "../arbitrage-engine" }
onchain-executor = { path = "../onchain-executor" }

chrono.workspace = true
thiserror.workspace = true
tokio = { workspace = true, features = ["rt", "rt-multi-thread", "macros", "sync"]}
tracing.workspace = true
tracing-appender.workspace = true
tracing-subscriber.workspace = true
solana-sdk = { workspace = true }
solana-client.workspace = true
async-trait.workspace = true
