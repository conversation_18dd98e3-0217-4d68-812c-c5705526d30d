//! 事件协调器
//!
//! 负责协调池管理器和套利引擎之间的事件处理，避免数据丢失

use std::str::FromStr;
use std::sync::Arc;
use std::path::PathBuf;
use std::time::Duration;
use shared::pump_swap::GlobalConfig;
use state_manager::core::DexPoolManager;
use arbitrage_engine::{tokens, BellmanFordArbitrageEngine};
use solana_sdk::{pubkey::Pubkey, signature::{Keypair, Signer}};
use solana_client::nonblocking::rpc_client::RpcClient;
use async_trait::async_trait;
use solana_sdk::pubkey;
use data_parser::{ParsedAccount};
use shared::raydium_cpmm::AmmConfig;
use shared::{KeypairVault, TokenMintInfo};
use state_manager::{MeteoraDammPoolManager, MeteoraLbPairState, MeteoraLbPoolManager, PumpSwapPoolManager, RaydiumClmmPoolState, RaydiumCpmmPoolManager};
use crate::event::{ArbitrageEvent, EventError, EventHandler, EventReceiver, EventResult, EventSender, PoolTokenMintUpdateEvent, PoolUpdateEvent, SystemEvent, ArbitrageExecutedEvent, ArbitrageFailedEvent};
use crate::publish_event;
use tracing::{info, warn, error};
use onchain_executor::ata_cache::ATACache;
use onchain_executor::coordinator::CoordinatorConfig;
use onchain_executor::{BalanceService, ExecutionCoordinator, ExecutorError, InstructionRequest, TransactionRequest};
use onchain_executor::types::{ExecutionPriority, InstructionParams, SwapParams, SwapMode, PoolRef};
use onchain_executor::risk_manager::{RiskConfig, RiskManager};
use onchain_executor::transaction_builder::TransactionBuilder;
use onchain_executor::transaction_executor::{ExecutorConfig, TransactionExecutor};

/// 事件协调器
///
/// 管理池更新事件和套利检测的协调工作
pub struct EventCoordinator {
    /// 池管理器
    pool_manager: Arc<DexPoolManager>,
    /// 套利引擎
    arbitrage_engine: Arc<BellmanFordArbitrageEngine>,
    /// 套利执行协调器
    arbitrage_coordinator: Option<Arc<ExecutionCoordinator>>,
    /// 事件发送器（用于发布套利机会事件）
    event_sender: EventSender,
}

impl EventCoordinator {
    /// 创建新的事件协调器（简化版本，不带执行器）
    pub fn new(
        pool_manager: Arc<DexPoolManager>,
        arbitrage_engine: Arc<BellmanFordArbitrageEngine>,
        event_sender: EventSender,
    ) -> Self {
        Self {
            pool_manager,
            arbitrage_engine,
            arbitrage_coordinator: None,
            event_sender,
        }
    }

    /// 创建带执行器的事件协调器
    pub async fn new_with_executor(
        pool_manager: Arc<DexPoolManager>,
        arbitrage_engine: Arc<BellmanFordArbitrageEngine>,
        event_sender: EventSender,
        wallet: Arc<Keypair>,
        rpc_url: &str,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // 创建RPC客户端
        let rpc_client = Arc::new(RpcClient::new(rpc_url.to_string()));

        // 创建ATA缓存
        let ata_cache = Arc::new(ATACache::new(
            rpc_client.clone(),
            wallet.pubkey(),
            PathBuf::from("./data/ata_cache.json"),
            Duration::from_secs(1800), // 30分钟TTL
        ));

        // 预热缓存
        ata_cache.initialize_with_preheat().await?;

        // 创建各个组件
        let transaction_builder = Arc::new(TransactionBuilder::new(
            wallet.clone(),
            rpc_client.clone(),
            ata_cache.clone(),
        ));

        let transaction_executor = Arc::new(TransactionExecutor::new(
            rpc_client.clone(),
            ExecutorConfig::default(),
        ));


        let risk_manager = Arc::new(RiskManager::new(RiskConfig::default()));

        // 创建执行协调器
        let arbitrage_coordinator = Arc::new(
            ExecutionCoordinator::new(
                transaction_builder,
                transaction_executor,
                Arc::new(BalanceService::new(wallet.clone(), rpc_client.clone(), 1, 1, Duration::from_secs(100))),
                risk_manager,
                ata_cache,
                CoordinatorConfig::default())
        );

        Ok(Self {
            pool_manager,
            arbitrage_engine,
            arbitrage_coordinator: Some(arbitrage_coordinator),
            event_sender,
        })
    }

    /// 处理池更新事件
    async fn handle_pool_update(&self, event: PoolUpdateEvent) -> EventResult<()> {
        let pool_address = event.pool_address;
        let account_data = event.parsed_account;

        println!("Processing pool update for {}", pool_address);

        // 获取更新前的池价格（如果存在）
        let pre_pool_price = self.pool_manager.get_pool_price(&pool_address).unwrap_or_default();

        // 更新state manager中的池状态
        update_pool_state(&self.pool_manager, &account_data).await
            .map_err(|e| EventError::ProcessingFailed(format!("Failed to update pool state: {}", e)))?;

        // 获取更新后的池价格
        let post_pool_price = self.pool_manager.get_pool_price(&pool_address)
            .map_err(|e| EventError::ProcessingFailed(format!("Failed to get updated pool state: {}", e)))?;

        println!("Pool {} price updated from {:.10} to {:.10}", pool_address, pre_pool_price, post_pool_price);


        // 检查是否需要触发套利检测
        let should_detect_arbitrage = if pre_pool_price > 0.0 {
            let price_change_pct = ((post_pool_price - pre_pool_price) / pre_pool_price * 100.0).abs();
            price_change_pct > 0.0
        } else {
            true
        };


        if should_detect_arbitrage {
            println!("检测套利机会 {}", pool_address);

            // 检测套利机会（ArbitrageEngine 会自动重建图）
            // 从 SOL 开始搜索套利机会
            match self.arbitrage_engine.detect_arbitrage_opportunities(vec![tokens::sol()]).await {
                Ok(opportunities) if !opportunities.is_empty() => {
                    info!("🎯 Found {} arbitrage opportunities", opportunities.len());
                    let arbitrage_event = ArbitrageEvent::new(opportunities);
                    let event = SystemEvent::ArbitrageOpportunity(arbitrage_event);
                    publish_event!(&self.event_sender, event);
                }
                Ok(_) => {} // 没有套利机会，不需要处理
                Err(e) => {
                    warn!("Failed to detect arbitrage opportunities: {}", e);
                }
            }
        }

        Ok(())
    }

    /// 处理套利机会事件
    async fn handle_arbitrage_opportunity(&self, event: ArbitrageEvent) -> EventResult<()> {
        let opportunities = &event.opportunities;
        if opportunities.is_empty() {
            return Ok(());
        }

        info!("🎯 发现 {} 个套利机会，开始处理", opportunities.len());
        info!("套利路径: {:?}", opportunities);

        // 如果有执行器，尝试执行最佳机会
        if let Some(ref coordinator) = self.arbitrage_coordinator {
            // 取第一个机会作为最佳机会
            if let Some(best_path) = opportunities.first() {

                // 将套利路径转换为交易请求
                match convert_arbitrage_path_to_transaction_request(best_path) {
                    Ok(transaction_request) => {
                        info!("📝 转换套利路径为交易请求成功，包含 {} 个指令", transaction_request.instructions.len());

                        // 执行交易请求
                        match coordinator.execute_transaction_request(transaction_request).await {
                            Ok(result) => {
                                let success_event = ArbitrageExecutedEvent {
                                    signature: result.signature,
                                    profit_lamports: 0, // TODO: 从结果中计算实际利润
                                    path_length: best_path.steps.len() as u8,
                                    gas_used: result.gas_used,
                                    execution_time_ms: result.execution_time.as_millis() as u64,
                                    timestamp: std::time::SystemTime::now()
                                        .duration_since(std::time::UNIX_EPOCH)
                                        .unwrap_or_default()
                                        .as_secs(),
                                };
                                let event = SystemEvent::ArbitrageExecuted(success_event);
                                publish_event!(&self.event_sender, event);

                                info!("🎉 套利执行成功！签名: {}", result.signature);
                            },
                            Err(error) => {
                                let failure_reason = self.classify_failure_reason(&error);
                                let failed_event = ArbitrageFailedEvent {
                                    error: error.to_string(),
                                    path: best_path.clone(),
                                    attempted_amount: 100000, // TODO: 使用实际的输入金额
                                    failure_reason,
                                    timestamp: std::time::SystemTime::now()
                                        .duration_since(std::time::UNIX_EPOCH)
                                        .unwrap_or_default()
                                        .as_secs(),
                                };
                                let event = SystemEvent::ArbitrageExecutionFailed(failed_event);
                                publish_event!(&self.event_sender, event);

                                warn!("💥 套利执行失败: {}", error);
                            }
                        }
                    },
                    Err(e) => {
                        warn!("❌ 转换套利路径为交易请求失败: {}", e);
                    }
                }

                info!("🚀 执行套利机会: 路径长度={}", best_path.steps.len());

            }
        } else {
            // 如果没有执行器，仅打印信息
            for (i, opportunity) in opportunities.iter().take(3).enumerate() {
                info!("套利机会 {}: 路径长度={}",
                     i + 1,
                     opportunity.steps.len());
            }
            info!("💡 提示: 使用 new_with_executor() 创建协调器以启用自动执行");
        }

        Ok(())
    }

    /// 分类失败原因
    fn classify_failure_reason(&self, error: &ExecutorError) -> String {
        use ExecutorError;

        match error {
            ExecutorError::InsufficientFunds(_) => "资金不足".to_string(),
            ExecutorError::SlippageTooHigh => "滑点过大".to_string(),
            ExecutorError::TransactionFailed(_) => "交易执行失败".to_string(),
            ExecutorError::SimulationFailed(_) => "模拟执行失败".to_string(),
            ExecutorError::RiskAssessmentFailed(_) => "风险评估失败".to_string(),
            ExecutorError::ExecutionTimeout => "执行超时".to_string(),
            _ => "未知错误".to_string(),
        }
    }

    async fn handle_pool_token_mint_update(
        &self,
        event: PoolTokenMintUpdateEvent,
    ) -> EventResult<()> {
        let pool_address = &event.pool_address.clone();

        let pool_key = Pubkey::from_str(pool_address).unwrap();

        // 获取更新前的池价格
        let pre_pool_price = self.pool_manager.get_pool_price(&pool_key).unwrap_or_default();

        // 更新池的代币 mint 信息
        self.pool_manager.update_pool_token_mint_balance(event.pool_address, event.token_mint_info)
            .map_err(|e| EventError::ProcessingFailed(format!("Failed to update pool token mint: {}", e)))?;

        // 获取更新后的池价格
        let post_pool_price = self.pool_manager.get_pool_price(&pool_key)
            .map_err(|e| EventError::ProcessingFailed(format!("Failed to get updated pool state: {}", e)))?;

        println!(
            "Token mint更新: 池 {} 的价格从 {:.10} 更新为 {:.10}",
            pool_address, pre_pool_price, post_pool_price
        );

        // 检查是否需要触发套利检测
        let should_detect_arbitrage = if pre_pool_price > 0.0 {
            let price_change_pct = ((post_pool_price - pre_pool_price) / pre_pool_price * 100.0).abs();
            price_change_pct > 0.0
        } else {
            true
        };

        if should_detect_arbitrage {
            println!("Token mint更新触发套利检测 {}", pool_address);

            // 检测套利机会（ArbitrageEngine 会自动重建图）
            // 从 SOL 开始搜索套利机会
            match self.arbitrage_engine.detect_arbitrage_opportunities(vec![tokens::sol()]).await {
                Ok(opportunities) if !opportunities.is_empty() => {
                    info!("🎯 Found {} arbitrage opportunities", opportunities.len());
                    let arbitrage_event = ArbitrageEvent::new(opportunities);
                    let event = SystemEvent::ArbitrageOpportunity(arbitrage_event);
                    publish_event!(&self.event_sender, event);
                }
                Ok(_) => {}
                Err(e) => {
                    warn!("Failed to detect arbitrage opportunities: {}", e);
                }
            }
        }

        Ok(())
    }

    /// 启动事件处理循环
    pub async fn start_event_loop(self, mut event_receiver: EventReceiver) {
        info!("事件协调器开始运行");

        while let Some(event) = event_receiver.recv().await {
            let result = match &event {
                SystemEvent::PoolUpdate(pool_event) => {
                    self.handle_pool_update(pool_event.clone()).await
                }
                SystemEvent::PoolTokenMintUpdate(token_event) => {
                    self.handle_pool_token_mint_update(token_event.clone()).await
                }
                SystemEvent::ArbitrageOpportunity(arbitrage_event) => {
                    self.handle_arbitrage_opportunity(arbitrage_event.clone()).await
                }
                SystemEvent::ArbitrageExecuted(executed_event) => {
                    info!("🎉 套利执行成功: 利润 {} lamports", executed_event.profit_lamports);
                    Ok(())
                }
                SystemEvent::ArbitrageExecutionFailed(failed_event) => {
                    warn!("💥 套利执行失败: {}", failed_event.failure_reason);
                    Ok(())
                }
            };

            if let Err(e) = result {
                error!("事件处理失败 {}: {}", event, e);
            }
        }

        warn!("事件协调器退出：事件通道已关闭");
    }
}

/// 将套利路径转换为交易请求
///
/// 将 ArbitragePath 中的每个 SwapStep 转换为 InstructionRequest，
/// 创建一个完整的 TransactionRequest 用于执行套利交易
fn convert_arbitrage_path_to_transaction_request(
    arbitrage_path: &arbitrage_engine::ArbitragePath
) -> Result<TransactionRequest, String> {
    use std::collections::HashMap;

    if arbitrage_path.steps.is_empty() {
        return Err("套利路径为空".to_string());
    }

    // 将每个交换步骤转换为指令请求
    let mut instructions = Vec::new();

    let mut current_amount = 100_000_000u64;

    for step in arbitrage_path.steps.iter() {
        // 计算预期输出金额
        let expected_output = (current_amount as f64 * step.rate) as u64;

        // 创建池引用
        let pool_ref = PoolRef {
            address: step.pool_address,
            token_a: step.from_token,
            token_b: step.to_token,
            protocol: step.protocol.clone(),
            pool_type: step.pool_type,
        };

        // 创建交换参数
        let swap_params = SwapParams {
            from_token: step.from_token,
            to_token: step.to_token,
            mode: SwapMode::ExactIn {
                amount_in: current_amount,
                min_output: Some((expected_output as f64 * 0.95) as u64), // 5%滑点保护
            },
            slippage_bps: 500, // 5% 滑点
            pool: pool_ref,
        };

        // 创建指令请求
        let instruction = InstructionRequest {
            params: InstructionParams::Swap(swap_params),
        };

        instructions.push(instruction);
        current_amount = expected_output;
    }

    // 创建元数据
    let mut metadata = HashMap::new();
    metadata.insert("strategy".to_string(), "arbitrage".to_string());
    metadata.insert("path_length".to_string(), arbitrage_path.steps.len().to_string());
    metadata.insert("expected_profit_ratio".to_string(), arbitrage_path.expected_profit_ratio.to_string());
    metadata.insert("start_token".to_string(), arbitrage_path.start_token.to_string());

    // 创建交易请求
    let transaction_request = TransactionRequest::new(
        instructions,
        ExecutionPriority::High, // 套利交易使用高优先级
        metadata,
    );

    Ok(transaction_request)
}

/// 根据解析的账户数据增量更新池状态管理器
async fn update_pool_state(
    pool_manager: &Arc<DexPoolManager>,
    parsed_data: &ParsedAccount,
) -> shared::Result<()> {
    use data_parser::accounts::ParsedAccountData;

    match &parsed_data.data {
        ParsedAccountData::RaydiumPoolState { address, data } => {
            // 转换 data_parser 的 PoolState 到 state_manager 的 RaydiumClmmPoolState
            let state_manager_pool_state = convert_raydium_pool_state(*address, data);

            // 使用增量更新，如果池不存在则创建新的
            if !pool_manager.update_raydium_pool_state(*address, state_manager_pool_state.clone())? {
                // 池不存在，创建新的池管理器
                let raydium_manager = state_manager::dex::raydium::clmm::RaydiumClmmPoolManager::new(state_manager_pool_state);
                pool_manager.add_pool(raydium_manager)?;
            }
            Ok(())
        }
        ParsedAccountData::MeteoraLbPair { address, data } => {
            let state_manager_pool_state = data.into();

            let sol_wrapped_sol_mint = pubkey!("So11111111111111111111111111111111111111112");
            let token_x_decimals = if data.token_x_mint == sol_wrapped_sol_mint {
                9
            } else {
                6
            };
            let token_y_decimals = if data.token_y_mint == sol_wrapped_sol_mint {
                9
            } else {
                6
            };

            let state_manager_pool_state = MeteoraLbPairState {
                address: *address,
                token_x_decimals,
                token_y_decimals,
                ..state_manager_pool_state
            };

            // 使用增量更新，如果池不存在则创建新的
            if !pool_manager.update_meteora_dlmm_pool_state(*address, state_manager_pool_state.clone())? {
                // 池不存在，创建新的池管理器
                let meteora_manager = MeteoraLbPoolManager::new(state_manager_pool_state);
                pool_manager.add_pool(meteora_manager)?;
            }
            Ok(())
        }
        ParsedAccountData::PumpSwapPool { address, data } => {
            // 使用真正的增量更新：保留现有的 token_mint_info，只更新其他池状态
            // 先创建完整的管理器，然后提取状态进行增量更新
            let pump_swap_manager = PumpSwapPoolManager::from_account_data(
                *address, data.clone(), &GlobalConfig::default(), Some(1), Some(1))?;
            let pump_swap_state = pump_swap_manager.pool_state.clone();

            if !pool_manager.update_pump_swap_pool_state(*address, pump_swap_state)? {
                // 池不存在，添加新的池管理器
                pool_manager.add_pool(pump_swap_manager)?;
            }
            Ok(())
        }
        ParsedAccountData::RaydiumCpmmPoolState {address, data} => {
            // 使用真正的增量更新：保留现有的 token_mint_info，只更新其他池状态
            let raydium_manager = RaydiumCpmmPoolManager::from_account_data(
                *address, data.clone(), &AmmConfig::default(), Some(1), Some(1))?;
            let cpmm_state = raydium_manager.pool_state.clone();

            if !pool_manager.update_raydium_cpmm_pool_state(*address, cpmm_state)? {
                // 池不存在，添加新的池管理器
                pool_manager.add_pool(raydium_manager)?;
            }
            Ok(())
        }
        ParsedAccountData::MeteoraDammPool {address, data} => {
            // 使用真正的增量更新：保留现有的 token_mint_info，只更新其他池状态
            let meteora_manager = MeteoraDammPoolManager::from_account_data(
                *address, data.clone(), Some(1), Some(1), Some(6), Some(6))?;
            let damm_state = meteora_manager.pool_state.clone();

            if !pool_manager.update_meteora_damm_pool_state(*address, damm_state)? {
                // 池不存在，添加新的池管理器
                pool_manager.add_pool(meteora_manager)?;
            }
            Ok(())
        }
        _ => {
            println!("⚠️  暂不支持的账户类型: {:?}", parsed_data.data.account_type());
            Ok(())
        }
    }
}


/// 转换 data_parser 的 PoolState 到 state_manager 的 RaydiumClmmPoolState
fn convert_raydium_pool_state(
    pool_address: Pubkey,
    parser_data: &shared::anchor_types::raydium::PoolState,
) -> RaydiumClmmPoolState {
    use state_manager::dex::raydium::clmm::pool::RaydiumClmmPoolState;

    RaydiumClmmPoolState {
        pool_id: pool_address,
        amm_config: parser_data.amm_config,
        owner: parser_data.owner,
        token_mint_0: parser_data.token_mint_0,
        token_mint_1: parser_data.token_mint_1,
        token_vault_0: parser_data.token_vault_0,
        token_vault_1: parser_data.token_vault_1,
        observation_key: parser_data.observation_key,
        mint_decimals_0: parser_data.mint_decimals_0,
        mint_decimals_1: parser_data.mint_decimals_1,
        tick_spacing: parser_data.tick_spacing,
        liquidity: parser_data.liquidity,
        sqrt_price_x64: parser_data.sqrt_price_x64,
        tick_current: parser_data.tick_current,
        fee_growth_global_0_x64: parser_data.fee_growth_global_0_x64,
        fee_growth_global_1_x64: parser_data.fee_growth_global_1_x64,
        protocol_fees_token_0: parser_data.protocol_fees_token_0,
        protocol_fees_token_1: parser_data.protocol_fees_token_1,
        swap_in_amount_token_0: parser_data.swap_in_amount_token_0,
        swap_out_amount_token_1: parser_data.swap_out_amount_token_1,
        swap_in_amount_token_1: parser_data.swap_in_amount_token_1,
        swap_out_amount_token_0: parser_data.swap_out_amount_token_0,
        status: parser_data.status,
        // 转换奖励信息数组
        reward_infos: parser_data.reward_infos.iter().map(|r| state_manager::dex::raydium::clmm::types::RewardInfo {
            reward_state: r.reward_state,
            open_time: r.open_time,
            end_time: r.end_time,
            last_update_time: r.last_update_time,
            emissions_per_second_x64: r.emissions_per_second_x64,
            reward_total_emissioned: r.reward_total_emissioned,
            reward_claimed: r.reward_claimed,
            token_mint: r.token_mint,
            token_vault: r.token_vault,
            authority: r.authority,
            reward_growth_global_x64: r.reward_growth_global_x64,
        }).collect(),
        tick_array_bitmap: parser_data.tick_array_bitmap,
        total_fees_token_0: parser_data.total_fees_token_0,
        total_fees_claimed_token_0: parser_data.total_fees_claimed_token_0,
        total_fees_token_1: parser_data.total_fees_token_1,
        total_fees_claimed_token_1: parser_data.total_fees_claimed_token_1,
        fund_fees_token_0: parser_data.fund_fees_token_0,
        fund_fees_token_1: parser_data.fund_fees_token_1,
        open_time: parser_data.open_time,
        recent_epoch: parser_data.recent_epoch,
        // 添加缺失的字段，使用默认值
        fee_rate: 500, // 默认费率 0.05%
        active: parser_data.status == 1, // 状态为1时表示激活
    }
}




#[async_trait]
impl EventHandler for EventCoordinator {
    async fn handle_event(&self, event: SystemEvent) -> EventResult<()> {
        match event {
            SystemEvent::PoolUpdate(pool_event) => {
                self.handle_pool_update(pool_event).await
            }
            SystemEvent::PoolTokenMintUpdate(token_event) => {
                self.handle_pool_token_mint_update(token_event).await
            }
            SystemEvent::ArbitrageOpportunity(arbitrage_event) => {
                self.handle_arbitrage_opportunity(arbitrage_event).await
            }
            SystemEvent::ArbitrageExecuted(executed_event) => {
                info!("🎉 套利执行成功事件: 利润 {} lamports, 签名 {}",
                     executed_event.profit_lamports, executed_event.signature);
                Ok(())
            }
            SystemEvent::ArbitrageExecutionFailed(failed_event) => {
                warn!("💥 套利执行失败事件: {}", failed_event.failure_reason);
                Ok(())
            }
        }
    }

    fn name(&self) -> &'static str {
        "EventCoordinator"
    }
}

/// 创建并启动事件协调器
pub async fn start_event_coordinator(
    pool_manager: Arc<DexPoolManager>,
    arbitrage_engine: Arc<BellmanFordArbitrageEngine>,
    event_sender: EventSender,
    event_receiver: EventReceiver,
) -> tokio::task::JoinHandle<()> {
    let vault_path = PathBuf::new().join("crates/shared/.real_vault.bin");

    let pw = "TestPassword123!";
    let loaded_vault = KeypairVault::load(&vault_path).unwrap();
    let keypair = loaded_vault.decrypt(pw).unwrap();
    println!("\n🔑 使用的钱包地址: {}", keypair.pubkey());


    let rpc_url = "https://mainnet.helius-rpc.com/?api-key=d836ddde-cf6c-452c-8fb5-b08e06fe3092";
    let coordinator = EventCoordinator::new_with_executor(pool_manager, arbitrage_engine, event_sender, Arc::new(keypair), rpc_url).await.unwrap();

    tokio::spawn(async move {
        coordinator.start_event_loop(event_receiver).await;
    })
}

/// 便捷函数：发布池更新事件
pub fn publish_pool_update_event(
    event_sender: &EventSender,
    pool_address: Pubkey,
    account_data: ParsedAccount,
) {
    let event = SystemEvent::PoolUpdate(PoolUpdateEvent::new(pool_address, account_data));
    publish_event!(event_sender, event);
}


pub fn publish_pool_token_mint_update_event(
    event_sender: &EventSender,
    token_mint_info: TokenMintInfo,
    pool_address: String
) {
    let event = SystemEvent::PoolTokenMintUpdate(PoolTokenMintUpdateEvent::new(token_mint_info, pool_address));
    publish_event!(event_sender, event);
}
