use std::sync::Arc;
use std::collections::HashMap;
use solana_sdk::pubkey;
use tracing::info;
use chain_listener::{ChainListenerConfig, DataHandler, SubscriptionConfig, YellowstoneGrpcClient};
use data_parser::{global_registry, register_global_parser, AccountParserRegistry, AccountType,
                  RaydiumClmmEventParser};
use data_parser::accounts::{MeteoraAnchorAccountParser, MeteoraDammAnchorAccountParser,
                            PumpSwapAnchorAccountParser, RaydiumAnchorAccountParser,
                            RaydiumCpmmAccountParser};
use shared::{DataType, TokenMintInfo};
use state_manager::DexPoolManager;
use arbitrage_engine::{BellmanFordArbitrageEngine};
use solana_sdk::pubkey::Pubkey;
use crate::event::create_event_channel;
use crate::logging::init_logging;
use crate::event_coordinator::{start_event_coordinator, publish_pool_update_event, publish_pool_token_mint_update_event};
use crate::spl_token_parser::SplTokenAccountParser;

mod logging;
mod event_coordinator;
mod event;
mod spl_token_parser;

/// 池子配置结构，管理地址和协议映射
#[derive(Debug, Clone)]
struct PoolConfig {
    /// 池子地址到协议信息的映射
    pools: HashMap<String, PoolInfo>,
    token_mints: HashMap<String, TokenMintInfo>,
    vault_to_pool: HashMap<String, String>,
}

#[derive(Debug, Clone)]
struct PoolInfo {
    /// 池子地址
    address: String,
    /// 协议程序ID
    program_id: String,
    /// 账户类型
    account_type: AccountType,
    /// 协议名称
    protocol_name: &'static str,
    token_mint_accounts: Vec<String>,
}

impl PoolConfig {
    fn new() -> Self {
        // 池子配置数组 - 添加新池子只需要在这里添加一行
        let pool_configs = [
            // (pool_address, program_id, account_type, protocol_name)
            ("3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv", "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK", None, None, AccountType::RaydiumPoolState, "Raydium"),
            // ("8ujpQXxnnWvRohU2oCe3eaSzoL7paU2uj3fEn4Zp72US", "CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C", AccountType::RaydiumCpmmPoolState, "Raydium"),
            // ("45FffdEVGaik9mraDpFkhnLbfghTSVwQJx3mnLXHV5hk", "CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C", AccountType::RaydiumCpmmPoolState, "Raydium"),
            // ("5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6", "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", AccountType::MeteoraLbPair, "Meteora"),
            // ("Fhdtz1THK4Vs3KwoJPztVFZCc24jfNQZsVYB28Zvi5FM", "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", AccountType::MeteoraLbPair, "Meteora"),
            ("3eT45gu5yLZjhTmWrWKATeeKMnEXTY8ke1L4YD5yqCuU", "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", None, None, AccountType::MeteoraLbPair, "Meteora"),
            ("7UnkoDCAuJnbL5RJkSR6HhbCkAsxpKoPYLzYjmw1Pr22", "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", None, None, AccountType::MeteoraLbPair, "Meteora"),
            ("9o9PL6EtF7tezDszMbeELksuUCKrKckKDUF9Kea8rwjr", "cpamdpZCGKUy5JxQXB4dcpGPiikHawvSWAd6mEn1sGG",
             Some(TokenMintInfo::new(pubkey!("So11111111111111111111111111111111111111112"), 9, pubkey!("4yxYk8HYm6DAZiNYo9WdEZLKC7Q1nLrQ5SbknwHD4kP3"), 0 )),
             Some(TokenMintInfo::new(pubkey!("3C6S7LGiyzdr5j1ZB2WfadPPzbwE15VxntmdtGY3BAGS"), 6, pubkey!("63L7ijG8WMcceAbZFJZvyVNmQQcH7rbP6SVt16LW6yH4"), 0 )), AccountType::MeteoraDammPool, "Meteora"),
            ("E9pd39bsqnQ62FVB4o7HMm8RZ8Hw2zG5wNsZDVgfBcJJ", "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA",
                 Some(TokenMintInfo::new(pubkey!("So11111111111111111111111111111111111111112"), 9, pubkey!("7xUNxHD3Nk75gLModDcuAVX3c7eZ3xFWRoMJRSSChaT"), 0 )),
                 Some(TokenMintInfo::new(pubkey!("66LPmREAYBqPeyidL7LMW7RaJjKbfADbuUMsqatkpump"), 6, pubkey!("2wGi8iyPjXRuykWqpLyARgetHx7GCTK6AqH1cZxy1xk8"), 0 )),
                AccountType::PumpSwapPool, "Pump"),
            ("********************************************", "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA",
                 Some(TokenMintInfo::new(pubkey!("So11111111111111111111111111111111111111112"), 9, pubkey!("G9J4M1SCQRfX1qbT9wvWVPSgmGaDwUn2SRWneETtSVbX"), 0 )),
                 Some(TokenMintInfo::new(pubkey!("5zCETicUCJqJ5Z3wbfFPZqtSpHPYqnggs1wX7ZRpump"), 6, pubkey!("7isfXQm9GJNdSz2Mdhj8xhYFApueXnGFLM2dTs3hED4Y"), 0 )),
                AccountType::PumpSwapPool, "Pump"),
        ];

        let mut token_mints = HashMap::new();
        let mut vault_to_pool = HashMap::new();
        let pools = pool_configs
            .iter()
            .map(|(address, program_id,token_a_mint, token_b_mint, account_type, protocol_name)| {
                let mut token_mint_accounts = Vec::new();
                if let Some(token_a) = token_a_mint {
                    token_mint_accounts.push(token_a.token_vault.to_string());
                    token_mints.insert(token_a.token_vault.to_string(), token_a.clone());
                    vault_to_pool.insert(token_a.token_vault.to_string(), address.to_string());
                }
                if let Some(token_b) = token_b_mint {
                    token_mint_accounts.push(token_b.token_vault.to_string());
                    token_mints.insert(token_b.token_vault.to_string(), token_b.clone());
                    vault_to_pool.insert(token_b.token_vault.to_string(), address.to_string());
                }

                let pool_info = PoolInfo {
                    address: address.to_string(),
                    program_id: program_id.to_string(),
                    account_type: account_type.clone(),
                    protocol_name,
                    token_mint_accounts
                };
                (address.to_string(), pool_info)
            })
            .collect();

        Self { pools, token_mints, vault_to_pool }
    }

    /// 获取所有池子地址用于订阅
    fn get_subscription_addresses(&self) -> Vec<String> {
        self.pools.keys().cloned().collect()
    }

    /// 根据地址获取池子信息
    fn get_pool_info(&self, address: &str) -> Option<&PoolInfo> {
        self.pools.get(address)
    }
}

#[tokio::main]
async fn main() {
    let _guard = init_logging();
    info!("🚀🚀🚀 Starting application");

    // 初始化池子配置
    let pool_config = Arc::new(PoolConfig::new());
    info!("Initialized pool configuration with {} pools", pool_config.pools.len());

    // 注册所有解析器
    setup_parsers().await;

    // 初始化账户解析器注册表（避免重复创建）
    let account_parser = Arc::new(setup_account_parsers().await);
    info!("Initialized account parsers");

    // 创建事件通道
    let (system_event_tx, system_event_rx) = create_event_channel();

    // 初始化 DexPoolManager 和套利检测器
    let pool_manager = Arc::new(DexPoolManager::new());
    let arbitrage_engine = Arc::new(BellmanFordArbitrageEngine::new().with_pool_manager(pool_manager.clone()));

    info!("Initialized DexPoolManager and ArbitrageEngine");

    // 启动事件协调器
    let _coordinator_handle = start_event_coordinator(
        pool_manager.clone(),
        arbitrage_engine.clone(),
        system_event_tx.clone(),
        system_event_rx,
    ).await;

    info!("Started event coordinator");

    // 配置并启动链监听器
    let config = ChainListenerConfig::default();
    let client = YellowstoneGrpcClient::new(config);

    // 从配置生成订阅地址
    let mut subscription_addresses = pool_config.get_subscription_addresses();
    for token_mint in pool_config.token_mints.keys() {
        if !subscription_addresses.contains(token_mint) {
            subscription_addresses.push(token_mint.clone());
        }
    }
    info!("Subscribing to {} pool addresses", subscription_addresses.len());

    let subscription_config = SubscriptionConfig {
        accounts: Some(subscription_addresses),
        vote: false,
        failed: false,
        ..Default::default()
    };

    let system_event_tx_for_handler = system_event_tx.clone();
    let pool_config_for_handler = pool_config.clone();
    let account_parser_for_handler = account_parser.clone();

    // 简化处理逻辑：将池管理和套利检测作为独立任务
    let data_handler: DataHandler = Arc::new(move |data: DataType| -> Result<(), chain_listener::ChainListenerError> {
        match data {
            DataType::Transaction(tx) => {
                // 解析交易数据
                let slot = tx.slot;
                let signature = tx.signature.to_string();
                let bot_wallet = None;

                let signature_clone = signature.clone();
                let bot_wallet_clone = bot_wallet.clone();

                tokio::spawn(async move {
                    let parsers = global_registry().get_all_parsers();
                    for parse in parsers {
                        let tx_clone = tx.tx.clone();
                        let res = parse.parse_transaction(
                            tx_clone,
                            &signature_clone,
                            Some(slot),
                            None,
                            bot_wallet_clone,
                        ).await.unwrap_or_else(|_e| vec![]);
                        println!("res: {:?}", res);
                    }
                });
            }
            DataType::Account(acc) => {
                let account_parser = account_parser_for_handler.clone();
                let event_sender = system_event_tx_for_handler.clone();
                let pool_config = pool_config_for_handler.clone();
                tokio::spawn(async move {
                    let account_address = acc.pubkey.to_string();

                    // 检查是否是 token vault 账户
                    if let Some(info) = pool_config.token_mints.get(&account_address) {
                        let mut new_info = info.clone();

                        // 使用 SPL Token 解析器智能提取 token 余额
                        // 对于 WSOL 使用 lamports，对于其他 SPL token 从账户数据解析
                        let spl_parser = SplTokenAccountParser::new();

                        match spl_parser.extract_token_balance(&acc.data, acc.lamports, info) {
                            Ok(token_balance) => {
                                new_info.token_balance = token_balance as u128;

                                let is_wsol = info.mint.to_string() == "So11111111111111111111111111111111111111112";
                                let balance_type = if is_wsol { "lamports (WSOL)" } else { "token amount" };

                                println!(
                                    "Received token vault update for {}: {} = {}, lamports = {}",
                                    account_address, balance_type, token_balance, acc.lamports
                                );

                                let pool_address = pool_config.vault_to_pool.get(&account_address);
                                if let Some(pool_address) = pool_address {
                                    publish_pool_token_mint_update_event(&event_sender, new_info, pool_address.to_string());
                                } else {
                                    println!("No pool found for token vault: {}", account_address);
                                }
                            }
                            Err(e) => {
                                println!(
                                    "Error parsing token balance for {}: {:?}. Using lamports as fallback: {}",
                                    account_address, e, acc.lamports
                                );
                                // 作为后备方案，如果解析失败，使用 lamports
                                new_info.token_balance = acc.lamports as u128;

                                let pool_address = pool_config.vault_to_pool.get(&account_address);
                                if let Some(pool_address) = pool_address {
                                    publish_pool_token_mint_update_event(&event_sender, new_info, pool_address.to_string());
                                }
                            }
                        }

                        return;
                    }

                    // 查找池子配置
                    if let Some(pool_info) = pool_config.get_pool_info(&account_address) {
                        let parsers = account_parser.find_parsers_for_account_type(&pool_info.account_type);

                        for parser in parsers {
                            let pool_address: Pubkey = acc.pubkey.parse().unwrap();
                            let program_id: Pubkey = pool_info.program_id.parse().unwrap();
                            let res = parser.parse_account(pool_address, program_id, &acc.data).await;
                            match res {
                                Ok(parsed_data) => {
                                    publish_pool_update_event(&event_sender, pool_address, parsed_data);
                                }
                                Err(e) => {
                                    println!("Error parsing {} PoolState: {:?}", pool_info.protocol_name, e);
                                }
                            }
                        }
                    } else {
                        // 未配置的账户地址，忽略处理
                        println!("Received account update for unconfigured address: {}", account_address);
                    }
                });
            }
            DataType::Block(_) => {}
            DataType::Slot(_) => {}
        }
        Ok(())
    });

    let res = client.subscribe_with_monitoring(subscription_config, &data_handler).await;
    if let Err(e) = res {
        println!("Error establishing subscription: {:?}", e);
        return;
    }
    info!("Subscription established successfully");
}


async fn setup_parsers() {
    let raydium_parser = Arc::new(RaydiumClmmEventParser::new());
    register_global_parser(raydium_parser).expect("Failed to register Raydium parser");
}

/// 设置账户解析器注册表
async fn setup_account_parsers() -> AccountParserRegistry {
    let account_parser = AccountParserRegistry::new();

    // 注册 Meteora 解析器
    let meteora_parser = Arc::new(MeteoraAnchorAccountParser::new());
    account_parser.register_parser(meteora_parser)
        .expect("Failed to register Meteora parser");

    // 注册 Raydium 解析器
    let raydium_parser = Arc::new(RaydiumAnchorAccountParser::new());
    account_parser.register_parser(raydium_parser)
        .expect("Failed to register Raydium parser");

    let pump_swap_parser = Arc::new(PumpSwapAnchorAccountParser::new());
    account_parser.register_parser(pump_swap_parser)
        .expect("Failed to register Pump Swap parser");

    let raydium_cpmm_parser = Arc::new(RaydiumCpmmAccountParser::new());
    account_parser.register_parser(raydium_cpmm_parser)
        .expect("Failed to register Raydium CPMM parser");


    let meteora_damm_parser = Arc::new(MeteoraDammAnchorAccountParser::new());
    account_parser.register_parser(meteora_damm_parser)
        .expect("Failed to register Meteora DAMM parser");

    info!("Successfully registered account parsers");
    account_parser
}
