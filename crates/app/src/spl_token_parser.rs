//! SPL Token 账户数据解析器
//!
//! 用于解析 SPL Token 账户的数据结构，提取实际的 token 余额

use shared::{EchoesError, Result, TokenMintInfo};
use solana_sdk::pubkey::Pubkey;

/// WSOL (Wrapped SOL) 的 mint 地址
pub const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";

/// SPL Token 账户数据结构
#[derive(Debug, Clone)]
pub struct SplTokenAccount {
    /// 代币 mint 地址
    pub mint: Pubkey,
    /// 账户所有者
    pub owner: Pubkey,
    /// 代币余额
    pub amount: u64,
    /// 委托者（可选）
    pub delegate: Option<Pubkey>,
    /// 账户状态
    pub state: u8,
    /// 是否为原生代币
    pub is_native: Option<u64>,
    /// 委托金额
    pub delegated_amount: u64,
    /// 关闭权限（可选）
    pub close_authority: Option<Pubkey>,
}

/// SPL Token 账户解析器
pub struct SplTokenAccountParser;

impl SplTokenAccountParser {
    /// 创建新的解析器实例
    pub fn new() -> Self {
        Self
    }

    /// 解析 SPL Token 账户数据
    /// 
    /// SPL Token 账户数据布局：
    /// - mint: 32 bytes
    /// - owner: 32 bytes  
    /// - amount: 8 bytes
    /// - delegate: 4 + 32 bytes (Option<Pubkey>)
    /// - state: 1 byte
    /// - is_native: 4 + 8 bytes (Option<u64>)
    /// - delegated_amount: 8 bytes
    /// - close_authority: 4 + 32 bytes (Option<Pubkey>)
    pub fn parse_spl_token_account(&self, data: &[u8]) -> Result<SplTokenAccount> {
        if data.len() < 165 {
            return Err(EchoesError::InvalidInput(
                "SPL Token account data too short".to_string()
            ));
        }

        let mut offset = 0;

        // 解析 mint (32 bytes)
        let mint = Pubkey::try_from(&data[offset..offset + 32])
            .map_err(|_| EchoesError::InvalidInput("Invalid mint pubkey".to_string()))?;
        offset += 32;

        // 解析 owner (32 bytes)
        let owner = Pubkey::try_from(&data[offset..offset + 32])
            .map_err(|_| EchoesError::InvalidInput("Invalid owner pubkey".to_string()))?;
        offset += 32;

        // 解析 amount (8 bytes, little-endian)
        let amount = u64::from_le_bytes([
            data[offset], data[offset + 1], data[offset + 2], data[offset + 3],
            data[offset + 4], data[offset + 5], data[offset + 6], data[offset + 7],
        ]);
        offset += 8;

        // 解析 delegate (4 + 32 bytes)
        let delegate_option = u32::from_le_bytes([
            data[offset], data[offset + 1], data[offset + 2], data[offset + 3],
        ]);
        offset += 4;

        let delegate = if delegate_option == 1 {
            let delegate_pubkey = Pubkey::try_from(&data[offset..offset + 32])
                .map_err(|_| EchoesError::InvalidInput("Invalid delegate pubkey".to_string()))?;
            offset += 32;
            Some(delegate_pubkey)
        } else {
            offset += 32; // 跳过未使用的 32 字节
            None
        };

        // 解析 state (1 byte)
        let state = data[offset];
        offset += 1;

        // 解析 is_native (4 + 8 bytes)
        let is_native_option = u32::from_le_bytes([
            data[offset], data[offset + 1], data[offset + 2], data[offset + 3],
        ]);
        offset += 4;

        let is_native = if is_native_option == 1 {
            let native_amount = u64::from_le_bytes([
                data[offset], data[offset + 1], data[offset + 2], data[offset + 3],
                data[offset + 4], data[offset + 5], data[offset + 6], data[offset + 7],
            ]);
            offset += 8;
            Some(native_amount)
        } else {
            offset += 8; // 跳过未使用的 8 字节
            None
        };

        // 解析 delegated_amount (8 bytes)
        let delegated_amount = u64::from_le_bytes([
            data[offset], data[offset + 1], data[offset + 2], data[offset + 3],
            data[offset + 4], data[offset + 5], data[offset + 6], data[offset + 7],
        ]);
        offset += 8;

        // 解析 close_authority (4 + 32 bytes)
        let close_authority_option = u32::from_le_bytes([
            data[offset], data[offset + 1], data[offset + 2], data[offset + 3],
        ]);
        offset += 4;

        let close_authority = if close_authority_option == 1 {
            let close_authority_pubkey = Pubkey::try_from(&data[offset..offset + 32])
                .map_err(|_| EchoesError::InvalidInput("Invalid close authority pubkey".to_string()))?;
            Some(close_authority_pubkey)
        } else {
            None
        };

        Ok(SplTokenAccount {
            mint,
            owner,
            amount,
            delegate,
            state,
            is_native,
            delegated_amount,
            close_authority,
        })
    }

    /// 检查账户数据是否为有效的 SPL Token 账户
    pub fn is_spl_token_account(&self, data: &[u8]) -> bool {
        // SPL Token 账户数据长度应该是 165 字节
        data.len() == 165
    }

    /// 快速提取 token 余额，不进行完整解析
    pub fn extract_token_amount(&self, data: &[u8]) -> Result<u64> {
        if data.len() < 72 {
            return Err(EchoesError::InvalidInput(
                "Data too short to contain token amount".to_string()
            ));
        }

        // amount 位于偏移量 64 的位置（mint: 32 + owner: 32）
        let amount = u64::from_le_bytes([
            data[64], data[65], data[66], data[67],
            data[68], data[69], data[70], data[71],
        ]);

        Ok(amount)
    }

    /// 智能提取 token 余额，根据 mint 类型选择合适的方法
    /// 对于 WSOL，使用 lamports；对于其他 SPL token，从账户数据解析
    pub fn extract_token_balance(&self, data: &[u8], lamports: u64, token_mint: &TokenMintInfo) -> Result<u64> {
        // 检查是否为 WSOL
        if token_mint.mint.to_string() == WSOL_MINT {
            // 对于 WSOL，余额就是 lamports
            Ok(lamports)
        } else {
            // 对于其他 SPL token，从账户数据中解析
            self.extract_token_amount(data)
        }
    }

    /// 检查指定的 mint 是否为 WSOL
    pub fn is_wsol_mint(mint: &Pubkey) -> bool {
        mint.to_string() == WSOL_MINT
    }
}

impl Default for SplTokenAccountParser {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_token_amount() {
        let parser = SplTokenAccountParser::new();
        
        // 创建模拟的 SPL Token 账户数据
        let mut data = vec![0u8; 165];
        
        // 设置 amount 字段（位于偏移量 64-71）
        let test_amount = 1000000u64;
        let amount_bytes = test_amount.to_le_bytes();
        data[64..72].copy_from_slice(&amount_bytes);
        
        let result = parser.extract_token_amount(&data);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), test_amount);
    }

    #[test]
    fn test_is_spl_token_account() {
        let parser = SplTokenAccountParser::new();
        
        // 正确长度的数据
        let valid_data = vec![0u8; 165];
        assert!(parser.is_spl_token_account(&valid_data));
        
        // 错误长度的数据
        let invalid_data = vec![0u8; 100];
        assert!(!parser.is_spl_token_account(&invalid_data));
    }

    #[test]
    fn test_wsol_balance_extraction() {
        use solana_sdk::pubkey;
        
        let parser = SplTokenAccountParser::new();
        
        // 创建 WSOL token mint info
        let wsol_mint = pubkey!("So11111111111111111111111111111111111111112");
        let wsol_info = TokenMintInfo::new(
            wsol_mint,
            9,
            pubkey!("11111111111111111111111111111112"), // 模拟 vault (使用有效地址)
            0
        );
        
        // 创建非 WSOL token mint info
        let other_mint = pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"); // USDC
        let other_info = TokenMintInfo::new(
            other_mint,
            6,
            pubkey!("11111111111111111111111111111113"), // 模拟 vault (使用有效地址)
            0
        );
        
        let data = vec![0u8; 165];
        let lamports = **********; // 5 SOL
        
        // 测试 WSOL：应该返回 lamports
        let wsol_balance = parser.extract_token_balance(&data, lamports, &wsol_info).unwrap();
        assert_eq!(wsol_balance, lamports);
        
        // 测试其他 token：应该从账户数据解析（在这个测试中为 0）
        let other_balance = parser.extract_token_balance(&data, lamports, &other_info).unwrap();
        assert_eq!(other_balance, 0); // 因为测试数据中 amount 字段为 0
    }

    #[test]
    fn test_is_wsol_mint() {
        use solana_sdk::pubkey;
        
        let wsol_mint = pubkey!("So11111111111111111111111111111111111111112");
        let usdc_mint = pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
        
        assert!(SplTokenAccountParser::is_wsol_mint(&wsol_mint));
        assert!(!SplTokenAccountParser::is_wsol_mint(&usdc_mint));
    }
}