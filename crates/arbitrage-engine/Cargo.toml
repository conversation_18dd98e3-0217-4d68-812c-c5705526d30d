[package]
name = "arbitrage-engine"
version.workspace = true
edition.workspace = true

[dependencies]
shared = { path = "../shared" }
state-manager = { path = "../state-manager" }
dex-instructions = { path = "../dex-instructions" }
solana-sdk.workspace = true
thiserror.workspace = true
tokio.workspace = true
tracing.workspace = true
serde.workspace = true
borsh.workspace = true
spl-token.workspace = true
spl-associated-token-account.workspace = true
spl-memo.workspace = true

[dev-dependencies]
dex-instructions = { path = "../dex-instructions" }
