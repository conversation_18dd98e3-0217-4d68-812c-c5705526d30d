//! 动态规划套利引擎使用示例
//! 
//! 展示如何使用 DpArbitrageEngine 进行套利机会检测

use arbitrage_engine::{DpArbitrageEngine, tokens};
use std::time::Instant;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {

    println!("🚀 动态规划套利引擎示例");
    println!("=======================");
    
    // 创建动态规划套利引擎
    let dp_engine = DpArbitrageEngine::new()
        .with_max_depth(4)
        .with_min_liquidity(1000);

    // 设置缓存生存时间为15秒
    dp_engine.set_cache_ttl(15000).await;

    println!("✅ 动态规划引擎已初始化");
    
    // 获取引擎统计信息
    let stats = dp_engine.get_stats().await?;
    println!("📊 引擎统计:");
    println!("   - 代币数量: {}", stats.total_tokens);
    println!("   - 边数量: {}", stats.total_edges);
    println!("   - 最大深度: {}", stats.max_depth);
    println!("   - 最小流动性: {}", stats.min_liquidity);
    println!("   - 缓存大小: {}", stats.cache_size);
    println!("   - 缓存状态: {}", if stats.is_cache_expired { "已过期" } else { "有效" });

    // 注意：由于没有真实的池管理器，这里只是演示API使用
    // 在实际使用中，你需要先设置池管理器：
    // let dp_engine = dp_engine.with_pool_manager(pool_manager);

    println!("\n🔍 检测套利机会 (模拟):");
    
    // 测试不同的套利检测方法
    let start = Instant::now();
    
    // 方法1: 从 SOL 开始检测
    let sol_opportunities = dp_engine.detect_arbitrage_from_sol().await?;
    println!("   📈 从 SOL 开始找到 {} 个套利机会", sol_opportunities.len());

    // 方法2: 从 USDC 开始检测  
    let usdc_opportunities = dp_engine.detect_arbitrage_from_usdc().await?;
    println!("   💵 从 USDC 开始找到 {} 个套利机会", usdc_opportunities.len());

    // 方法3: 从 SOL 和 USDC 同时开始检测
    let combined_opportunities = dp_engine.detect_arbitrage_from_sol_usdc().await?;
    println!("   🔄 从 SOL+USDC 开始找到 {} 个套利机会", combined_opportunities.len());

    // 方法4: 从指定代币地址检测
    let token_opportunities = dp_engine.detect_arbitrage_from_token_str(tokens::SOL).await?;
    println!("   🎯 从指定代币找到 {} 个套利机会", token_opportunities.len());

    // 方法5: 获取顶部机会
    let top_opportunities = dp_engine.get_top_opportunities(5).await?;
    println!("   🏆 前5个最佳机会: {} 个", top_opportunities.len());

    let elapsed = start.elapsed();
    println!("   ⏱️  总耗时: {:?}", elapsed);

    // 获取缓存统计
    let (cache_size, is_expired) = dp_engine.get_cache_stats().await;
    println!("\n📊 缓存性能:");
    println!("   - 缓存条目: {}", cache_size);
    println!("   - 缓存状态: {}", if is_expired { "已过期" } else { "有效" });

    // 演示缓存清理
    println!("\n🧹 清理缓存...");
    dp_engine.clear_cache().await;
    let (cache_size_after, _) = dp_engine.get_cache_stats().await;
    println!("   - 清理后缓存条目: {}", cache_size_after);

    // 展示利润估算功能
    if let Some(path) = combined_opportunities.first() {
        let input_amount = 1_000_000; // 1 SOL (in lamports)
        let estimated_profit = dp_engine.estimate_profit(path, input_amount)?;
        println!("\n💰 利润估算 (示例路径):");
        println!("   - 投入金额: {} lamports", input_amount);
        println!("   - 预期利润: {} lamports", estimated_profit);
        println!("   - 净利润率: {:.4}%", path.net_profit_ratio() * 100.0);
        println!("   - 路径步骤数: {}", path.steps.len());
    }

    println!("\n✨ 动态规划引擎示例完成!");
    println!("\n🎯 主要优势:");
    println!("   ✅ 使用HashMap记忆化缓存避免重复计算");
    println!("   ✅ 动态规划优化搜索性能");
    println!("   ✅ 自动缓存失效机制");
    println!("   ✅ 与原ArbitrageEngine API完全兼容");
    println!("   ✅ 支持缓存统计和手动清理");

    Ok(())
}