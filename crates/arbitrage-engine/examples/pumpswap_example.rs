//! PumpSwap 交易指令构建示例
//!
//! 展示如何使用套利引擎构建 PumpSwap 交易指令

use arbitrage_engine::{
    ArbitrageEngine,
    tokens,
    PumpSwapInstructionBuilder, SwapInstructionBuilder,
};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 PumpSwap 交易指令构建示例");

    // 创建套利引擎
    let engine = ArbitrageEngine::new()
        .with_max_depth(3)
        .with_min_liquidity(1000);

    // 示例地址（使用有效的base58格式）
    let user = Pubkey::from_str("11111111111111111111111111111112")?;
    let token_mint = Pubkey::from_str("So11111111111111111111111111111111111111112")?;
    let pool_address = Pubkey::from_str("So11111111111111111111111111111111111111113")?;
    
    println!("用户地址: {}", user);
    println!("代币地址: {}", token_mint);
    println!("池子地址: {}", pool_address);

    // 构建买入指令示例
    println!("\n📈 构建买入指令 (SOL -> Token)");
    let buy_instruction = PumpSwapInstructionBuilder::build_buy_instruction(
        user,
        token_mint,
        pool_address,
        1_000_000_000, // 1 SOL (lamports)
        1_000_000,     // 1 个代币 (假设6位小数)
        100,           // 1% 滑点
    );

    match buy_instruction {
        Ok(instruction) => {
            println!("✅ 买入指令构建成功");
            println!("程序ID: {}", instruction.program_id);
            println!("账户数量: {}", instruction.accounts.len());
            println!("指令数据长度: {} bytes", instruction.data.len());
        }
        Err(e) => {
            println!("❌ 买入指令构建失败: {}", e);
        }
    }

    // 构建卖出指令示例
    println!("\n📉 构建卖出指令 (Token -> SOL)");
    let sell_instruction = PumpSwapInstructionBuilder::build_sell_instruction(
        user,
        token_mint,
        pool_address,
        1_000_000,     // 1 个代币
        900_000_000,   // 0.9 SOL (考虑滑点)
        100,           // 1% 滑点
    );

    match sell_instruction {
        Ok(instruction) => {
            println!("✅ 卖出指令构建成功");
            println!("程序ID: {}", instruction.program_id);
            println!("账户数量: {}", instruction.accounts.len());
            println!("指令数据长度: {} bytes", instruction.data.len());
        }
        Err(e) => {
            println!("❌ 卖出指令构建失败: {}", e);
        }
    }

    // 显示常用代币地址
    println!("\n💰 常用代币地址:");
    println!("SOL: {}", tokens::sol());
    println!("USDC: {}", tokens::usdc());

    // 获取引擎统计信息
    let stats = engine.get_stats().await?;
    println!("\n📊 引擎统计信息:");
    println!("总代币数: {}", stats.total_tokens);
    println!("总边数: {}", stats.total_edges);
    println!("最大深度: {}", stats.max_depth);
    println!("最小流动性: {}", stats.min_liquidity);
    println!("池管理器: {}", if stats.has_pool_manager { "已配置" } else { "未配置" });

    println!("\n🎉 示例完成!");
    Ok(())
}