//! 基于 Bellman-Ford 算法的套利引擎
//!
//! 使用负环检测算法进行套利机会发现

use shared::{NormalizedPoolPrice, ArbitrageResult, ArbitrageError};
use state_manager::core::DexPoolManager;
use solana_sdk::{pubkey::Pubkey, instruction::Instruction};
use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::Arc;
use crate::engine::{ArbitragePath, SwapStep, EngineStats};

const MAX_ARBITRAGE_STEPS: usize = 4;
const MIN_PROFIT_FACTOR: f64 = 1.0001; // 0.01% 最小利润
const FLOATING_POINT_EPSILON: f64 = 1e-12;

/// 交易方向
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum TradeDirection {
    T0toT1,
    T1toT0,
}

/// 获取从 Token0 到 Token1 的汇率
fn rate_t0_to_t1(pool: &NormalizedPoolPrice) -> Option<f64> {
    if pool.normalized_price <= 0.0 || !pool.normalized_price.is_finite() {
        None
    } else {
        Some(pool.normalized_price)
    }
}

/// 获取从 Token1 到 Token0 的汇率
fn rate_t1_to_t0(pool: &NormalizedPoolPrice) -> Option<f64> {
    if pool.normalized_price <= 0.0 || !pool.normalized_price.is_finite() {
        None
    } else {
        Some(1.0 / pool.normalized_price)
    }
}

/// 图的边结构
#[derive(Debug, Clone)]
struct Edge {
    from_node_idx: usize,
    to_node_idx: usize,
    weight: f64,
    pool_info: Arc<NormalizedPoolPrice>,
    direction: TradeDirection,
}

/// 基于 Bellman-Ford 算法的套利引擎
pub struct BellmanFordArbitrageEngine {
    pool_manager: Option<Arc<DexPoolManager>>,
    max_depth: usize,
    min_liquidity: u64,
    min_profit_threshold: f64,
}

impl BellmanFordArbitrageEngine {
    /// 创建新的套利引擎
    pub fn new() -> Self {
        Self {
            pool_manager: None,
            max_depth: MAX_ARBITRAGE_STEPS,
            min_liquidity: 0,
            min_profit_threshold: MIN_PROFIT_FACTOR,
        }
    }

    /// 设置池管理器
    pub fn with_pool_manager(mut self, pool_manager: Arc<DexPoolManager>) -> Self {
        self.pool_manager = Some(pool_manager);
        self
    }

    /// 设置最大搜索深度
    pub fn with_max_depth(mut self, max_depth: usize) -> Self {
        self.max_depth = max_depth;
        self
    }

    /// 设置最小流动性阈值
    pub fn with_min_liquidity(mut self, min_liquidity: u64) -> Self {
        self.min_liquidity = min_liquidity;
        self
    }

    /// 检测套利机会
    pub async fn detect_arbitrage_opportunities(&self, start_tokens: Vec<Pubkey>) -> ArbitrageResult<Vec<ArbitragePath>> {
        let pool_manager = self.pool_manager.as_ref()
            .ok_or_else(|| ArbitrageError::Config("No pool manager configured".to_string()))?;

        let normalized_prices = pool_manager.get_all_normalized_pool_prices();
        let pools = self.filter_pools(normalized_prices);

        if pools.is_empty() {
            return Ok(Vec::new());
        }

        let start_token_set: HashSet<Pubkey> = start_tokens.into_iter().collect();
        self.find_arbitrage_opportunities(&pools, &start_token_set).await
    }

    /// 检测从多个代币地址字符串开始的套利机会
    pub async fn detect_arbitrage_from_tokens_str(&self, tokens: &[&str]) -> ArbitrageResult<Vec<ArbitragePath>> {
        let mut start_tokens = Vec::new();
        for token_address in tokens {
            let token = Pubkey::try_from(*token_address)
                .map_err(|e| ArbitrageError::Config(format!("Invalid token address '{}': {}", token_address, e)))?;
            start_tokens.push(token);
        }
        self.detect_arbitrage_opportunities(start_tokens).await
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> ArbitrageResult<EngineStats> {
        let pool_manager = self.pool_manager.as_ref()
            .ok_or_else(|| ArbitrageError::Config("No pool manager configured".to_string()))?;

        let normalized_prices = pool_manager.get_all_normalized_pool_prices();
        let pools = self.filter_pools(normalized_prices);

        let (token_to_idx, _, edges) = self.build_graph(&pools)?;

        Ok(EngineStats {
            total_tokens: token_to_idx.len(),
            total_edges: edges.len(),
            max_depth: self.max_depth,
            min_liquidity: self.min_liquidity,
            has_pool_manager: self.pool_manager.is_some(),
        })
    }

    /// 构建套利执行指令
    pub fn build_arbitrage_instructions(
        &self,
        path: &ArbitragePath,
        user: Pubkey,
        input_amount: u64,
        slippage_bps: u64,
    ) -> ArbitrageResult<Vec<Instruction>> {
        if !path.is_profitable() {
            return Err(ArbitrageError::Config(
                "Cannot execute unprofitable arbitrage path".to_string()
            ));
        }

        path.build_swap_instructions(user, input_amount, slippage_bps)
    }

    /// 估算套利路径的预期利润
    pub fn estimate_profit(&self, path: &ArbitragePath, input_amount: u64) -> ArbitrageResult<u64> {
        if !path.is_profitable() {
            return Ok(0);
        }

        let gross_profit = (input_amount as f64 * path.net_profit_ratio()) as u64;
        let gas_cost = path.estimated_gas_cost;

        Ok(gross_profit.saturating_sub(gas_cost))
    }

    /// 估算最佳输入金额
    pub fn estimate_optimal_input_amount(&self, path: &ArbitragePath) -> u64 {
        let min_liquidity = path.steps.iter()
            .map(|step| step.liquidity)
            .min()
            .unwrap_or(0);

        (min_liquidity / 10).max(1000)
    }

    /// 过滤池数据
    fn filter_pools(&self, normalized_prices: Vec<NormalizedPoolPrice>) -> Vec<Arc<NormalizedPoolPrice>> {
        normalized_prices.into_iter()
            .filter(|pool| (pool.liquidity as u64) >= self.min_liquidity)
            .map(Arc::new)
            .collect()
    }

    /// 主要的套利发现函数
    async fn find_arbitrage_opportunities(&self, pools: &[Arc<NormalizedPoolPrice>], start_tokens: &HashSet<Pubkey>) -> ArbitrageResult<Vec<ArbitragePath>> {
        if pools.is_empty() || start_tokens.is_empty() {
            return Ok(Vec::new());
        }

        // 构建图
        let (token_to_idx, idx_to_token, edges) = self.build_graph(pools)?;
        let num_tokens = idx_to_token.len();

        if num_tokens < 2 || edges.is_empty() {
            return Ok(Vec::new());
        }

        // 查找起始代币在图中的索引
        let start_token_indices: HashSet<usize> = start_tokens
            .iter()
            .filter_map(|token_pk| token_to_idx.get(token_pk).copied())
            .collect();

        if start_token_indices.is_empty() {
            return Ok(Vec::new());
        }

        // Bellman-Ford 算法检测负环
        let mut dist: Vec<f64> = vec![0.0; num_tokens];
        let mut predecessor: Vec<Option<(usize, Arc<Edge>)>> = vec![None; num_tokens];

        // 执行 V 次松弛操作
        for _i in 1..=num_tokens {
            for edge in &edges {
                let new_dist = dist[edge.from_node_idx] + edge.weight;
                if new_dist < dist[edge.to_node_idx] - FLOATING_POINT_EPSILON {
                    dist[edge.to_node_idx] = new_dist;
                    predecessor[edge.to_node_idx] = Some((edge.from_node_idx, Arc::new(edge.clone())));
                }
            }
        }

        // 识别负环中的节点
        let mut nodes_in_neg_cycle = HashSet::new();
        for edge in &edges {
            let new_dist = dist[edge.from_node_idx] + edge.weight;
            if new_dist < dist[edge.to_node_idx] - FLOATING_POINT_EPSILON {
                nodes_in_neg_cycle.insert(edge.to_node_idx);
                nodes_in_neg_cycle.insert(edge.from_node_idx);
            }
        }

        if nodes_in_neg_cycle.is_empty() {
            return Ok(Vec::new());
        }

        // 重构环路
        let potential_start_nodes: HashSet<usize> = nodes_in_neg_cycle
            .intersection(&start_token_indices)
            .copied()
            .collect();

        if potential_start_nodes.is_empty() {
            return Ok(Vec::new());
        }

        let mut arbitrage_paths = Vec::new();
        let mut nodes_in_reported_cycles = HashSet::new();

        for &start_node_idx in &potential_start_nodes {
            if nodes_in_reported_cycles.contains(&start_node_idx) {
                continue;
            }

            if let Ok(cycle_edges) = self.reconstruct_cycle_robust(start_node_idx, &predecessor, num_tokens) {
                if cycle_edges.is_empty() || cycle_edges.len() < 2 || cycle_edges.len() > self.max_depth {
                    continue;
                }

                // 验证环路起点终点
                let cycle_start_token_idx = cycle_edges.first().map(|e| e.from_node_idx);
                let cycle_end_token_idx = cycle_edges.last().map(|e| e.to_node_idx);

                if cycle_start_token_idx != Some(start_node_idx) || cycle_end_token_idx != Some(start_node_idx) {
                    continue;
                }

                // 转换为套利路径
                let mut steps = Vec::new();
                let mut total_log_weight = 0.0;
                let mut path_valid = true;

                for edge_arc in &cycle_edges {
                    total_log_weight += edge_arc.weight;
                    let from_token = idx_to_token[edge_arc.from_node_idx];
                    let to_token = idx_to_token[edge_arc.to_node_idx];

                    let rate = match edge_arc.direction {
                        TradeDirection::T0toT1 => rate_t0_to_t1(&edge_arc.pool_info),
                        TradeDirection::T1toT0 => rate_t1_to_t0(&edge_arc.pool_info),
                    };

                    if let Some(r) = rate {
                        if r <= 0.0 || !r.is_finite() {
                            path_valid = false;
                            break;
                        }

                        steps.push(SwapStep {
                            pool_address: edge_arc.pool_info.pool_address,
                            from_token,
                            to_token,
                            rate: r,
                            protocol: edge_arc.pool_info.protocol,
                            pool_type: edge_arc.pool_info.pool_type,
                            liquidity: edge_arc.pool_info.liquidity as u64,
                        });
                    } else {
                        path_valid = false;
                        break;
                    }
                }

                if path_valid {
                    let profit_factor = (-total_log_weight).exp();

                    if profit_factor > self.min_profit_threshold {
                        let start_token = idx_to_token[start_node_idx];
                        let path = ArbitragePath {
                            steps,
                            start_token,
                            expected_profit_ratio: profit_factor - 1.0,
                            estimated_gas_cost: 100000,
                        };

                        arbitrage_paths.push(path);

                        // 标记已报告的节点
                        for edge in &cycle_edges {
                            nodes_in_reported_cycles.insert(edge.from_node_idx);
                            nodes_in_reported_cycles.insert(edge.to_node_idx);
                        }
                    }
                }
            }
        }

        // 按利润率排序
        arbitrage_paths.sort_by(|a, b| b.expected_profit_ratio.partial_cmp(&a.expected_profit_ratio).unwrap());

        Ok(arbitrage_paths)
    }

    /// 构建图表示
    fn build_graph(&self, pools: &[Arc<NormalizedPoolPrice>]) -> Result<(HashMap<Pubkey, usize>, Vec<Pubkey>, Vec<Edge>), ArbitrageError> {
        let mut token_to_idx: HashMap<Pubkey, usize> = HashMap::new();
        let mut idx_to_token: Vec<Pubkey> = Vec::new();
        let mut edges: Vec<Edge> = Vec::new();
        let mut token_counter = 0;

        let mut get_token_idx = |token: Pubkey| -> usize {
            *token_to_idx.entry(token).or_insert_with(|| {
                let idx = token_counter;
                idx_to_token.push(token);
                token_counter += 1;
                idx
            })
        };

        for pool_arc in pools {
            let pool = pool_arc.as_ref();

            if pool.normalized_price <= 0.0 || !pool.normalized_price.is_finite() {
                continue;
            }

            let idx0 = get_token_idx(pool.token_pair.token_a);
            let idx1 = get_token_idx(pool.token_pair.token_b);

            // Token0 -> Token1
            if let Some(rate) = rate_t0_to_t1(pool) {
                if rate > FLOATING_POINT_EPSILON {
                    let weight = -rate.ln();
                    if weight.is_finite() {
                        edges.push(Edge {
                            from_node_idx: idx0,
                            to_node_idx: idx1,
                            weight,
                            pool_info: Arc::clone(pool_arc),
                            direction: TradeDirection::T0toT1,
                        });
                    }
                }
            }

            // Token1 -> Token0
            if let Some(rate) = rate_t1_to_t0(pool) {
                if rate > FLOATING_POINT_EPSILON {
                    let weight = -rate.ln();
                    if weight.is_finite() {
                        edges.push(Edge {
                            from_node_idx: idx1,
                            to_node_idx: idx0,
                            weight,
                            pool_info: Arc::clone(pool_arc),
                            direction: TradeDirection::T1toT0,
                        });
                    }
                }
            }
        }

        Ok((token_to_idx, idx_to_token, edges))
    }

    /// 重构环路
    fn reconstruct_cycle_robust(
        &self,
        start_node_idx: usize,
        predecessor: &[Option<(usize, Arc<Edge>)>],
        num_nodes: usize,
    ) -> Result<Vec<Arc<Edge>>, String> {
        // 先后退 V 步确保进入环路
        let mut node_in_cycle = start_node_idx;
        for _ in 0..num_nodes {
            match predecessor[node_in_cycle] {
                Some((prev_node, _)) => {
                    node_in_cycle = prev_node;
                }
                None => {
                    return Err(format!("Node {} detected in V-th pass but predecessor trace failed early.", start_node_idx));
                }
            }
        }

        // 从环路内部开始重构
        let mut cycle_edges = VecDeque::new();
        let mut visited_in_trace = HashSet::new();
        let mut current_trace_node = node_in_cycle;

        for _ in 0..=num_nodes {
            if !visited_in_trace.insert(current_trace_node) {
                if current_trace_node == node_in_cycle {
                    break;
                } else {
                    break;
                }
            }

            match predecessor[current_trace_node] {
                Some((prev_node, ref edge_arc)) => {
                    cycle_edges.push_front(Arc::clone(edge_arc));
                    current_trace_node = prev_node;
                }
                None => {
                    return Err(format!(
                        "Cycle reconstruction failed: Predecessor missing for node {} during cycle trace.",
                        current_trace_node
                    ));
                }
            }
        }

        let final_cycle_edges: Vec<Arc<Edge>> = cycle_edges.into();

        if final_cycle_edges.is_empty() {
            return Ok(Vec::new());
        }

        // 验证环路连接性
        if let (Some(first_edge), Some(last_edge)) = (final_cycle_edges.first(), final_cycle_edges.last()) {
            if last_edge.to_node_idx != first_edge.from_node_idx {
                return Ok(Vec::new());
            }
        }

        Ok(final_cycle_edges)
    }
}
