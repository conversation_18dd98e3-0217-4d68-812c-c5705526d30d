//! 基于动态规划的套利引擎优化版本
//!
//! 使用记忆化搜索和动态规划算法优化套利路径搜索性能

use shared::{ArbitrageResult, ArbitrageError, DexProtocol};
use state_manager::core::DexPoolManager;
use solana_sdk::pubkey::Pubkey;
use std::collections::{HashMap, HashSet};
use std::str::FromStr;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, debug};

// 重用现有的数据结构
use crate::engine::{
    ArbitragePath, SwapStep, TokenEdge, TokenGraph, EngineStats, tokens
};

/// 记忆化缓存键：包含当前代币、起始代币、剩余深度和已访问代币集合
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct MemoKey {
    /// 当前代币
    current_token: Pubkey,
    /// 起始代币
    start_token: Pubkey,
    /// 剩余搜索深度
    remaining_depth: usize,
    /// 已访问的代币集合（按字典序排序以确保唯一性）
    visited_tokens: Vec<Pubkey>,
}

impl MemoKey {
    fn new(
        current_token: Pubkey,
        start_token: Pubkey,
        remaining_depth: usize,
        visited_tokens: &HashSet<Pubkey>,
    ) -> Self {
        let mut visited_vec: Vec<Pubkey> = visited_tokens.iter().cloned().collect();
        visited_vec.sort(); // 确保唯一性

        Self {
            current_token,
            start_token,
            remaining_depth,
            visited_tokens: visited_vec,
        }
    }
}

/// 记忆化缓存值：包含找到的套利路径列表
type MemoValue = Vec<ArbitragePath>;

/// 缓存失效管理器，处理价格更新时的缓存清理
struct CacheManager {
    /// 主记忆化缓存
    memo_cache: HashMap<MemoKey, MemoValue>,
    /// 缓存创建时间戳，用于失效检查
    cache_timestamp: u64,
    /// 缓存失效阈值（毫秒）
    cache_ttl_ms: u64,
}

impl CacheManager {
    fn new(cache_ttl_ms: u64) -> Self {
        Self {
            memo_cache: HashMap::new(),
            cache_timestamp: Self::current_timestamp(),
            cache_ttl_ms,
        }
    }

    /// 获取当前时间戳（毫秒）
    fn current_timestamp() -> u64 {
        std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64
    }

    /// 检查缓存是否需要失效
    fn should_invalidate(&self) -> bool {
        let now = Self::current_timestamp();
        now.saturating_sub(self.cache_timestamp) > self.cache_ttl_ms
    }

    /// 清理失效缓存
    fn clear_if_expired(&mut self) {
        if self.should_invalidate() {
            debug!("清理过期缓存，缓存项数量: {}", self.memo_cache.len());
            self.memo_cache.clear();
            self.cache_timestamp = Self::current_timestamp();
        }
    }

    /// 强制清理所有缓存（价格更新时调用）
    fn force_clear(&mut self) {
        debug!("强制清理所有缓存，缓存项数量: {}", self.memo_cache.len());
        self.memo_cache.clear();
        self.cache_timestamp = Self::current_timestamp();
    }

    /// 获取缓存值
    fn get(&mut self, key: &MemoKey) -> Option<&MemoValue> {
        self.clear_if_expired();
        self.memo_cache.get(key)
    }

    /// 插入缓存值
    fn insert(&mut self, key: MemoKey, value: MemoValue) {
        self.clear_if_expired();
        self.memo_cache.insert(key, value);
    }

    /// 获取缓存统计信息
    fn cache_stats(&self) -> (usize, bool) {
        (self.memo_cache.len(), self.should_invalidate())
    }
}

/// 基于动态规划优化的套利引擎
pub struct DpArbitrageEngine {
    /// 代币交换图
    token_graph: Arc<RwLock<TokenGraph>>,
    /// 池管理器
    pool_manager: Option<Arc<DexPoolManager>>,
    /// 最大搜索深度
    max_depth: usize,
    /// 最小流动性阈值
    min_liquidity: u64,
    /// 记忆化缓存管理器
    cache_manager: Arc<RwLock<CacheManager>>,
}

impl DpArbitrageEngine {
    /// 创建新的动态规划套利引擎
    pub fn new() -> Self {
        Self {
            token_graph: Arc::new(RwLock::new(TokenGraph::new())),
            pool_manager: None,
            max_depth: 4, // 最多4步交换
            min_liquidity: 1000, // 最小流动性
            cache_manager: Arc::new(RwLock::new(CacheManager::new(30000))), // 30秒缓存
        }
    }

    /// 设置池管理器
    pub fn with_pool_manager(mut self, pool_manager: Arc<DexPoolManager>) -> Self {
        self.pool_manager = Some(pool_manager);
        self
    }

    /// 设置最大搜索深度
    pub fn with_max_depth(mut self, max_depth: usize) -> Self {
        self.max_depth = max_depth;
        self
    }

    /// 设置最小流动性阈值
    pub fn with_min_liquidity(mut self, min_liquidity: u64) -> Self {
        self.min_liquidity = min_liquidity;
        self
    }

    /// 设置缓存生存时间（毫秒）
    pub async fn set_cache_ttl(&self, ttl_ms: u64) {
        let mut cache_manager = self.cache_manager.write().await;
        *cache_manager = CacheManager::new(ttl_ms);
    }

    /// 从池管理器构建代币图（与原版本兼容）
    pub async fn build_graph_from_pools(&self) -> ArbitrageResult<usize> {
        let pool_manager = self.pool_manager.as_ref()
            .ok_or_else(|| ArbitrageError::Config("No pool manager configured".to_string()))?;

        let normalized_prices = pool_manager.get_all_normalized_pool_prices();
        let pool_count = normalized_prices.len();
        let mut graph = self.token_graph.write().await;

        debug!("构建图：{:?}", normalized_prices);

        // 清空现有图
        *graph = TokenGraph::new();

        // 强制清理缓存，因为价格数据已更新
        let mut cache_manager = self.cache_manager.write().await;
        cache_manager.force_clear();
        drop(cache_manager);

        let mut edge_count = 0;

        for pool_price in normalized_prices {
            // 过滤流动性不足的池子
            if (pool_price.liquidity as u64) < self.min_liquidity {
                // continue; // 暂时不过滤，保持与原版本一致
            }

            let protocol = pool_price.protocol;
            let pool_type = pool_price.pool_type;

            // 添加双向交换边
            let edge_a_to_b = TokenEdge {
                to_token: pool_price.token_pair.token_b,
                pool_address: pool_price.pool_address,
                rate: pool_price.normalized_price,
                protocol: protocol.clone(),
                pool_type,
                liquidity: pool_price.liquidity as u64,
            };

            let edge_b_to_a = TokenEdge {
                to_token: pool_price.token_pair.token_a,
                pool_address: pool_price.pool_address,
                rate: 1.0 / pool_price.normalized_price,
                protocol,
                pool_type,
                liquidity: pool_price.liquidity as u64,
            };

            graph.add_edge(pool_price.token_pair.token_a, edge_a_to_b);
            graph.add_edge(pool_price.token_pair.token_b, edge_b_to_a);
            edge_count += 2;
        }

        info!("已构建代币图，包含 {} 条边，来自 {} 个池子", edge_count, pool_count);
        Ok(edge_count)
    }

    /// 使用动态规划检测套利机会
    pub async fn detect_arbitrage_opportunities(&self, start_tokens: Vec<Pubkey>) -> ArbitrageResult<Vec<ArbitragePath>> {
        // 确保使用最新的池数据重建代币图
        if self.pool_manager.is_some() {
            self.build_graph_from_pools().await?;
        }

        let graph = self.token_graph.read().await;
        let mut opportunities = Vec::new();

        // 从每个起始代币开始搜索套利路径
        for start_token in &start_tokens {
            info!("从代币 {} 开始搜索套利路径", start_token);
            let paths = self.find_arbitrage_paths_dp(&graph, *start_token).await;
            opportunities.extend(paths);
        }

        // 按净利润率排序
        opportunities.sort_by(|a, b| b.net_profit_ratio().partial_cmp(&a.net_profit_ratio()).unwrap());

        // 过滤掉不盈利的路径
        let profitable: Vec<_> = opportunities.into_iter()
            .filter(|path| path.is_profitable())
            .collect();

        // 输出缓存统计信息
        let cache_manager = self.cache_manager.read().await;
        let (cache_size, is_expired) = cache_manager.cache_stats();
        drop(cache_manager);

        info!(
            "从 {} 个起始代币找到 {} 条盈利的套利路径，缓存大小: {}，缓存状态: {}",
            start_tokens.len(),
            profitable.len(),
            cache_size,
            if is_expired { "已过期" } else { "有效" }
        );

        Ok(profitable)
    }

    /// 使用动态规划和记忆化搜索套利路径
    async fn find_arbitrage_paths_dp(
        &self,
        graph: &TokenGraph,
        start_token: Pubkey,
    ) -> Vec<ArbitragePath> {
        let mut visited = HashSet::new();
        visited.insert(start_token);

        self.dp_search(
            graph,
            start_token,
            start_token,
            self.max_depth,
            &visited,
            Vec::new(),
            1.0,
        ).await
    }

    /// 动态规划递归搜索函数，使用记忆化优化
    fn dp_search<'a>(
        &'a self,
        graph: &'a TokenGraph,
        current_token: Pubkey,
        start_token: Pubkey,
        remaining_depth: usize,
        visited: &'a HashSet<Pubkey>,
        current_path: Vec<SwapStep>,
        current_rate: f64,
    ) -> std::pin::Pin<Box<dyn std::future::Future<Output = Vec<ArbitragePath>> + 'a>> {
        Box::pin(async move {
        // 构建记忆化键
        let memo_key = MemoKey::new(current_token, start_token, remaining_depth, visited);

        // 检查记忆化缓存
        {
            let mut cache_manager = self.cache_manager.write().await;
            if let Some(cached_result) = cache_manager.get(&memo_key) {
                debug!("缓存命中: {} -> {} (深度: {})", current_token, start_token, remaining_depth);
                return cached_result.clone();
            }
        }

        let mut paths = Vec::new();

        // 如果深度用尽，返回空结果
        if remaining_depth == 0 {
            let mut cache_manager = self.cache_manager.write().await;
            cache_manager.insert(memo_key, paths.clone());
            return paths;
        }

        // 获取当前代币的所有可交换边
        if let Some(edges) = graph.get_edges(&current_token) {
            for edge in edges {
                // 检查是否回到起始代币形成循环
                if current_path.len() >= 1 && edge.to_token == start_token {
                    let final_rate = current_rate * edge.rate;
                    if final_rate > 1.0 {
                        let mut steps = current_path.clone();
                        steps.push(SwapStep {
                            pool_address: edge.pool_address,
                            from_token: current_token,
                            to_token: edge.to_token,
                            rate: edge.rate,
                            protocol: edge.protocol.clone(),
                            pool_type: edge.pool_type,
                            liquidity: edge.liquidity,
                        });

                        let path = ArbitragePath {
                            steps,
                            start_token,
                            expected_profit_ratio: final_rate - 1.0,
                            estimated_gas_cost: 100000,
                        };

                        if path.is_profitable() {
                            paths.push(path);
                        }
                    }
                    continue;
                }

                // 避免访问已经在路径中的代币（防止非终端循环）
                if visited.contains(&edge.to_token) {
                    continue;
                }

                // 构建新的访问集合和路径
                let mut new_visited = visited.clone();
                new_visited.insert(edge.to_token);

                let mut new_path = current_path.clone();
                new_path.push(SwapStep {
                    pool_address: edge.pool_address,
                    from_token: current_token,
                    to_token: edge.to_token,
                    rate: edge.rate,
                    protocol: edge.protocol.clone(),
                    pool_type: edge.pool_type,
                    liquidity: edge.liquidity,
                });

                // 递归搜索子问题
                let sub_paths = self.dp_search(
                    graph,
                    edge.to_token,
                    start_token,
                    remaining_depth - 1,
                    &new_visited,
                    new_path,
                    current_rate * edge.rate,
                ).await;

                paths.extend(sub_paths);
            }
        }

        // 将结果存入缓存
        {
            let mut cache_manager = self.cache_manager.write().await;
            cache_manager.insert(memo_key, paths.clone());
        }

        paths
        })
    }

    /// 强制清理缓存（当外部知道价格已更新时调用）
    pub async fn clear_cache(&self) {
        let mut cache_manager = self.cache_manager.write().await;
        cache_manager.force_clear();
    }

    /// 获取缓存统计信息
    pub async fn get_cache_stats(&self) -> (usize, bool) {
        let cache_manager = self.cache_manager.read().await;
        cache_manager.cache_stats()
    }

    // === 与原 ArbitrageEngine API 保持兼容的方法 ===

    /// 估算套利路径的预期利润
    pub fn estimate_profit(&self, path: &ArbitragePath, input_amount: u64) -> ArbitrageResult<u64> {
        if !path.is_profitable() {
            return Ok(0);
        }

        let gross_profit = (input_amount as f64 * path.net_profit_ratio()) as u64;
        let gas_cost = path.estimated_gas_cost;

        Ok(gross_profit.saturating_sub(gas_cost))
    }

    /// 检测从 SOL 开始的套利机会
    pub async fn detect_arbitrage_from_sol(&self) -> ArbitrageResult<Vec<ArbitragePath>> {
        self.detect_arbitrage_opportunities(vec![tokens::sol()]).await
    }

    /// 检测从 USDC 开始的套利机会
    pub async fn detect_arbitrage_from_usdc(&self) -> ArbitrageResult<Vec<ArbitragePath>> {
        self.detect_arbitrage_opportunities(vec![tokens::usdc()]).await
    }

    /// 检测从 SOL 和 USDC 开始的套利机会
    pub async fn detect_arbitrage_from_sol_usdc(&self) -> ArbitrageResult<Vec<ArbitragePath>> {
        self.detect_arbitrage_opportunities(vec![tokens::sol(), tokens::usdc()]).await
    }

    /// 检测从指定代币地址字符串开始的套利机会
    pub async fn detect_arbitrage_from_token_str(&self, token_address: &str) -> ArbitrageResult<Vec<ArbitragePath>> {
        let token = Pubkey::from_str(token_address)
            .map_err(|e| ArbitrageError::Config(format!("无效的代币地址: {}", e)))?;
        self.detect_arbitrage_opportunities(vec![token]).await
    }

    /// 检测从多个代币地址字符串开始的套利机会
    pub async fn detect_arbitrage_from_tokens_str(&self, tokens: &[&str]) -> ArbitrageResult<Vec<ArbitragePath>> {
        let mut start_tokens = Vec::new();
        for token_address in tokens {
            let token = Pubkey::from_str(token_address)
                .map_err(|e| ArbitrageError::Config(format!("无效的代币地址 '{}': {}", token_address, e)))?;
            start_tokens.push(token);
        }
        self.detect_arbitrage_opportunities(start_tokens).await
    }

    /// 获取从 SOL 开始的最佳套利机会
    pub async fn get_top_opportunities(&self, limit: usize) -> ArbitrageResult<Vec<ArbitragePath>> {
        let mut opportunities = self.detect_arbitrage_opportunities(vec![tokens::sol()]).await?;
        opportunities.truncate(limit);
        Ok(opportunities)
    }

    /// 获取从指定代币开始的最佳套利机会
    pub async fn get_top_opportunities_from_token(&self, start_token: Pubkey, limit: usize) -> ArbitrageResult<Vec<ArbitragePath>> {
        let mut opportunities = self.detect_arbitrage_opportunities(vec![start_token]).await?;
        opportunities.truncate(limit);
        Ok(opportunities)
    }

    /// 获取从多个代币开始的最佳套利机会
    pub async fn get_top_opportunities_from_tokens(&self, start_tokens: Vec<Pubkey>, limit: usize) -> ArbitrageResult<Vec<ArbitragePath>> {
        let mut opportunities = self.detect_arbitrage_opportunities(start_tokens).await?;
        opportunities.truncate(limit);
        Ok(opportunities)
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> ArbitrageResult<DpEngineStats> {
        let graph = self.token_graph.read().await;
        let token_count = graph.get_all_tokens().len();
        let edge_count = graph.get_total_edges();

        let cache_manager = self.cache_manager.read().await;
        let (cache_size, is_cache_expired) = cache_manager.cache_stats();
        drop(cache_manager);

        Ok(DpEngineStats {
            total_tokens: token_count,
            total_edges: edge_count,
            max_depth: self.max_depth,
            min_liquidity: self.min_liquidity,
            has_pool_manager: self.pool_manager.is_some(),
            cache_size,
            is_cache_expired,
        })
    }
}

/// 动态规划引擎统计信息（扩展原版本）
#[derive(Debug, Clone)]
pub struct DpEngineStats {
    pub total_tokens: usize,
    pub total_edges: usize,
    pub max_depth: usize,
    pub min_liquidity: u64,
    pub has_pool_manager: bool,
    /// 缓存大小
    pub cache_size: usize,
    /// 缓存是否过期
    pub is_cache_expired: bool,
}

impl From<DpEngineStats> for EngineStats {
    fn from(dp_stats: DpEngineStats) -> Self {
        EngineStats {
            total_tokens: dp_stats.total_tokens,
            total_edges: dp_stats.total_edges,
            max_depth: dp_stats.max_depth,
            min_liquidity: dp_stats.min_liquidity,
            has_pool_manager: dp_stats.has_pool_manager,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试记忆化键的唯一性
    #[test]
    fn test_memo_key_uniqueness() {
        let token1 = tokens::sol();
        let token2 = tokens::usdc();
        let mut visited1 = HashSet::new();
        visited1.insert(token1);
        let mut visited2 = HashSet::new();
        visited2.insert(token2);

        let key1 = MemoKey::new(token1, token1, 3, &visited1);
        let key2 = MemoKey::new(token1, token1, 3, &visited1);
        let key3 = MemoKey::new(token1, token1, 3, &visited2);

        assert_eq!(key1, key2); // 相同的键应该相等
        assert_ne!(key1, key3); // 不同访问集合的键应该不等
    }

    /// 测试缓存管理器
    #[tokio::test]
    async fn test_cache_manager() {
        let mut cache_manager = CacheManager::new(100); // 100ms TTL

        let key = MemoKey::new(tokens::sol(), tokens::sol(), 2, &HashSet::new());
        let value = vec![];

        cache_manager.insert(key.clone(), value.clone());
        assert!(cache_manager.get(&key).is_some());

        // 等待缓存过期
        tokio::time::sleep(tokio::time::Duration::from_millis(150)).await;
        assert!(cache_manager.get(&key).is_none()); // 应该已经过期
    }

    /// 测试基本引擎创建
    #[tokio::test]
    async fn test_dp_engine_creation() {
        let engine = DpArbitrageEngine::new()
            .with_max_depth(3)
            .with_min_liquidity(500);

        let stats = engine.get_stats().await.unwrap();
        assert_eq!(stats.max_depth, 3);
        assert_eq!(stats.min_liquidity, 500);
        assert_eq!(stats.cache_size, 0);
    }
}
