//! 多角套利引擎
//!
//! 支持跨多个池子的循环套利检测

use shared::{ArbitrageResult, ArbitrageError, DexProtocol, PoolType};
use state_manager::core::DexPoolManager;
use solana_sdk::{pubkey::Pubkey, instruction::Instruction};
use std::collections::HashMap;
use std::str::FromStr;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::info;
use dex_instructions::{constants, PumpSwapInstructionBuilder, SwapInstructionBuilder};

/// 常用代币地址常量
pub mod tokens {
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    /// SOL (Native Solana)
    pub const SOL: &str = "So11111111111111111111111111111111111111112";

    /// USDC
    pub const USDC: &str = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";

    /// 获取 SOL 的 Pubkey
    pub fn sol() -> Pubkey {
        Pubkey::from_str(SOL).expect("Valid SOL address")
    }

    /// 获取 USDC 的 Pubkey
    pub fn usdc() -> Pubkey {
        Pubkey::from_str(USDC).expect("Valid USDC address")
    }
}

/// 交换步骤
#[derive(Debug, Clone)]
pub struct SwapStep {
    pub pool_address: Pubkey,
    pub from_token: Pubkey,
    pub to_token: Pubkey,
    pub rate: f64,
    pub protocol: DexProtocol,
    pub pool_type: PoolType,
    pub liquidity: u64,
}

/// 套利路径
#[derive(Debug, Clone)]
pub struct ArbitragePath {
    pub steps: Vec<SwapStep>,
    pub start_token: Pubkey,
    pub expected_profit_ratio: f64,
    pub estimated_gas_cost: u64,
}

impl ArbitragePath {
    /// 计算路径的总费用（交换费用 + gas）
    pub fn total_fees(&self) -> f64 {
        // 每步0.003%交换费用
        let swap_fees = self.steps.len() as f64 * 0.00003;
        swap_fees
    }

    /// 计算路径净利润率
    pub fn net_profit_ratio(&self) -> f64 {
        self.expected_profit_ratio - self.total_fees()
    }

    /// 判断路径是否盈利
    pub fn is_profitable(&self) -> bool {
        // self.net_profit_ratio() > 0.001 // 最低0.1%净利润
        self.net_profit_ratio() > -0.05 // 只要有正利润就算盈利
    }

    /// 构建套利路径的交易指令
    pub fn build_swap_instructions(
        &self,
        user: Pubkey,
        input_amount: u64,
        slippage_bps: u64,
    ) -> ArbitrageResult<Vec<Instruction>> {
        let mut instructions = Vec::new();
        let mut current_amount = input_amount;

        for step in &self.steps {
            // 计算预期的输出金额
            let expected_output = (current_amount as f64 * step.rate) as u64;

            let instruction = match step.protocol {
                DexProtocol::PumpFun => {
                    // 确定是买入还是卖出操作
                    let is_buying = step.from_token == constants::WSOL_TOKEN_MINT;

                    if is_buying {
                        // 买入代币（SOL -> Token）
                        PumpSwapInstructionBuilder::build_buy_instruction(
                            user,
                            step.to_token,
                            step.pool_address,
                            current_amount,
                            expected_output,
                            slippage_bps,
                        )?
                    } else {
                        // 卖出代币（Token -> SOL）
                        PumpSwapInstructionBuilder::build_sell_instruction(
                            user,
                            step.from_token,
                            step.pool_address,
                            current_amount,
                            expected_output,
                            slippage_bps,
                        )?
                    }
                }
                _ => {
                    return Err(ArbitrageError::Config(
                        format!("Unsupported protocol for instruction building: {:?}", step.protocol)
                    ));
                }
            };

            instructions.push(instruction);
            current_amount = expected_output;
        }

        Ok(instructions)
    }
}

/// 代币图的边
#[derive(Debug, Clone)]
pub struct TokenEdge {
    pub to_token: Pubkey,
    pub pool_address: Pubkey,
    pub rate: f64,
    pub protocol: DexProtocol,
    pub pool_type: PoolType,
    pub liquidity: u64,
}

/// 代币交换图
#[derive(Debug)]
pub struct TokenGraph {
    edges: HashMap<Pubkey, Vec<TokenEdge>>,
}

impl TokenGraph {
    pub fn new() -> Self {
        Self {
            edges: HashMap::new(),
        }
    }

    /// 添加交换边
    pub fn add_edge(&mut self, from_token: Pubkey, edge: TokenEdge) {
        self.edges.entry(from_token).or_default().push(edge);
    }

    /// 获取从指定代币可交换到的所有代币
    pub fn get_edges(&self, token: &Pubkey) -> Option<&Vec<TokenEdge>> {
        self.edges.get(token)
    }

    /// 获取图中所有代币
    pub fn get_all_tokens(&self) -> Vec<Pubkey> {
        self.edges.keys().cloned().collect()
    }

    /// 获取图中总的边数
    pub fn get_total_edges(&self) -> usize {
        self.edges.values().map(|edges| edges.len()).sum()
    }
}

/// 多角套利引擎
pub struct ArbitrageEngine {
    /// 代币交换图
    token_graph: Arc<RwLock<TokenGraph>>,
    /// 池管理器
    pool_manager: Option<Arc<DexPoolManager>>,
    /// 最大搜索深度
    max_depth: usize,
    /// 最小流动性阈值
    min_liquidity: u64,
}

impl ArbitrageEngine {
    /// 创建新的套利引擎
    pub fn new() -> Self {
        Self {
            token_graph: Arc::new(RwLock::new(TokenGraph::new())),
            pool_manager: None,
            max_depth: 4, // 最多4步交换
            min_liquidity: 1000, // 最小流动性
        }
    }

    /// 设置池管理器
    pub fn with_pool_manager(mut self, pool_manager: Arc<DexPoolManager>) -> Self {
        self.pool_manager = Some(pool_manager);
        self
    }

    /// 设置最大搜索深度
    pub fn with_max_depth(mut self, max_depth: usize) -> Self {
        self.max_depth = max_depth;
        self
    }

    /// 设置最小流动性阈值
    pub fn with_min_liquidity(mut self, min_liquidity: u64) -> Self {
        self.min_liquidity = min_liquidity;
        self
    }

    /// 从池管理器构建代币图
    pub async fn build_graph_from_pools(&self) -> ArbitrageResult<usize> {
        let pool_manager = self.pool_manager.as_ref()
            .ok_or_else(|| ArbitrageError::Config("No pool manager configured".to_string()))?;

        let normalized_prices = pool_manager.get_all_normalized_pool_prices();
        let pool_count = normalized_prices.len();
        let mut graph = self.token_graph.write().await;

        println!("构建图：{:?}", normalized_prices);

        // 清空现有图
        *graph = TokenGraph::new();

        let mut edge_count = 0;

        for pool_price in normalized_prices {
            // 过滤流动性不足的池子
            if (pool_price.liquidity as u64) < self.min_liquidity {
                // continue;
            }

            let protocol =pool_price.protocol;
            let pool_type = pool_price.pool_type;

            // 添加双向交换边
            let edge_a_to_b = TokenEdge {
                to_token: pool_price.token_pair.token_b,
                pool_address: pool_price.pool_address,
                rate: pool_price.normalized_price,
                protocol: protocol.clone(),
                pool_type,
                liquidity: pool_price.liquidity as u64,
            };

            let edge_b_to_a = TokenEdge {
                to_token: pool_price.token_pair.token_a,
                pool_address: pool_price.pool_address,
                rate: 1.0 / pool_price.normalized_price,
                protocol,
                pool_type,
                liquidity: pool_price.liquidity as u64,
            };

            graph.add_edge(pool_price.token_pair.token_a, edge_a_to_b);
            graph.add_edge(pool_price.token_pair.token_b, edge_b_to_a);
            edge_count += 2;
        }

        info!("Built token graph with {} edges from {} pools", edge_count, pool_count);
        Ok(edge_count)
    }

    /// 检测多角套利机会
    ///
    /// # 参数
    /// * `start_tokens` - 起始代币列表，从这些代币开始搜索套利路径
    ///
    /// # 常用代币地址
    /// * SOL: So11111111111111111111111111111111111111112
    /// * USDC: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
    pub async fn detect_arbitrage_opportunities(&self, start_tokens: Vec<Pubkey>) -> ArbitrageResult<Vec<ArbitragePath>> {
        // 确保使用最新的池数据重建代币图
        if self.pool_manager.is_some() {
            self.build_graph_from_pools().await?;
        }

        let graph = self.token_graph.read().await;
        let mut opportunities = Vec::new();

        // 遍历所有指定的起始代币，从每个代币开始搜索套利路径
        for start_token in &start_tokens {
            info!("Searching for arbitrage paths starting from token: {}", start_token);
            let paths = self.find_arbitrage_paths(&graph, *start_token);
            opportunities.extend(paths);
        }

        // 按净利润率排序
        opportunities.sort_by(|a, b| b.net_profit_ratio().partial_cmp(&a.net_profit_ratio()).unwrap());

        // 过滤掉不盈利的路径
        let profitable: Vec<_> = opportunities.into_iter()
            .filter(|path| path.is_profitable())
            .collect();

        info!("Found {} profitable arbitrage paths from {} starting tokens", profitable.len(), start_tokens.len());
        Ok(profitable)
    }

    /// 使用栈的方式搜索套利路径（避免递归）
    fn find_arbitrage_paths(
        &self,
        graph: &TokenGraph,
        start_token: Pubkey,
    ) -> Vec<ArbitragePath> {
        let mut paths = Vec::new();
        let mut stack = Vec::new();

        // 初始化栈：(当前代币, 当前路径, 当前汇率, 深度)
        stack.push((start_token, Vec::new(), 1.0, 0));

        while let Some((current_token, current_path, current_rate, depth)) = stack.pop() {
            // 如果到达最大深度，停止探索这条路径
            if depth >= self.max_depth {
                continue;
            }

            // 获取当前代币的所有可交换边
            if let Some(edges) = graph.get_edges(&current_token) {
                for edge in edges {
                    // 避免立即返回起始代币（至少要2步）
                    if depth == 0 && edge.to_token == start_token {
                        continue;
                    }

                    // 检查是否回到起始代币形成套利循环
                    if depth >= 1 && edge.to_token == start_token {
                        let final_rate = current_rate * edge.rate;
                        if final_rate > 1.0 {
                            let mut steps = current_path.clone();
                            steps.push(SwapStep {
                                pool_address: edge.pool_address,
                                from_token: current_token,
                                to_token: edge.to_token,
                                rate: edge.rate,
                                protocol: edge.protocol.clone(),
                                pool_type: edge.pool_type,
                                liquidity: edge.liquidity,
                            });

                            let path = ArbitragePath {
                                steps,
                                start_token,
                                expected_profit_ratio: final_rate - 1.0,
                                estimated_gas_cost: 100000,
                            };

                            if path.is_profitable() {
                                paths.push(path);
                            }
                        }
                        continue;
                    }

                    // 避免访问已经在路径中的代币
                    let already_visited = current_path.iter()
                        .any(|step| step.to_token == edge.to_token);
                    if already_visited {
                        continue;
                    }

                    // 添加新路径到栈中继续探索
                    let mut new_path = current_path.clone();
                    new_path.push(SwapStep {
                        pool_address: edge.pool_address,
                        from_token: current_token,
                        to_token: edge.to_token,
                        rate: edge.rate,
                        protocol: edge.protocol.clone(),
                        pool_type: edge.pool_type,
                        liquidity: edge.liquidity,
                    });

                    stack.push((
                        edge.to_token,
                        new_path,
                        current_rate * edge.rate,
                        depth + 1,
                    ));
                }
            }
        }

        paths
    }

    /// 估算套利路径的预期利润
    pub fn estimate_profit(&self, path: &ArbitragePath, input_amount: u64) -> ArbitrageResult<u64> {
        if !path.is_profitable() {
            return Ok(0);
        }

        let gross_profit = (input_amount as f64 * path.net_profit_ratio()) as u64;
        let gas_cost = path.estimated_gas_cost;

        Ok(gross_profit.saturating_sub(gas_cost))
    }


    /// 检测从多个代币地址字符串开始的套利机会
    pub async fn detect_arbitrage_from_tokens_str(&self, tokens: &[&str]) -> ArbitrageResult<Vec<ArbitragePath>> {
        let mut start_tokens = Vec::new();
        for token_address in tokens {
            let token = Pubkey::from_str(token_address)
                .map_err(|e| ArbitrageError::Config(format!("Invalid token address '{}': {}", token_address, e)))?;
            start_tokens.push(token);
        }
        self.detect_arbitrage_opportunities(start_tokens).await
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> ArbitrageResult<EngineStats> {
        let graph = self.token_graph.read().await;
        let token_count = graph.get_all_tokens().len();
        let edge_count: usize = graph.edges.values().map(|edges| edges.len()).sum();

        Ok(EngineStats {
            total_tokens: token_count,
            total_edges: edge_count,
            max_depth: self.max_depth,
            min_liquidity: self.min_liquidity,
            has_pool_manager: self.pool_manager.is_some(),
        })
    }

    /// 构建套利执行指令
    ///
    /// # 参数
    /// * `path` - 套利路径
    /// * `user` - 执行者钱包地址
    /// * `input_amount` - 输入金额
    /// * `slippage_bps` - 滑点容忍度（基点）
    ///
    /// # 返回
    /// 返回构建好的交易指令列表
    pub fn build_arbitrage_instructions(
        &self,
        path: &ArbitragePath,
        user: Pubkey,
        input_amount: u64,
        slippage_bps: u64,
    ) -> ArbitrageResult<Vec<Instruction>> {
        if !path.is_profitable() {
            return Err(ArbitrageError::Config(
                "Cannot execute unprofitable arbitrage path".to_string()
            ));
        }

        path.build_swap_instructions(user, input_amount, slippage_bps)
    }

    /// 估算最佳输入金额
    ///
    /// 根据路径的流动性限制估算最佳的输入金额
    pub fn estimate_optimal_input_amount(&self, path: &ArbitragePath) -> u64 {
        // 找到路径中流动性最小的步骤
        let min_liquidity = path.steps.iter()
            .map(|step| step.liquidity)
            .min()
            .unwrap_or(0);

        // 使用最小流动性的10%作为安全的输入金额
        (min_liquidity / 10).max(1000) // 最小1000单位
    }
}

/// 引擎统计信息
#[derive(Debug, Clone)]
pub struct EngineStats {
    pub total_tokens: usize,
    pub total_edges: usize,
    pub max_depth: usize,
    pub min_liquidity: u64,
    pub has_pool_manager: bool,
}
