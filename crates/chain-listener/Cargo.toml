[package]
name = "chain-listener"
version.workspace = true
edition.workspace = true

[dependencies]
shared = { path = "../shared" }
futures.workspace = true
futures-util.workspace = true
solana-sdk.workspace = true
solana-transaction-status.workspace = true
thiserror.workspace = true
tokio.workspace = true
tokio-stream.workspace = true
yellowstone-grpc-client.workspace = true
yellowstone-grpc-proto.workspace = true
bs58.workspace = true
tracing.workspace = true
rand.workspace = true