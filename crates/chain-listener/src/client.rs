use std::sync::Arc;
use std::time::Duration;
use futures::channel::mpsc::SendError;
use futures_util::{Sink, SinkExt};
use tokio::sync::Mutex;
use tokio_stream::StreamExt;
use yellowstone_grpc_client::{ClientTlsConfig, GeyserGrpcClient, Interceptor};
use yellowstone_grpc_proto::geyser::{CommitmentLevel, SubscribeRequestPing};
use yellowstone_grpc_proto::geyser::subscribe_update::UpdateOneof;
use yellowstone_grpc_proto::prelude::{SubscribeRequest, SubscribeRequestFilterAccounts, SubscribeUpdate};
use shared::{AccountData, DataType, TransactionData};

use crate::{ChainListenerConfig, ChainListenerError};
use crate::connection::{ConnectionManager, ConnectionState};
use crate::reconnect::Reconnector;
use crate::health::HealthMonitor;
use crate::disconnect::DisconnectionDetector;

/// Yellowstone gRPC 客户端
#[derive(Clone)]
pub struct YellowstoneGrpcClient {
    config: ChainListenerConfig,
    connection_manager: Arc<ConnectionManager>,
}

/// 监控客户端 - 带有健康监控和重连功能的客户端
pub struct MonitoredYellowstoneGrpcClient {
    client: YellowstoneGrpcClient,
    connection_manager: Arc<ConnectionManager>,
    reconnector: Option<Reconnector>,
    health_monitor: Option<HealthMonitor>,
    disconnection_detector: Option<DisconnectionDetector>,
}

impl YellowstoneGrpcClient {
    /// 创建新的客户端实例
    pub fn new(config: ChainListenerConfig) -> Self {
        Self { 
            config,
            connection_manager: Arc::new(ConnectionManager::new()),
        }
    }

    /// 获取连接管理器
    pub fn connection_manager(&self) -> Arc<ConnectionManager> {
        self.connection_manager.clone()
    }

    /// 构建底层gRPC客户端
    pub async fn build_client(
        &self,
    ) -> Result<Arc<Mutex<GeyserGrpcClient<impl Interceptor>>>, ChainListenerError> {
        let client = GeyserGrpcClient::build_from_shared(self.config.endpoint.clone())?
            .x_token(self.config.x_token.clone())?
            .tls_config(ClientTlsConfig::new().with_native_roots())?
            .connect_timeout(Duration::from_secs(self.config.connect_timeout))
            .keep_alive_while_idle(self.config.keep_alive)
            .timeout(Duration::from_secs(self.config.request_timeout))
            .connect()
            .await?;
        Ok(Arc::new(Mutex::new(client)))
    }

    /// 使用配置订阅数据流（基础版本，无监控）
    pub async fn subscribe_with_config(
        &self,
        config: SubscriptionConfig,
        data_handler: &DataHandler,
    ) -> Result<(), ChainListenerError> {
        let client = self.build_client().await?;
        let subscribe_request = config.to_subscribe_request();

        tracing::debug!("SubscribeRequest: {:?}", subscribe_request);
        
        // 更新连接状态
        self.connection_manager.update_state(ConnectionState::Connected).await;

        let (mut subscribe_tx, mut stream) = client
            .lock()
            .await
            .subscribe_with_request(Some(subscribe_request)).await?;

        while let Some(message) = stream.next().await {
            match message {
                Ok(msg) => {
                    // 记录收到消息
                    self.connection_manager.record_message().await;
                    
                    if let Err(e) = Self::message_handler(msg, &mut subscribe_tx, &data_handler, &self.connection_manager).await {
                        tracing::error!("Error handling message: {:?}", e);
                        self.connection_manager.record_error().await;
                    }
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {:?}", e);
                    self.connection_manager.record_error().await;
                    self.connection_manager.update_state(ConnectionState::Disconnected).await;
                    break;
                }
            }
        }

        Ok(())
    }

    /// 使用配置订阅数据流（带监控和重连功能）
    pub async fn subscribe_with_monitoring(
        &self,
        config: SubscriptionConfig,
        data_handler: &DataHandler,
    ) -> Result<(), ChainListenerError> {
        if !self.config.monitoring_enabled {
            return self.subscribe_with_config(config, &data_handler).await;
        }

        let monitored_client = MonitoredYellowstoneGrpcClient::new(
            self.config.clone(),
            self.connection_manager.clone(),
        );
        
        monitored_client.subscribe_with_config(config, &data_handler).await
    }

    /// 处理订阅消息
    pub async fn message_handler(
        message: SubscribeUpdate,
        subscribe_tx: &mut (impl Sink<SubscribeRequest, Error = SendError> + std::marker::Unpin),
        handler: &DataHandler,
        connection_manager: &ConnectionManager,
    ) -> Result<(), ChainListenerError> {
        match message.update_oneof {
            Some(UpdateOneof::Transaction(sut)) => {
                let transaction_data: TransactionData = sut.into();
                if let Err(e) = handler(DataType::Transaction(transaction_data)) {
                    tracing::error!("Error handling transaction data: {:?}", e);
                }
            }
            Some(UpdateOneof::Account(account_update)) => {
                let account = AccountData {
                    pubkey: account_update.account.as_ref()
                        .map(|acc| bs58::encode(&acc.pubkey).into_string())
                        .unwrap_or_default(),
                    slot: account_update.slot,
                    lamports: account_update.account.as_ref()
                        .map(|acc| acc.lamports)
                        .unwrap_or_default(),
                    owner: account_update.account.as_ref()
                        .map(|acc| bs58::encode(&acc.owner).into_string())
                        .unwrap_or_default(),
                    data: account_update.account.as_ref()
                        .map(|acc| acc.data.clone())
                        .unwrap_or_default(),
                    executable: account_update.account.as_ref()
                        .map(|acc| acc.executable)
                        .unwrap_or_default(),
                    rent_epoch: account_update.account.as_ref()
                        .map(|acc| acc.rent_epoch)
                        .unwrap_or_default(),
                };
                if let Err(e) = handler(DataType::Account(account)) {
                    tracing::error!("Error handling account data: {:?}", e);
                }
            }
            Some(UpdateOneof::Ping(_)) => {
                let _ = subscribe_tx
                    .send(SubscribeRequest {
                        ping: Some(SubscribeRequestPing { id: 1 }),
                        ..Default::default()
                    })
                    .await;
                connection_manager.record_ping().await;
                tracing::debug!("Ping sent successfully");
            }
            Some(UpdateOneof::Pong(_)) => {
                connection_manager.record_pong().await;
                tracing::debug!("Pong received");
            }
            _ => {
                tracing::debug!("Received other update type");
            }
        }
        Ok(())
    }
}

/// 订阅配置
#[derive(Debug, Clone)]
pub struct SubscriptionConfig {
    /// 要监听的账户地址
    pub accounts: Option<Vec<String>>,
    /// 要监听的程序ID
    pub programs: Option<Vec<String>>,
    /// 排除的交易账户
    pub tx_account_exclude: Option<Vec<String>>,
    /// 必需的交易账户
    pub tx_account_required: Option<Vec<String>>,
    /// 是否订阅投票交易
    pub vote: bool,
    /// 是否订阅失败交易
    pub failed: bool,
}

impl Default for SubscriptionConfig {
    fn default() -> Self {
        Self {
            accounts: None,
            programs: None,
            tx_account_exclude: None,
            tx_account_required: None,
            vote: false,
            failed: false,
        }
    }
}

impl SubscriptionConfig {
    /// 转换为Yellowstone订阅请求
    pub fn to_subscribe_request(&self) -> SubscribeRequest {
        let mut request = SubscribeRequest::default();

        if let Some(accounts) = &self.accounts {
            request.accounts.insert(
                "client".to_string(),
                SubscribeRequestFilterAccounts {
                    account: accounts.clone(),
                    ..Default::default()
                },
            );
        }

        if let Some(programs) = &self.programs {
            request.transactions.insert(
                "client".to_string(),
                yellowstone_grpc_proto::prelude::SubscribeRequestFilterTransactions {
                    vote: Some(self.vote),
                    failed: Some(self.failed),
                    signature: None,
                    account_include: programs.clone(),
                    account_exclude: self.tx_account_exclude.clone().unwrap_or_default(),
                    account_required: self.tx_account_required.clone().unwrap_or_default(),
                },
            );
        }

        request.set_commitment(CommitmentLevel::Processed);
        request
    }
}

/// 数据处理函数类型
pub type DataHandler = Arc<dyn Fn(DataType) -> Result<(), ChainListenerError> + Send + Sync>;

impl MonitoredYellowstoneGrpcClient {
    /// 创建带监控的客户端
    pub fn new(config: ChainListenerConfig, connection_manager: Arc<ConnectionManager>) -> Self {
        let client = YellowstoneGrpcClient {
            config: config.clone(),
            connection_manager: connection_manager.clone(),
        };

        let mut reconnector = None;
        let mut health_monitor = None;
        let mut disconnection_detector = None;

        if config.monitoring_enabled {
            // 初始化重连器
            if config.reconnect.enabled {
                reconnector = Some(Reconnector::new(config.reconnect.clone(), connection_manager.clone()));
            }

            // 初始化健康监控器
            if config.health_check.enabled {
                health_monitor = Some(HealthMonitor::new(config.health_check.clone(), connection_manager.clone()));
            }

            // 初始化断线检测器
            if config.disconnection_detection.enabled {
                disconnection_detector = Some(DisconnectionDetector::new(
                    config.disconnection_detection.clone(),
                    connection_manager.clone(),
                ));
            }
        }

        Self {
            client,
            connection_manager,
            reconnector,
            health_monitor,
            disconnection_detector,
        }
    }

    /// 使用配置订阅数据流（带完整监控功能）
    pub async fn subscribe_with_config(
        &self,
        config: SubscriptionConfig,
        data_handler: &DataHandler,
    ) -> Result<(), ChainListenerError> {
        // 验证配置
        self.client.config.validate()?;

        // 启动监控组件
        self.start_monitoring_components().await?;

        // 开始订阅流程，包含重连逻辑
        self.subscribe_with_reconnect(config, &data_handler).await
    }

    /// 启动监控组件
    async fn start_monitoring_components(&self) -> Result<(), ChainListenerError> {
        // 启动断线检测
        if let Some(ref detector) = self.disconnection_detector {
            let mut detector_clone = detector.clone();
            tokio::spawn(async move {
                if let Err(e) = detector_clone.start_detection().await {
                    tracing::error!("Disconnection detector failed: {}", e);
                }
            });
        }

        Ok(())
    }

    /// 带重连的订阅流程
    async fn subscribe_with_reconnect(
        &self,
        config: SubscriptionConfig,
        data_handler: &DataHandler,
    ) -> Result<(), ChainListenerError> {
        loop {
            // 尝试连接和订阅
            match self.attempt_subscribe(config.clone(), &data_handler).await {
                Ok(()) => {
                    tracing::info!("Subscription completed normally");
                    break;
                }
                Err(e) => {
                    tracing::error!("Subscription failed: {}", e);
                    self.connection_manager.record_error().await;

                    // 检查是否需要重连
                    if let Some(ref mut reconnector) = self.reconnector.as_ref().map(|r| r.clone()) {
                        if reconnector.can_reconnect() {
                            tracing::info!("Attempting to reconnect...");
                            
                            // 执行重连
                            match reconnector.smart_reconnect(|| {
                                let client = self.client.clone();
                                let _config = config.clone();
                                async move {
                                    // 简单的连接测试
                                    client.build_client().await.map(|_| ())
                                }
                            }).await {
                                Ok(()) => {
                                    tracing::info!("Reconnection successful, resuming subscription");
                                    continue; // 重新尝试订阅
                                }
                                Err(reconnect_error) => {
                                    tracing::error!("Reconnection failed: {}", reconnect_error);
                                    return Err(reconnect_error);
                                }
                            }
                        } else {
                            tracing::error!("Maximum reconnection attempts reached");
                            return Err(ChainListenerError::MaxReconnectAttemptsExceeded {
                                attempts: reconnector.current_attempts(),
                            });
                        }
                    } else {
                        // 没有重连器，直接返回错误
                        return Err(e);
                    }
                }
            }
        }

        Ok(())
    }

    /// 尝试订阅（单次）
    async fn attempt_subscribe(
        &self,
        config: SubscriptionConfig,
        data_handler: &DataHandler,
    ) -> Result<(), ChainListenerError> {
        let client = self.client.build_client().await?;
        let subscribe_request = config.to_subscribe_request();

        tracing::info!("Starting subscription with monitoring enabled");
        self.connection_manager.update_state(ConnectionState::Connected).await;

        let (mut subscribe_tx, mut stream) = client
            .lock()
            .await
            .subscribe_with_request(Some(subscribe_request))
            .await?;

        // 启动被动健康监控
        if let Some(ref health_monitor) = self.health_monitor {
            let mut health_monitor_clone = health_monitor.clone();
            tokio::spawn(async move {
                if let Err(e) = health_monitor_clone.start_passive_monitoring().await {
                    tracing::error!("Health monitoring failed: {}", e);
                }
            });
        }

        // 主要消息处理循环
        while let Some(message) = stream.next().await {
            match message {
                Ok(msg) => {
                    // 记录收到消息
                    self.connection_manager.record_message().await;

                    // 处理特殊消息类型
                    match &msg.update_oneof {
                        Some(UpdateOneof::Pong(pong)) => {
                            if let Some(ref health_monitor) = self.health_monitor {
                                health_monitor.handle_pong(pong.id).await;
                            }
                        }
                        _ => {}
                    }

                    // 处理消息
                    if let Err(e) = YellowstoneGrpcClient::message_handler(
                        msg,
                        &mut subscribe_tx,
                        &data_handler,
                        &self.connection_manager,
                    ).await {
                        tracing::error!("Error handling message: {:?}", e);
                        self.connection_manager.record_error().await;
                    }
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {:?}", e);
                    self.connection_manager.record_error().await;
                    self.connection_manager.update_state(ConnectionState::Disconnected).await;
                    
                    return Err(ChainListenerError::Connection(format!("Stream error: {}", e)));
                }
            }
        }

        // 流结束，更新状态
        self.connection_manager.update_state(ConnectionState::Disconnected).await;
        Ok(())
    }

    /// 获取连接状态
    pub async fn get_connection_state(&self) -> ConnectionState {
        self.connection_manager.get_state().await
    }

    /// 获取连接健康信息
    pub async fn get_connection_health(&self) -> crate::connection::ConnectionHealth {
        self.connection_manager.get_health().await
    }

    /// 获取监控统计信息
    pub async fn get_monitoring_stats(&self) -> MonitoringStats {
        let health = self.connection_manager.get_health().await;
        
        let ping_stats = if let Some(ref health_monitor) = self.health_monitor {
            Some(health_monitor.get_ping_stats().await)
        } else {
            None
        };

        let disconnection_stats = if let Some(ref detector) = self.disconnection_detector {
            Some(detector.get_stats().await)
        } else {
            None
        };

        let reconnect_attempts = if let Some(ref reconnector) = self.reconnector {
            reconnector.current_attempts()
        } else {
            0
        };

        MonitoringStats {
            connection_health: health,
            ping_stats,
            disconnection_stats,
            reconnect_attempts,
            message_count: self.connection_manager.get_message_count(),
            error_count: self.connection_manager.get_error_count(),
        }
    }

    /// 手动触发重连
    pub async fn manual_reconnect(&mut self) -> Result<(), ChainListenerError> {
        if let Some(ref mut reconnector) = self.reconnector {
            reconnector.smart_reconnect(|| {
                let client = self.client.clone();
                async move {
                    client.build_client().await.map(|_| ())
                }
            }).await
        } else {
            Err(ChainListenerError::Config("Reconnection is not enabled".to_string()))
        }
    }

    /// 停止所有监控组件
    pub async fn stop_monitoring(&mut self) {
        if let Some(ref mut health_monitor) = self.health_monitor {
            health_monitor.stop_monitoring().await;
        }

        if let Some(ref mut detector) = self.disconnection_detector {
            detector.stop_detection().await;
        }

        tracing::info!("All monitoring components stopped");
    }
}

/// 监控统计信息
#[derive(Debug, Clone)]
pub struct MonitoringStats {
    /// 连接健康状态
    pub connection_health: crate::connection::ConnectionHealth,
    /// Ping统计
    pub ping_stats: Option<crate::health::PingStats>,
    /// 断线检测统计
    pub disconnection_stats: Option<crate::disconnect::DisconnectionStats>,
    /// 重连尝试次数
    pub reconnect_attempts: u32,
    /// 消息计数
    pub message_count: u64,
    /// 错误计数
    pub error_count: u64,
}
