use std::time::Duration;
use crate::reconnect::ReconnectConfig;
use crate::health::HealthCheckConfig;
use crate::disconnect::DisconnectionConfig;

/// 链监听器配置
#[derive(Debug, Clone)]
pub struct ChainListenerConfig {
    /// Yellowstone gRPC 端点
    pub endpoint: String,
    /// 认证Token
    pub x_token: Option<String>,
    /// 连接超时时间（秒）
    pub connect_timeout: u64,
    /// 请求超时时间（秒）
    pub request_timeout: u64,
    /// 是否保持连接
    pub keep_alive: bool,
    /// 重连配置
    pub reconnect: ReconnectConfig,
    /// 健康检查配置
    pub health_check: HealthCheckConfig,
    /// 断线检测配置
    pub disconnection_detection: DisconnectionConfig,
    /// 是否启用监控功能
    pub monitoring_enabled: bool,
}

impl Default for ChainListenerConfig {
    fn default() -> Self {
        Self {
            endpoint: "https://solana-yellowstone-grpc.publicnode.com:443".to_string(),
            x_token: None,
            connect_timeout: 10,
            request_timeout: 60,
            keep_alive: true,
            reconnect: ReconnectConfig::default(),
            health_check: HealthCheckConfig::default(),
            disconnection_detection: DisconnectionConfig::default(),
            monitoring_enabled: true,
        }
    }
}

impl ChainListenerConfig {
    /// 创建新的配置
    pub fn new(endpoint: String) -> Self {
        Self {
            endpoint,
            ..Default::default()
        }
    }

    /// 设置认证Token
    pub fn with_token(mut self, token: String) -> Self {
        self.x_token = Some(token);
        self
    }

    /// 设置连接超时
    pub fn with_connect_timeout(mut self, timeout: u64) -> Self {
        self.connect_timeout = timeout;
        self
    }

    /// 设置请求超时
    pub fn with_request_timeout(mut self, timeout: u64) -> Self {
        self.request_timeout = timeout;
        self
    }

    /// 设置重连配置
    pub fn with_reconnect_config(mut self, config: ReconnectConfig) -> Self {
        self.reconnect = config;
        self
    }

    /// 设置健康检查配置
    pub fn with_health_check_config(mut self, config: HealthCheckConfig) -> Self {
        self.health_check = config;
        self
    }

    /// 设置断线检测配置
    pub fn with_disconnection_detection_config(mut self, config: DisconnectionConfig) -> Self {
        self.disconnection_detection = config;
        self
    }

    /// 设置是否启用监控
    pub fn with_monitoring_enabled(mut self, enabled: bool) -> Self {
        self.monitoring_enabled = enabled;
        self
    }

    /// 启用快速监控模式（适合高频交易）
    pub fn enable_fast_monitoring(mut self) -> Self {
        use crate::reconnect::ReconnectConfigBuilder;
        use crate::health::HealthMonitorBuilder;
        use crate::disconnect::DisconnectionDetectorBuilder;

        self.reconnect = ReconnectConfigBuilder::new()
            .fast_reconnect()
            .build();
        
        self.health_check = HealthMonitorBuilder::new()
            .fast_monitoring()
            .build(std::sync::Arc::new(crate::connection::ConnectionManager::new()))
            .config;
        
        self.disconnection_detection = DisconnectionDetectorBuilder::new()
            .fast_detection()
            .build(std::sync::Arc::new(crate::connection::ConnectionManager::new()))
            .get_config();
        
        self.monitoring_enabled = true;
        self
    }

    /// 启用稳定监控模式（适合长期连接）
    pub fn enable_stable_monitoring(mut self) -> Self {
        use crate::reconnect::ReconnectConfigBuilder;
        use crate::health::HealthMonitorBuilder;
        use crate::disconnect::DisconnectionDetectorBuilder;

        self.reconnect = ReconnectConfigBuilder::new()
            .stable_reconnect()
            .build();
        
        self.health_check = HealthMonitorBuilder::new()
            .standard_monitoring()
            .build(std::sync::Arc::new(crate::connection::ConnectionManager::new()))
            .config;
        
        self.disconnection_detection = DisconnectionDetectorBuilder::new()
            .standard_detection()
            .build(std::sync::Arc::new(crate::connection::ConnectionManager::new()))
            .get_config();
        
        self.monitoring_enabled = true;
        self
    }

    /// 启用宽松监控模式（适合不稳定网络）
    pub fn enable_relaxed_monitoring(mut self) -> Self {
        use crate::reconnect::ReconnectConfigBuilder;
        use crate::health::HealthMonitorBuilder;
        use crate::disconnect::DisconnectionDetectorBuilder;

        self.reconnect = ReconnectConfigBuilder::new()
            .persistent_reconnect()
            .build();
        
        self.health_check = HealthMonitorBuilder::new()
            .relaxed_monitoring()
            .build(std::sync::Arc::new(crate::connection::ConnectionManager::new()))
            .config;
        
        self.disconnection_detection = DisconnectionDetectorBuilder::new()
            .relaxed_detection()
            .build(std::sync::Arc::new(crate::connection::ConnectionManager::new()))
            .get_config();
        
        self.monitoring_enabled = true;
        self
    }

    /// 禁用所有监控功能
    pub fn disable_monitoring(mut self) -> Self {
        self.monitoring_enabled = false;
        self.reconnect.enabled = false;
        self.health_check.enabled = false;
        self.disconnection_detection.enabled = false;
        self
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> Result<(), crate::error::ChainListenerError> {
        // 验证端点
        if self.endpoint.is_empty() {
            return Err(crate::error::ChainListenerError::Config(
                "Endpoint cannot be empty".to_string()
            ));
        }

        // 验证超时时间
        if self.connect_timeout == 0 {
            return Err(crate::error::ChainListenerError::Config(
                "Connect timeout must be greater than 0".to_string()
            ));
        }

        if self.request_timeout == 0 {
            return Err(crate::error::ChainListenerError::Config(
                "Request timeout must be greater than 0".to_string()
            ));
        }

        // 验证监控配置的一致性
        if self.monitoring_enabled {
            if self.health_check.ping_interval > Duration::from_secs(self.request_timeout) {
                return Err(crate::error::ChainListenerError::Config(
                    "Health check ping interval should not exceed request timeout".to_string()
                ));
            }

            if self.disconnection_detection.message_timeout < Duration::from_secs(self.connect_timeout) {
                return Err(crate::error::ChainListenerError::Config(
                    "Disconnection detection message timeout should be greater than connect timeout".to_string()
                ));
            }
        }

        Ok(())
    }
}