use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

/// 连接状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ConnectionState {
    /// 断开连接
    Disconnected,
    /// 正在连接
    Connecting,
    /// 已连接
    Connected,
    /// 正在重连
    Reconnecting,
    /// 连接失败
    Failed,
    /// 健康状态
    Healthy,
    /// 降级状态（连接但性能较差）
    Degraded,
}

impl std::fmt::Display for ConnectionState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ConnectionState::Disconnected => write!(f, "Disconnected"),
            ConnectionState::Connecting => write!(f, "Connecting"),
            ConnectionState::Connected => write!(f, "Connected"),
            ConnectionState::Reconnecting => write!(f, "Reconnecting"),
            ConnectionState::Failed => write!(f, "Failed"),
            ConnectionState::Healthy => write!(f, "Healthy"),
            ConnectionState::Degraded => write!(f, "Degraded"),
        }
    }
}

/// 连接健康状态信息
#[derive(Debug, Clone)]
pub struct ConnectionHealth {
    /// 当前连接状态
    pub state: ConnectionState,
    /// 最后一次状态更新时间
    pub last_update: Instant,
    /// 连接建立时间
    pub connected_at: Option<Instant>,
    /// 最后一次收到消息的时间
    pub last_message_at: Option<Instant>,
    /// 最后一次ping的时间
    pub last_ping_at: Option<Instant>,
    /// 最后一次pong的时间
    pub last_pong_at: Option<Instant>,
    /// 平均往返时间（RTT）
    pub average_rtt: Option<Duration>,
    /// 最近的RTT测量值
    pub recent_rtt_samples: Vec<Duration>,
    /// 错误计数
    pub error_count: u64,
    /// 连续错误计数
    pub consecutive_errors: u64,
    /// 重连尝试次数
    pub reconnect_attempts: u64,
    /// 总接收消息数
    pub total_messages: u64,
    /// 最近一分钟接收的消息数
    pub messages_per_minute: u64,
}

impl Default for ConnectionHealth {
    fn default() -> Self {
        Self {
            state: ConnectionState::Disconnected,
            last_update: Instant::now(),
            connected_at: None,
            last_message_at: None,
            last_ping_at: None,
            last_pong_at: None,
            average_rtt: None,
            recent_rtt_samples: Vec::with_capacity(10),
            error_count: 0,
            consecutive_errors: 0,
            reconnect_attempts: 0,
            total_messages: 0,
            messages_per_minute: 0,
        }
    }
}

impl ConnectionHealth {
    /// 更新连接状态
    pub fn update_state(&mut self, new_state: ConnectionState) {
        self.state = new_state;
        self.last_update = Instant::now();
        
        if new_state == ConnectionState::Connected {
            self.connected_at = Some(Instant::now());
            self.consecutive_errors = 0;
        }
    }

    /// 记录收到消息
    pub fn record_message(&mut self) {
        self.last_message_at = Some(Instant::now());
        self.total_messages += 1;
    }

    /// 记录ping发送
    pub fn record_ping(&mut self) {
        self.last_ping_at = Some(Instant::now());
    }

    /// 记录pong接收并计算RTT
    pub fn record_pong(&mut self) {
        let now = Instant::now();
        self.last_pong_at = Some(now);
        
        if let Some(ping_time) = self.last_ping_at {
            let rtt = now.duration_since(ping_time);
            self.add_rtt_sample(rtt);
        }
    }

    /// 添加RTT样本
    pub fn add_rtt_sample(&mut self, rtt: Duration) {
        self.recent_rtt_samples.push(rtt);
        
        // 保持最近10个样本
        if self.recent_rtt_samples.len() > 10 {
            self.recent_rtt_samples.remove(0);
        }
        
        // 计算平均RTT
        if !self.recent_rtt_samples.is_empty() {
            let total_rtt: Duration = self.recent_rtt_samples.iter().sum();
            self.average_rtt = Some(total_rtt / self.recent_rtt_samples.len() as u32);
        }
    }

    /// 记录错误
    pub fn record_error(&mut self) {
        self.error_count += 1;
        self.consecutive_errors += 1;
        self.last_update = Instant::now();
    }

    /// 记录重连尝试
    pub fn record_reconnect_attempt(&mut self) {
        self.reconnect_attempts += 1;
        self.last_update = Instant::now();
    }

    /// 检查连接是否健康
    pub fn is_healthy(&self, max_rtt: Duration, max_consecutive_errors: u64) -> bool {
        match self.state {
            ConnectionState::Connected | ConnectionState::Healthy => {
                // 检查RTT
                if let Some(rtt) = self.average_rtt {
                    if rtt > max_rtt {
                        return false;
                    }
                }
                
                // 检查连续错误数
                self.consecutive_errors <= max_consecutive_errors
            }
            _ => false,
        }
    }

    /// 检查是否需要降级
    pub fn should_degrade(&self, rtt_threshold: Duration, error_threshold: u64) -> bool {
        if let Some(rtt) = self.average_rtt {
            if rtt > rtt_threshold || self.consecutive_errors > error_threshold {
                return true;
            }
        }
        false
    }

    /// 获取连接持续时间
    pub fn connection_duration(&self) -> Option<Duration> {
        self.connected_at.map(|connected| Instant::now().duration_since(connected))
    }

    /// 获取自上次消息以来的时间
    pub fn time_since_last_message(&self) -> Option<Duration> {
        self.last_message_at.map(|last| Instant::now().duration_since(last))
    }
}

/// 连接管理器
pub struct ConnectionManager {
    /// 连接健康状态
    health: Arc<RwLock<ConnectionHealth>>,
    /// 消息计数器（用于计算吞吐量）
    message_counter: Arc<AtomicU64>,
    /// 错误计数器
    error_counter: Arc<AtomicU64>,
}

impl ConnectionManager {
    /// 创建新的连接管理器
    pub fn new() -> Self {
        Self {
            health: Arc::new(RwLock::new(ConnectionHealth::default())),
            message_counter: Arc::new(AtomicU64::new(0)),
            error_counter: Arc::new(AtomicU64::new(0)),
        }
    }

    /// 获取当前连接状态
    pub async fn get_state(&self) -> ConnectionState {
        self.health.read().await.state
    }

    /// 更新连接状态
    pub async fn update_state(&self, new_state: ConnectionState) {
        let mut health = self.health.write().await;
        health.update_state(new_state);
        
        tracing::info!("Connection state updated to: {}", new_state);
    }

    /// 记录收到消息
    pub async fn record_message(&self) {
        self.message_counter.fetch_add(1, Ordering::Relaxed);
        let mut health = self.health.write().await;
        health.record_message();
    }

    /// 记录ping发送
    pub async fn record_ping(&self) {
        let mut health = self.health.write().await;
        health.record_ping();
    }

    /// 记录pong接收
    pub async fn record_pong(&self) {
        let mut health = self.health.write().await;
        health.record_pong();
    }

    /// 记录错误
    pub async fn record_error(&self) {
        self.error_counter.fetch_add(1, Ordering::Relaxed);
        let mut health = self.health.write().await;
        health.record_error();
    }

    /// 记录重连尝试
    pub async fn record_reconnect_attempt(&self) {
        let mut health = self.health.write().await;
        health.record_reconnect_attempt();
    }

    /// 获取连接健康状态的克隆
    pub async fn get_health(&self) -> ConnectionHealth {
        self.health.read().await.clone()
    }

    /// 检查连接是否健康
    pub async fn is_healthy(&self, max_rtt: Duration, max_consecutive_errors: u64) -> bool {
        let health = self.health.read().await;
        health.is_healthy(max_rtt, max_consecutive_errors)
    }

    /// 检查是否需要降级
    pub async fn should_degrade(&self, rtt_threshold: Duration, error_threshold: u64) -> bool {
        let health = self.health.read().await;
        health.should_degrade(rtt_threshold, error_threshold)
    }

    /// 评估连接健康状态并更新状态
    pub async fn evaluate_health(&self, config: &HealthConfig) {
        let current_health = self.get_health().await;
        let new_state = match current_health.state {
            ConnectionState::Connected => {
                if current_health.is_healthy(config.max_rtt, config.max_consecutive_errors) {
                    ConnectionState::Healthy
                } else if current_health.should_degrade(config.degrade_rtt_threshold, config.degrade_error_threshold) {
                    ConnectionState::Degraded
                } else {
                    ConnectionState::Connected
                }
            }
            ConnectionState::Healthy => {
                if current_health.should_degrade(config.degrade_rtt_threshold, config.degrade_error_threshold) {
                    ConnectionState::Degraded
                } else if !current_health.is_healthy(config.max_rtt, config.max_consecutive_errors) {
                    ConnectionState::Connected
                } else {
                    ConnectionState::Healthy
                }
            }
            ConnectionState::Degraded => {
                if current_health.is_healthy(config.max_rtt, config.max_consecutive_errors) {
                    ConnectionState::Healthy
                } else {
                    ConnectionState::Degraded
                }
            }
            other => other,
        };

        if new_state != current_health.state {
            self.update_state(new_state).await;
        }
    }

    /// 重置健康状态
    pub async fn reset_health(&self) {
        let mut health = self.health.write().await;
        *health = ConnectionHealth::default();
        self.message_counter.store(0, Ordering::Relaxed);
        self.error_counter.store(0, Ordering::Relaxed);
    }

    /// 获取消息计数
    pub fn get_message_count(&self) -> u64 {
        self.message_counter.load(Ordering::Relaxed)
    }

    /// 获取错误计数
    pub fn get_error_count(&self) -> u64 {
        self.error_counter.load(Ordering::Relaxed)
    }
}

impl Default for ConnectionManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 健康检查配置
#[derive(Debug, Clone)]
pub struct HealthConfig {
    /// 最大允许的RTT
    pub max_rtt: Duration,
    /// 最大连续错误数
    pub max_consecutive_errors: u64,
    /// 降级RTT阈值
    pub degrade_rtt_threshold: Duration,
    /// 降级错误阈值
    pub degrade_error_threshold: u64,
    /// 健康检查间隔
    pub check_interval: Duration,
    /// 消息超时时间
    pub message_timeout: Duration,
}

impl Default for HealthConfig {
    fn default() -> Self {
        Self {
            max_rtt: Duration::from_millis(1000),
            max_consecutive_errors: 5,
            degrade_rtt_threshold: Duration::from_millis(500),
            degrade_error_threshold: 3,
            check_interval: Duration::from_secs(10),
            message_timeout: Duration::from_secs(30),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_connection_state_display() {
        assert_eq!(ConnectionState::Disconnected.to_string(), "Disconnected");
        assert_eq!(ConnectionState::Connected.to_string(), "Connected");
        assert_eq!(ConnectionState::Healthy.to_string(), "Healthy");
    }

    #[test]
    fn test_connection_health_default() {
        let health = ConnectionHealth::default();
        assert_eq!(health.state, ConnectionState::Disconnected);
        assert_eq!(health.error_count, 0);
        assert_eq!(health.consecutive_errors, 0);
        assert_eq!(health.total_messages, 0);
    }

    #[test]
    fn test_connection_health_update_state() {
        let mut health = ConnectionHealth::default();
        health.update_state(ConnectionState::Connected);
        
        assert_eq!(health.state, ConnectionState::Connected);
        assert!(health.connected_at.is_some());
        assert_eq!(health.consecutive_errors, 0);
    }

    #[test]
    fn test_connection_health_record_message() {
        let mut health = ConnectionHealth::default();
        health.record_message();
        
        assert_eq!(health.total_messages, 1);
        assert!(health.last_message_at.is_some());
    }

    #[test]
    fn test_connection_health_record_error() {
        let mut health = ConnectionHealth::default();
        health.record_error();
        
        assert_eq!(health.error_count, 1);
        assert_eq!(health.consecutive_errors, 1);
    }

    #[test]
    fn test_connection_health_rtt_calculation() {
        let mut health = ConnectionHealth::default();
        
        health.add_rtt_sample(Duration::from_millis(100));
        health.add_rtt_sample(Duration::from_millis(200));
        
        assert_eq!(health.recent_rtt_samples.len(), 2);
        assert_eq!(health.average_rtt, Some(Duration::from_millis(150)));
    }

    #[test]
    fn test_connection_health_is_healthy() {
        let mut health = ConnectionHealth::default();
        health.update_state(ConnectionState::Connected);
        health.add_rtt_sample(Duration::from_millis(100));
        
        assert!(health.is_healthy(Duration::from_millis(500), 5));
        
        health.consecutive_errors = 10;
        assert!(!health.is_healthy(Duration::from_millis(500), 5));
    }

    #[tokio::test]
    async fn test_connection_manager_state_updates() {
        let manager = ConnectionManager::new();
        
        assert_eq!(manager.get_state().await, ConnectionState::Disconnected);
        
        manager.update_state(ConnectionState::Connected).await;
        assert_eq!(manager.get_state().await, ConnectionState::Connected);
    }

    #[tokio::test]
    async fn test_connection_manager_message_recording() {
        let manager = ConnectionManager::new();
        
        manager.record_message().await;
        manager.record_message().await;
        
        assert_eq!(manager.get_message_count(), 2);
        
        let health = manager.get_health().await;
        assert_eq!(health.total_messages, 2);
    }

    #[tokio::test]
    async fn test_connection_manager_error_recording() {
        let manager = ConnectionManager::new();
        
        manager.record_error().await;
        manager.record_error().await;
        
        assert_eq!(manager.get_error_count(), 2);
        
        let health = manager.get_health().await;
        assert_eq!(health.error_count, 2);
        assert_eq!(health.consecutive_errors, 2);
    }

    #[tokio::test]
    async fn test_connection_manager_health_evaluation() {
        let manager = ConnectionManager::new();
        let config = HealthConfig::default();
        
        manager.update_state(ConnectionState::Connected).await;
        manager.evaluate_health(&config).await;
        
        assert_eq!(manager.get_state().await, ConnectionState::Healthy);
    }

    #[tokio::test]
    async fn test_connection_manager_reset() {
        let manager = ConnectionManager::new();
        
        manager.record_message().await;
        manager.record_error().await;
        manager.update_state(ConnectionState::Connected).await;
        
        manager.reset_health().await;
        
        assert_eq!(manager.get_state().await, ConnectionState::Disconnected);
        assert_eq!(manager.get_message_count(), 0);
        assert_eq!(manager.get_error_count(), 0);
    }
}