use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, mpsc};
use tokio::time::{interval, sleep};

use crate::connection::{ConnectionManager, ConnectionState};
use crate::error::ChainListenerError;

/// 断线检测配置
#[derive(Debug, Clone)]
pub struct DisconnectionConfig {
    /// 是否启用断线检测
    pub enabled: bool,
    /// 消息超时阈值
    pub message_timeout: Duration,
    /// 检测间隔
    pub check_interval: Duration,
    /// ping响应超时阈值
    pub ping_response_timeout: Duration,
    /// 连续超时次数阈值
    pub consecutive_timeout_threshold: u32,
    /// 网络错误阈值
    pub network_error_threshold: u32,
    /// 检测延迟（启动后多久开始检测）
    pub detection_delay: Duration,
}

impl Default for DisconnectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            message_timeout: Duration::from_secs(60),
            check_interval: Duration::from_secs(10),
            ping_response_timeout: Duration::from_secs(30),
            consecutive_timeout_threshold: 3,
            network_error_threshold: 5,
            detection_delay: Duration::from_secs(30),
        }
    }
}

impl DisconnectionConfig {
    /// 创建新的断线检测配置
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置是否启用
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 设置消息超时
    pub fn with_message_timeout(mut self, timeout: Duration) -> Self {
        self.message_timeout = timeout;
        self
    }

    /// 设置检测间隔
    pub fn with_check_interval(mut self, interval: Duration) -> Self {
        self.check_interval = interval;
        self
    }

    /// 设置ping响应超时
    pub fn with_ping_response_timeout(mut self, timeout: Duration) -> Self {
        self.ping_response_timeout = timeout;
        self
    }

    /// 设置连续超时阈值
    pub fn with_consecutive_timeout_threshold(mut self, threshold: u32) -> Self {
        self.consecutive_timeout_threshold = threshold;
        self
    }

    /// 设置网络错误阈值
    pub fn with_network_error_threshold(mut self, threshold: u32) -> Self {
        self.network_error_threshold = threshold;
        self
    }

    /// 设置检测延迟
    pub fn with_detection_delay(mut self, delay: Duration) -> Self {
        self.detection_delay = delay;
        self
    }
}

/// 断线检测器
#[derive(Clone)]
pub struct DisconnectionDetector {
    config: DisconnectionConfig,
    connection_manager: Arc<ConnectionManager>,
    consecutive_timeouts: Arc<RwLock<u32>>,
    network_errors: Arc<RwLock<u32>>,
    last_check_time: Arc<RwLock<Option<Instant>>>,
    detection_start_time: Option<Instant>,
    shutdown_tx: Option<mpsc::UnboundedSender<()>>,
    disconnect_callback: Option<Arc<dyn Fn() -> Result<(), ChainListenerError> + Send + Sync>>,
}

impl DisconnectionDetector {
    /// 创建新的断线检测器
    pub fn new(config: DisconnectionConfig, connection_manager: Arc<ConnectionManager>) -> Self {
        Self {
            config,
            connection_manager,
            consecutive_timeouts: Arc::new(RwLock::new(0)),
            network_errors: Arc::new(RwLock::new(0)),
            last_check_time: Arc::new(RwLock::new(None)),
            detection_start_time: None,
            shutdown_tx: None,
            disconnect_callback: None,
        }
    }

    /// 设置断线回调
    pub fn with_disconnect_callback<F>(mut self, callback: F) -> Self
    where
        F: Fn() -> Result<(), ChainListenerError> + Send + Sync + 'static,
    {
        self.disconnect_callback = Some(Arc::new(callback));
        self
    }

    /// 启动断线检测
    pub async fn start_detection(&mut self) -> Result<(), ChainListenerError> {
        if !self.config.enabled {
            tracing::info!("Disconnection detection is disabled");
            return Ok(());
        }

        self.detection_start_time = Some(Instant::now());
        
        let (shutdown_tx, mut shutdown_rx) = mpsc::unbounded_channel();
        self.shutdown_tx = Some(shutdown_tx);

        let config = self.config.clone();
        let connection_manager = self.connection_manager.clone();
        let consecutive_timeouts = self.consecutive_timeouts.clone();
        let network_errors = self.network_errors.clone();
        let last_check_time = self.last_check_time.clone();
        let detection_start_time = self.detection_start_time;

        // 启动检测任务
        let detection_task = tokio::spawn(async move {
            // 等待检测延迟
            if let Some(start_time) = detection_start_time {
                let elapsed = Instant::now().duration_since(start_time);
                if elapsed < config.detection_delay {
                    let remaining = config.detection_delay - elapsed;
                    tracing::info!("Waiting {:?} before starting disconnection detection", remaining);
                    sleep(remaining).await;
                }
            }

            let mut check_interval = interval(config.check_interval);
            tracing::info!("Disconnection detection started");

            loop {
                tokio::select! {
                    _ = check_interval.tick() => {
                        if let Err(e) = Self::check_for_disconnection(
                            &config,
                            &connection_manager,
                            &consecutive_timeouts,
                            &network_errors,
                            &last_check_time,
                        ).await {
                            tracing::error!("Error during disconnection check: {}", e);
                        }
                    }
                    _ = shutdown_rx.recv() => {
                        tracing::info!("Disconnection detector shutting down");
                        break;
                    }
                }
            }
        });

        let _ = detection_task.await;
        Ok(())
    }

    /// 检查是否发生断线
    async fn check_for_disconnection(
        config: &DisconnectionConfig,
        connection_manager: &ConnectionManager,
        consecutive_timeouts: &Arc<RwLock<u32>>,
        network_errors: &Arc<RwLock<u32>>,
        last_check_time: &Arc<RwLock<Option<Instant>>>,
    ) -> Result<(), ChainListenerError> {
        let current_time = Instant::now();
        let connection_health = connection_manager.get_health().await;
        let current_state = connection_health.state;

        // 更新检查时间
        {
            let mut last_check = last_check_time.write().await;
            *last_check = Some(current_time);
        }

        // 只在连接状态下进行检测
        if !matches!(current_state, 
            ConnectionState::Connected | 
            ConnectionState::Healthy | 
            ConnectionState::Degraded
        ) {
            return Ok(());
        }

        tracing::debug!("Checking for disconnection. Current state: {}", current_state);

        // 检查消息超时
        let message_timeout_detected = if let Some(last_message_time) = connection_health.last_message_at {
            let time_since_last_message = current_time.duration_since(last_message_time);
            
            if time_since_last_message > config.message_timeout {
                tracing::warn!("Message timeout detected: no messages for {:?}", time_since_last_message);
                true
            } else {
                false
            }
        } else {
            // 如果从未收到消息，检查连接持续时间
            if let Some(connected_time) = connection_health.connected_at {
                let connection_duration = current_time.duration_since(connected_time);
                if connection_duration > config.message_timeout {
                    tracing::warn!("No messages received since connection established {:?} ago", connection_duration);
                    true
                } else {
                    false
                }
            } else {
                false
            }
        };

        // 检查ping响应超时
        let ping_timeout_detected = if let (Some(last_ping), Some(last_pong)) = 
            (connection_health.last_ping_at, connection_health.last_pong_at) {
            
            if last_ping > last_pong {
                let ping_response_time = current_time.duration_since(last_ping);
                if ping_response_time > config.ping_response_timeout {
                    tracing::warn!("Ping response timeout: waiting {:?} for pong", ping_response_time);
                    true
                } else {
                    false
                }
            } else {
                false
            }
        } else if let Some(last_ping) = connection_health.last_ping_at {
            // 有ping但没有pong
            let ping_response_time = current_time.duration_since(last_ping);
            if ping_response_time > config.ping_response_timeout {
                tracing::warn!("No pong received for ping sent {:?} ago", ping_response_time);
                true
            } else {
                false
            }
        } else {
            false
        };

        // 处理超时检测结果
        if message_timeout_detected || ping_timeout_detected {
            let mut timeouts = consecutive_timeouts.write().await;
            *timeouts += 1;
            
            tracing::warn!("Timeout detected (consecutive: {})", *timeouts);
            
            if *timeouts >= config.consecutive_timeout_threshold {
                tracing::error!("Consecutive timeout threshold reached ({}), marking as disconnected", *timeouts);
                connection_manager.update_state(ConnectionState::Disconnected).await;
                return Self::handle_disconnection(connection_manager).await;
            }
        } else {
            // 重置连续超时计数
            let mut timeouts = consecutive_timeouts.write().await;
            if *timeouts > 0 {
                tracing::debug!("Resetting consecutive timeout count");
                *timeouts = 0;
            }
        }

        // 检查网络错误
        if connection_health.consecutive_errors > 0 {
            let mut errors = network_errors.write().await;
            *errors += 1;
            
            if *errors >= config.network_error_threshold {
                tracing::error!("Network error threshold reached ({}), marking as disconnected", *errors);
                connection_manager.update_state(ConnectionState::Disconnected).await;
                return Self::handle_disconnection(connection_manager).await;
            }
        } else {
            // 重置网络错误计数
            let mut errors = network_errors.write().await;
            if *errors > 0 {
                tracing::debug!("Resetting network error count");
                *errors = 0;
            }
        }

        Ok(())
    }

    /// 处理断线事件
    async fn handle_disconnection(connection_manager: &ConnectionManager) -> Result<(), ChainListenerError> {
        tracing::error!("Disconnection detected, connection will be marked as disconnected");
        
        // 记录断线时间
        connection_manager.record_error().await;
        
        // 更新状态为断开连接
        connection_manager.update_state(ConnectionState::Disconnected).await;
        
        Ok(())
    }

    /// 停止断线检测
    pub async fn stop_detection(&mut self) {
        if let Some(shutdown_tx) = self.shutdown_tx.take() {
            let _ = shutdown_tx.send(());
            tracing::info!("Disconnection detection stopped");
        }
    }

    /// 重置检测状态
    pub async fn reset(&self) {
        let mut timeouts = self.consecutive_timeouts.write().await;
        *timeouts = 0;
        
        let mut errors = self.network_errors.write().await;
        *errors = 0;
        
        let mut last_check = self.last_check_time.write().await;
        *last_check = None;
        
        tracing::info!("Disconnection detector state reset");
    }

    /// 获取检测统计信息
    pub async fn get_stats(&self) -> DisconnectionStats {
        let consecutive_timeouts = *self.consecutive_timeouts.read().await;
        let network_errors = *self.network_errors.read().await;
        let last_check_time = *self.last_check_time.read().await;
        
        DisconnectionStats {
            consecutive_timeouts,
            network_errors,
            last_check_time,
            detection_start_time: self.detection_start_time,
            is_running: self.is_running(),
        }
    }

    /// 检查检测器是否运行中
    pub fn is_running(&self) -> bool {
        self.shutdown_tx.is_some()
    }

    /// 手动触发断线检测
    pub async fn manual_check(&self) -> Result<(), ChainListenerError> {
        if !self.config.enabled {
            return Err(ChainListenerError::Config("Disconnection detection is disabled".to_string()));
        }

        Self::check_for_disconnection(
            &self.config,
            &self.connection_manager,
            &self.consecutive_timeouts,
            &self.network_errors,
            &self.last_check_time,
        ).await
    }

    /// 获取配置的克隆
    pub fn get_config(&self) -> DisconnectionConfig {
        self.config.clone()
    }
}

/// 断线检测统计信息
#[derive(Debug, Clone)]
pub struct DisconnectionStats {
    /// 连续超时次数
    pub consecutive_timeouts: u32,
    /// 网络错误次数
    pub network_errors: u32,
    /// 最后检查时间
    pub last_check_time: Option<Instant>,
    /// 检测开始时间
    pub detection_start_time: Option<Instant>,
    /// 是否正在运行
    pub is_running: bool,
}

/// 断线检测构建器
pub struct DisconnectionDetectorBuilder {
    config: DisconnectionConfig,
    disconnect_callback: Option<Arc<dyn Fn() -> Result<(), ChainListenerError> + Send + Sync>>,
}

impl DisconnectionDetectorBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: DisconnectionConfig::default(),
            disconnect_callback: None,
        }
    }

    /// 设置快速检测（适合实时交易）
    pub fn fast_detection(mut self) -> Self {
        self.config.message_timeout = Duration::from_secs(30);
        self.config.check_interval = Duration::from_secs(5);
        self.config.ping_response_timeout = Duration::from_secs(15);
        self.config.consecutive_timeout_threshold = 2;
        self.config.network_error_threshold = 3;
        self.config.detection_delay = Duration::from_secs(10);
        self
    }

    /// 设置标准检测
    pub fn standard_detection(mut self) -> Self {
        self.config = DisconnectionConfig::default();
        self
    }

    /// 设置宽松检测（适合不稳定网络）
    pub fn relaxed_detection(mut self) -> Self {
        self.config.message_timeout = Duration::from_secs(120);
        self.config.check_interval = Duration::from_secs(20);
        self.config.ping_response_timeout = Duration::from_secs(60);
        self.config.consecutive_timeout_threshold = 5;
        self.config.network_error_threshold = 10;
        self.config.detection_delay = Duration::from_secs(60);
        self
    }

    /// 设置断线回调
    pub fn with_disconnect_callback<F>(mut self, callback: F) -> Self
    where
        F: Fn() -> Result<(), ChainListenerError> + Send + Sync + 'static,
    {
        self.disconnect_callback = Some(Arc::new(callback));
        self
    }

    /// 构建检测器
    pub fn build(self, connection_manager: Arc<ConnectionManager>) -> DisconnectionDetector {
        let mut detector = DisconnectionDetector::new(self.config, connection_manager);
        
        if let Some(callback) = self.disconnect_callback {
            detector.disconnect_callback = Some(callback);
        }
        
        detector
    }
}

impl Default for DisconnectionDetectorBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::connection::ConnectionManager;
    use std::sync::Arc;

    #[test]
    fn test_disconnection_config_default() {
        let config = DisconnectionConfig::default();
        assert!(config.enabled);
        assert_eq!(config.message_timeout, Duration::from_secs(60));
        assert_eq!(config.check_interval, Duration::from_secs(10));
        assert_eq!(config.consecutive_timeout_threshold, 3);
    }

    #[test]
    fn test_disconnection_config_builder() {
        let config = DisconnectionConfig::new()
            .with_enabled(false)
            .with_message_timeout(Duration::from_secs(30))
            .with_consecutive_timeout_threshold(5);
        
        assert!(!config.enabled);
        assert_eq!(config.message_timeout, Duration::from_secs(30));
        assert_eq!(config.consecutive_timeout_threshold, 5);
    }

    #[tokio::test]
    async fn test_disconnection_detector_creation() {
        let config = DisconnectionConfig::default();
        let manager = Arc::new(ConnectionManager::new());
        let detector = DisconnectionDetector::new(config, manager);
        
        assert!(!detector.is_running());
        
        let stats = detector.get_stats().await;
        assert_eq!(stats.consecutive_timeouts, 0);
        assert_eq!(stats.network_errors, 0);
        assert!(!stats.is_running);
    }

    #[tokio::test]
    async fn test_disconnection_detector_reset() {
        let config = DisconnectionConfig::default();
        let manager = Arc::new(ConnectionManager::new());
        let detector = DisconnectionDetector::new(config, manager);
        
        // 模拟一些错误状态
        {
            let mut timeouts = detector.consecutive_timeouts.write().await;
            *timeouts = 5;
        }
        {
            let mut errors = detector.network_errors.write().await;
            *errors = 3;
        }
        
        detector.reset().await;
        
        let stats = detector.get_stats().await;
        assert_eq!(stats.consecutive_timeouts, 0);
        assert_eq!(stats.network_errors, 0);
    }

    #[tokio::test]
    async fn test_disconnection_detector_disabled() {
        let config = DisconnectionConfig::new().with_enabled(false);
        let manager = Arc::new(ConnectionManager::new());
        let mut detector = DisconnectionDetector::new(config, manager);
        
        let result = detector.start_detection().await;
        assert!(result.is_ok());
        assert!(!detector.is_running());
        
        let manual_check_result = detector.manual_check().await;
        assert!(manual_check_result.is_err());
    }

    #[test]
    fn test_disconnection_detector_builder_presets() {
        let manager = Arc::new(ConnectionManager::new());
        
        let fast_detector = DisconnectionDetectorBuilder::new()
            .fast_detection()
            .build(manager.clone());
        
        assert_eq!(fast_detector.config.message_timeout, Duration::from_secs(30));
        assert_eq!(fast_detector.config.consecutive_timeout_threshold, 2);
        
        let relaxed_detector = DisconnectionDetectorBuilder::new()
            .relaxed_detection()
            .build(manager);
        
        assert_eq!(relaxed_detector.config.message_timeout, Duration::from_secs(120));
        assert_eq!(relaxed_detector.config.consecutive_timeout_threshold, 5);
    }

    #[tokio::test]
    async fn test_disconnection_stats() {
        let config = DisconnectionConfig::default();
        let manager = Arc::new(ConnectionManager::new());
        let detector = DisconnectionDetector::new(config, manager);
        
        let stats = detector.get_stats().await;
        
        assert_eq!(stats.consecutive_timeouts, 0);
        assert_eq!(stats.network_errors, 0);
        assert!(stats.last_check_time.is_none());
        assert!(stats.detection_start_time.is_none());
        assert!(!stats.is_running);
    }

    #[tokio::test]
    async fn test_disconnection_detector_callback() {
        let manager = Arc::new(ConnectionManager::new());
        
        let callback_called = Arc::new(std::sync::atomic::AtomicBool::new(false));
        let callback_called_clone = callback_called.clone();
        
        let _detector = DisconnectionDetectorBuilder::new()
            .with_disconnect_callback(move || {
                callback_called_clone.store(true, std::sync::atomic::Ordering::Relaxed);
                Ok(())
            })
            .build(manager);
        
        // 验证回调被设置
        assert!(!callback_called.load(std::sync::atomic::Ordering::Relaxed));
    }
}