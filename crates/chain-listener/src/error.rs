use thiserror::Error;
use yellowstone_grpc_client::{GeyserGrpcBuilderError, GeyserGrpcClientError};

/// 链监听器错误类型
#[derive(Error, Debug)]
pub enum ChainListenerError {
    #[error("Failed to build Yellowstone gRPC client: {0}")]
    GrpcBuilder(#[from] GeyserGrpcBuilderError),

    #[error("gRPC client error: {0}")]
    GrpcClient(#[from] GeyserGrpcClientError),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Connection error: {0}")]
    Connection(String),

    #[error("Subscription error: {0}")]
    Subscription(String),

    #[error("Message processing error: {0}")]
    MessageProcessing(String),

    // 新增的监控和重连相关错误
    #[error("Health monitoring error: {0}")]
    HealthMonitoring(String),

    #[error("Reconnection failed: {0}")]
    ReconnectionFailed(String),

    #[error("Disconnection detected: {0}")]
    DisconnectionDetected(String),

    #[error("Ping timeout: expected response within {expected:?}, but got none after {actual:?}")]
    PingTimeout {
        expected: std::time::Duration,
        actual: std::time::Duration,
    },

    #[error("Connection state error: cannot perform operation in state {current_state}")]
    InvalidConnectionState { current_state: String },

    #[error("Health check failed: {reason}")]
    HealthCheckFailed { reason: String },

    #[error("Maximum reconnection attempts exceeded: {attempts} attempts failed")]
    MaxReconnectAttemptsExceeded { attempts: u32 },

    #[error("Connection timeout: operation timed out after {timeout:?}")]
    ConnectionTimeout { timeout: std::time::Duration },

    #[error("Network instability detected: {details}")]
    NetworkInstability { details: String },

    #[error("Monitoring task error: {task} failed with {error}")]
    MonitoringTaskError { task: String, error: String },
}

/// 链监听器结果类型
pub type ChainListenerResult<T> = Result<T, ChainListenerError>;