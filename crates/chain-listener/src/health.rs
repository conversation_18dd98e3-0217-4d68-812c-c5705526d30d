use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, mpsc};
use tokio::time::{interval, timeout};
use futures_util::{Sink, SinkExt};
use futures::channel::mpsc::SendError;
use yellowstone_grpc_proto::geyser::{SubscribeRequestPing, SubscribeRequest};

use crate::connection::{ConnectionManager, ConnectionState, HealthConfig};
use crate::error::ChainListenerError;

/// 健康检查配置
#[derive(Debug, Clone)]
pub struct HealthCheckConfig {
    /// ping间隔
    pub ping_interval: Duration,
    /// ping超时时间
    pub ping_timeout: Duration,
    /// 健康评估间隔
    pub evaluation_interval: Duration,
    /// RTT警告阈值
    pub rtt_warning_threshold: Duration,
    /// RTT错误阈值
    pub rtt_error_threshold: Duration,
    /// 连续失败ping次数阈值
    pub max_consecutive_ping_failures: u32,
    /// 消息超时阈值
    pub message_timeout_threshold: Duration,
    /// 是否启用健康检查
    pub enabled: bool,
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            ping_interval: Duration::from_secs(30),
            ping_timeout: Duration::from_secs(10),
            evaluation_interval: Duration::from_secs(15),
            rtt_warning_threshold: Duration::from_millis(500),
            rtt_error_threshold: Duration::from_millis(1000),
            max_consecutive_ping_failures: 3,
            message_timeout_threshold: Duration::from_secs(60),
            enabled: true,
        }
    }
}

impl HealthCheckConfig {
    /// 创建新的健康检查配置
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置是否启用健康检查
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 设置ping间隔
    pub fn with_ping_interval(mut self, interval: Duration) -> Self {
        self.ping_interval = interval;
        self
    }

    /// 设置ping超时
    pub fn with_ping_timeout(mut self, timeout_duration: Duration) -> Self {
        self.ping_timeout = timeout_duration;
        self
    }

    /// 设置健康评估间隔
    pub fn with_evaluation_interval(mut self, interval: Duration) -> Self {
        self.evaluation_interval = interval;
        self
    }

    /// 设置RTT阈值
    pub fn with_rtt_thresholds(mut self, warning: Duration, error: Duration) -> Self {
        self.rtt_warning_threshold = warning;
        self.rtt_error_threshold = error;
        self
    }

    /// 设置最大连续ping失败次数
    pub fn with_max_ping_failures(mut self, max_failures: u32) -> Self {
        self.max_consecutive_ping_failures = max_failures;
        self
    }

    /// 设置消息超时阈值
    pub fn with_message_timeout(mut self, timeout_duration: Duration) -> Self {
        self.message_timeout_threshold = timeout_duration;
        self
    }
}

/// 健康监控器
#[derive(Clone)]
pub struct HealthMonitor {
    pub config: HealthCheckConfig,
    connection_manager: Arc<ConnectionManager>,
    ping_counter: Arc<RwLock<u64>>,
    consecutive_ping_failures: Arc<RwLock<u32>>,
    last_ping_sent: Arc<RwLock<Option<Instant>>>,
    shutdown_tx: Option<mpsc::UnboundedSender<()>>,
}

impl HealthMonitor {
    /// 创建新的健康监控器
    pub fn new(config: HealthCheckConfig, connection_manager: Arc<ConnectionManager>) -> Self {
        Self {
            config,
            connection_manager,
            ping_counter: Arc::new(RwLock::new(0)),
            consecutive_ping_failures: Arc::new(RwLock::new(0)),
            last_ping_sent: Arc::new(RwLock::new(None)),
            shutdown_tx: None,
        }
    }

    /// 启动健康监控（被动模式，只做健康评估，ping由外部处理）
    pub async fn start_passive_monitoring(&mut self) -> Result<(), ChainListenerError> {
        if !self.config.enabled {
            tracing::info!("Health monitoring is disabled");
            return Ok(());
        }

        let (shutdown_tx, mut shutdown_rx) = mpsc::unbounded_channel();
        self.shutdown_tx = Some(shutdown_tx);

        let config = self.config.clone();
        let connection_manager = self.connection_manager.clone();

        // 只启动健康评估任务
        let health_eval_task = {
            tokio::spawn(async move {
                let mut eval_interval = interval(config.evaluation_interval);
                
                loop {
                    tokio::select! {
                        _ = eval_interval.tick() => {
                            Self::evaluate_health(&config, &connection_manager).await;
                        }
                        _ = shutdown_rx.recv() => {
                            tracing::info!("Health monitor evaluation task shutting down");
                            break;
                        }
                    }
                }
            })
        };

        // 等待任务完成
        let _ = health_eval_task.await;
        
        Ok(())
    }

    /// 发送ping消息
    async fn send_ping<T>(
        sender: &mut T,
        config: &HealthCheckConfig,
        connection_manager: &ConnectionManager,
        ping_counter: &Arc<RwLock<u64>>,
        consecutive_ping_failures: &Arc<RwLock<u32>>,
        last_ping_sent: &Arc<RwLock<Option<Instant>>>,
    ) -> Result<(), ChainListenerError>
    where
        T: Sink<SubscribeRequest, Error = SendError> + std::marker::Unpin,
    {
        let state = connection_manager.get_state().await;
        
        // 只在连接状态下发送ping
        if !matches!(state, ConnectionState::Connected | ConnectionState::Healthy | ConnectionState::Degraded) {
            return Ok(());
        }

        let ping_id = {
            let mut counter = ping_counter.write().await;
            *counter += 1;
            *counter
        };

        let ping_request = SubscribeRequest {
            ping: Some(SubscribeRequestPing { id: ping_id as i32 }),
            ..Default::default()
        };

        tracing::debug!("Sending ping with id: {}", ping_id);
        
        // 记录ping发送时间
        {
            let mut last_sent = last_ping_sent.write().await;
            *last_sent = Some(Instant::now());
        }
        
        connection_manager.record_ping().await;

        // 发送ping并设置超时
        match timeout(config.ping_timeout, sender.send(ping_request)).await {
            Ok(Ok(())) => {
                // ping发送成功，重置连续失败计数
                let mut failures = consecutive_ping_failures.write().await;
                *failures = 0;
                tracing::debug!("Ping {} sent successfully", ping_id);
            }
            Ok(Err(e)) => {
                // 发送失败
                let mut failures = consecutive_ping_failures.write().await;
                *failures += 1;
                connection_manager.record_error().await;
                
                tracing::warn!("Failed to send ping {}: {}", ping_id, e);
                
                if *failures >= config.max_consecutive_ping_failures {
                    tracing::error!("Too many consecutive ping failures ({}), marking connection as failed", *failures);
                    connection_manager.update_state(ConnectionState::Failed).await;
                }
                
                return Err(ChainListenerError::MessageProcessing(format!("Ping send failed: {}", e)));
            }
            Err(_) => {
                // 超时
                let mut failures = consecutive_ping_failures.write().await;
                *failures += 1;
                connection_manager.record_error().await;
                
                tracing::warn!("Ping {} timed out after {:?}", ping_id, config.ping_timeout);
                
                if *failures >= config.max_consecutive_ping_failures {
                    tracing::error!("Too many consecutive ping timeouts ({}), marking connection as failed", *failures);
                    connection_manager.update_state(ConnectionState::Failed).await;
                }
                
                return Err(ChainListenerError::MessageProcessing("Ping timeout".to_string()));
            }
        }

        Ok(())
    }

    /// 评估连接健康状态
    async fn evaluate_health(config: &HealthCheckConfig, connection_manager: &ConnectionManager) {
        let health = connection_manager.get_health().await;
        let current_state = health.state;

        tracing::debug!("Evaluating connection health. Current state: {}", current_state);

        // 检查消息超时
        if let Some(last_message_time) = health.last_message_at {
            let time_since_last_message = Instant::now().duration_since(last_message_time);
            if time_since_last_message > config.message_timeout_threshold {
                tracing::warn!("No messages received for {:?}, connection may be stale", time_since_last_message);
                
                if matches!(current_state, ConnectionState::Healthy) {
                    connection_manager.update_state(ConnectionState::Degraded).await;
                }
                return;
            }
        }

        // 检查RTT性能
        if let Some(avg_rtt) = health.average_rtt {
            if avg_rtt > config.rtt_error_threshold {
                tracing::warn!("Average RTT ({:?}) exceeds error threshold ({:?})", 
                    avg_rtt, config.rtt_error_threshold);
                
                if matches!(current_state, ConnectionState::Healthy | ConnectionState::Connected) {
                    connection_manager.update_state(ConnectionState::Degraded).await;
                }
                return;
            } else if avg_rtt > config.rtt_warning_threshold {
                tracing::warn!("Average RTT ({:?}) exceeds warning threshold ({:?})", 
                    avg_rtt, config.rtt_warning_threshold);
                
                if matches!(current_state, ConnectionState::Healthy) {
                    connection_manager.update_state(ConnectionState::Connected).await;
                }
                return;
            }
        }

        // 检查连续错误
        if health.consecutive_errors > 0 {
            if health.consecutive_errors >= config.max_consecutive_ping_failures as u64 {
                tracing::error!("Too many consecutive errors ({}), marking as failed", health.consecutive_errors);
                connection_manager.update_state(ConnectionState::Failed).await;
                return;
            } else if health.consecutive_errors > 1 {
                if matches!(current_state, ConnectionState::Healthy) {
                    connection_manager.update_state(ConnectionState::Degraded).await;
                }
                return;
            }
        }

        // 如果所有检查都通过，且当前是连接状态，升级为健康状态
        if matches!(current_state, ConnectionState::Connected | ConnectionState::Degraded) 
            && health.consecutive_errors == 0 {
            let health_check_config = HealthConfig {
                max_rtt: config.rtt_error_threshold,
                max_consecutive_errors: config.max_consecutive_ping_failures as u64,
                degrade_rtt_threshold: config.rtt_warning_threshold,
                degrade_error_threshold: 1,
                check_interval: config.evaluation_interval,
                message_timeout: config.message_timeout_threshold,
            };
            
            connection_manager.evaluate_health(&health_check_config).await;
        }
    }

    /// 处理收到的pong消息
    pub async fn handle_pong(&self, pong_id: i32) {
        tracing::debug!("Received pong with id: {}", pong_id);
        
        // 记录pong接收
        self.connection_manager.record_pong().await;
        
        // 重置连续失败计数
        let mut failures = self.consecutive_ping_failures.write().await;
        *failures = 0;
        
        // 如果连接状态不健康，可能需要升级状态
        let current_state = self.connection_manager.get_state().await;
        if matches!(current_state, ConnectionState::Degraded | ConnectionState::Connected) {
            let health_config = HealthConfig {
                max_rtt: self.config.rtt_error_threshold,
                max_consecutive_errors: self.config.max_consecutive_ping_failures as u64,
                degrade_rtt_threshold: self.config.rtt_warning_threshold,
                degrade_error_threshold: 1,
                check_interval: self.config.evaluation_interval,
                message_timeout: self.config.message_timeout_threshold,
            };
            
            self.connection_manager.evaluate_health(&health_config).await;
        }
    }

    /// 停止健康监控
    pub async fn stop_monitoring(&mut self) {
        if let Some(shutdown_tx) = self.shutdown_tx.take() {
            let _ = shutdown_tx.send(());
            tracing::info!("Health monitoring stopped");
        }
    }

    /// 获取ping统计信息
    pub async fn get_ping_stats(&self) -> PingStats {
        let ping_count = *self.ping_counter.read().await;
        let consecutive_failures = *self.consecutive_ping_failures.read().await;
        let last_ping_sent = *self.last_ping_sent.read().await;
        
        PingStats {
            total_pings_sent: ping_count,
            consecutive_failures,
            last_ping_sent,
        }
    }

    /// 检查健康监控是否运行中
    pub fn is_monitoring(&self) -> bool {
        self.shutdown_tx.is_some()
    }
}

/// Ping统计信息
#[derive(Debug, Clone)]
pub struct PingStats {
    /// 总发送ping数
    pub total_pings_sent: u64,
    /// 连续失败次数
    pub consecutive_failures: u32,
    /// 最后ping发送时间
    pub last_ping_sent: Option<Instant>,
}

/// 健康监控构建器
pub struct HealthMonitorBuilder {
    config: HealthCheckConfig,
}

impl HealthMonitorBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: HealthCheckConfig::default(),
        }
    }

    /// 设置快速健康检查（适合高频交易）
    pub fn fast_monitoring(mut self) -> Self {
        self.config.ping_interval = Duration::from_secs(10);
        self.config.ping_timeout = Duration::from_secs(3);
        self.config.evaluation_interval = Duration::from_secs(5);
        self.config.rtt_warning_threshold = Duration::from_millis(200);
        self.config.rtt_error_threshold = Duration::from_millis(500);
        self.config.max_consecutive_ping_failures = 2;
        self.config.message_timeout_threshold = Duration::from_secs(20);
        self
    }

    /// 设置标准健康检查
    pub fn standard_monitoring(mut self) -> Self {
        self.config = HealthCheckConfig::default();
        self
    }

    /// 设置宽松健康检查（适合不稳定网络）
    pub fn relaxed_monitoring(mut self) -> Self {
        self.config.ping_interval = Duration::from_secs(60);
        self.config.ping_timeout = Duration::from_secs(20);
        self.config.evaluation_interval = Duration::from_secs(30);
        self.config.rtt_warning_threshold = Duration::from_millis(1000);
        self.config.rtt_error_threshold = Duration::from_millis(2000);
        self.config.max_consecutive_ping_failures = 5;
        self.config.message_timeout_threshold = Duration::from_secs(120);
        self
    }

    /// 构建健康监控器
    pub fn build(self, connection_manager: Arc<ConnectionManager>) -> HealthMonitor {
        HealthMonitor::new(self.config, connection_manager)
    }
}

impl Default for HealthMonitorBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::connection::ConnectionManager;
    use std::sync::Arc;
    use futures::channel::mpsc as futures_mpsc;

    #[test]
    fn test_health_check_config_default() {
        let config = HealthCheckConfig::default();
        assert!(config.enabled);
        assert_eq!(config.ping_interval, Duration::from_secs(30));
        assert_eq!(config.ping_timeout, Duration::from_secs(10));
        assert_eq!(config.max_consecutive_ping_failures, 3);
    }

    #[test]
    fn test_health_check_config_builder() {
        let config = HealthCheckConfig::new()
            .with_enabled(false)
            .with_ping_interval(Duration::from_secs(60))
            .with_ping_timeout(Duration::from_secs(5));
        
        assert!(!config.enabled);
        assert_eq!(config.ping_interval, Duration::from_secs(60));
        assert_eq!(config.ping_timeout, Duration::from_secs(5));
    }

    #[test]
    fn test_ping_stats_default() {
        let stats = PingStats {
            total_pings_sent: 0,
            consecutive_failures: 0,
            last_ping_sent: None,
        };
        
        assert_eq!(stats.total_pings_sent, 0);
        assert_eq!(stats.consecutive_failures, 0);
        assert!(stats.last_ping_sent.is_none());
    }

    #[tokio::test]
    async fn test_health_monitor_creation() {
        let config = HealthCheckConfig::default();
        let manager = Arc::new(ConnectionManager::new());
        let monitor = HealthMonitor::new(config, manager);
        
        assert!(!monitor.is_monitoring());
        
        let stats = monitor.get_ping_stats().await;
        assert_eq!(stats.total_pings_sent, 0);
        assert_eq!(stats.consecutive_failures, 0);
    }

    #[tokio::test]
    async fn test_health_monitor_builder_presets() {
        let manager = Arc::new(ConnectionManager::new());
        
        let fast_monitor = HealthMonitorBuilder::new()
            .fast_monitoring()
            .build(manager.clone());
        
        assert_eq!(fast_monitor.config.ping_interval, Duration::from_secs(10));
        assert_eq!(fast_monitor.config.max_consecutive_ping_failures, 2);
        
        let relaxed_monitor = HealthMonitorBuilder::new()
            .relaxed_monitoring()
            .build(manager);
        
        assert_eq!(relaxed_monitor.config.ping_interval, Duration::from_secs(60));
        assert_eq!(relaxed_monitor.config.max_consecutive_ping_failures, 5);
    }

    #[tokio::test]
    async fn test_health_monitor_pong_handling() {
        let config = HealthCheckConfig::default();
        let manager = Arc::new(ConnectionManager::new());
        let monitor = HealthMonitor::new(config, manager.clone());
        
        // 模拟一些错误
        manager.record_error().await;
        manager.record_error().await;
        
        let health_before = manager.get_health().await;
        assert_eq!(health_before.consecutive_errors, 2);
        
        // 处理pong应该重置连续失败计数
        monitor.handle_pong(1).await;
        
        let stats = monitor.get_ping_stats().await;
        assert_eq!(stats.consecutive_failures, 0);
    }

    #[tokio::test]
    async fn test_health_monitor_disabled() {
        let config = HealthCheckConfig::new().with_enabled(false);
        let manager = Arc::new(ConnectionManager::new());
        let mut monitor = HealthMonitor::new(config, manager);
        
        let (_tx, _rx): (futures_mpsc::UnboundedSender<()>, _) = futures_mpsc::unbounded();
        let result = monitor.start_passive_monitoring().await;
        
        assert!(result.is_ok());
        assert!(!monitor.is_monitoring());
    }
}