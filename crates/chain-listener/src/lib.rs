//! # Chain Listener
//!
//! 负责监听区块链数据的核心模块。
//! 基于Yellowstone gRPC协议接收Solana链上的实时交易数据。
//! 
//! ## 功能特性
//! 
//! - **基础连接功能**: 基于Yellowstone gRPC的可靠连接
//! - **智能重连机制**: 指数退避+随机抖动的智能重连算法
//! - **健康监控系统**: 实时ping/pong健康检查和性能监控
//! - **断线检测**: 基于消息超时的自动断线检测
//! - **连接状态管理**: 完整的连接生命周期状态跟踪
//! 
//! ## 使用示例
//! 
//! ### 基础用法（无监控）
//! ```rust,no_run
//! use chain_listener::{ChainListenerConfig, YellowstoneGrpcClient, SubscriptionConfig};
//! 
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let config = ChainListenerConfig::default();
//! let client = YellowstoneGrpcClient::new(config);
//! 
//! let subscription_config = SubscriptionConfig::default();
//! client.subscribe_with_config(subscription_config, |data| {
//!     println!("Received data: {:?}", data);
//!     Ok(())
//! }).await?;
//! # Ok(())
//! # }
//! ```
//! 
//! ### 带监控功能的用法
//! ```rust,no_run
//! use chain_listener::{ChainListenerConfig, YellowstoneGrpcClient, SubscriptionConfig};
//! 
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let config = ChainListenerConfig::default()
//!     .enable_fast_monitoring(); // 启用快速监控模式
//! 
//! let client = YellowstoneGrpcClient::new(config);
//! 
//! let subscription_config = SubscriptionConfig::default();
//! // 使用带监控的订阅方法
//! client.subscribe_with_monitoring(subscription_config, |data| {
//!     println!("Received data: {:?}", data);
//!     Ok(())
//! }).await?;
//! # Ok(())
//! # }
//! ```
//! 
//! ### 自定义监控配置
//! ```rust,no_run
//! use chain_listener::{
//!     ChainListenerConfig, YellowstoneGrpcClient, SubscriptionConfig,
//!     ReconnectConfigBuilder, HealthMonitorBuilder, DisconnectionDetectorBuilder
//! };
//! 
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let reconnect_config = ReconnectConfigBuilder::new()
//!     .fast_reconnect()
//!     .build();
//! 
//! let config = ChainListenerConfig::default()
//!     .with_reconnect_config(reconnect_config)
//!     .with_monitoring_enabled(true);
//! 
//! let client = YellowstoneGrpcClient::new(config);
//! # Ok(())
//! # }
//! ```

// 核心模块
pub mod client;
pub mod config;
pub mod error;

// 监控和重连模块
pub mod connection;
pub mod reconnect;
pub mod health;
pub mod disconnect;

// 重新导出主要类型，保持向后兼容性
pub use client::*;
pub use config::*;
pub use error::*;

// 导出监控相关类型
pub use connection::{ConnectionState, ConnectionHealth, ConnectionManager, HealthConfig};
pub use reconnect::{ReconnectConfig, ReconnectConfigBuilder, Reconnector, ReconnectStrategy};
pub use health::{HealthCheckConfig, HealthMonitor, HealthMonitorBuilder, PingStats};
pub use disconnect::{DisconnectionConfig, DisconnectionDetector, DisconnectionDetectorBuilder, DisconnectionStats};
