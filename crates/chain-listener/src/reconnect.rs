use std::sync::Arc;
use std::time::Duration;
use rand::Rng;
use tokio::time::{sleep, Instant};
use crate::connection::{ConnectionManager, ConnectionState};
use crate::error::ChainListenerError;

/// 重连配置
#[derive(Debug, Clone)]
pub struct ReconnectConfig {
    /// 是否启用重连
    pub enabled: bool,
    /// 初始重连延迟
    pub initial_delay: Duration,
    /// 最大重连延迟
    pub max_delay: Duration,
    /// 退避倍数
    pub backoff_multiplier: f64,
    /// 最大重连尝试次数（None表示无限制）
    pub max_attempts: Option<u32>,
    /// 抖动因子（0.0-1.0），用于随机化延迟
    pub jitter_factor: f64,
    /// 重连超时时间
    pub reconnect_timeout: Duration,
    /// 连接稳定时间（连接保持稳定此时间后重置重连计数）
    pub stable_connection_time: Duration,
}

impl Default for ReconnectConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            initial_delay: Duration::from_millis(1000),
            max_delay: Duration::from_secs(30),
            backoff_multiplier: 2.0,
            max_attempts: Some(10),
            jitter_factor: 0.1,
            reconnect_timeout: Duration::from_secs(30),
            stable_connection_time: Duration::from_secs(60),
        }
    }
}

impl ReconnectConfig {
    /// 创建新的重连配置
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置是否启用重连
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 设置初始延迟
    pub fn with_initial_delay(mut self, delay: Duration) -> Self {
        self.initial_delay = delay;
        self
    }

    /// 设置最大延迟
    pub fn with_max_delay(mut self, delay: Duration) -> Self {
        self.max_delay = delay;
        self
    }

    /// 设置退避倍数
    pub fn with_backoff_multiplier(mut self, multiplier: f64) -> Self {
        self.backoff_multiplier = multiplier;
        self
    }

    /// 设置最大重连尝试次数
    pub fn with_max_attempts(mut self, attempts: Option<u32>) -> Self {
        self.max_attempts = attempts;
        self
    }

    /// 设置抖动因子
    pub fn with_jitter_factor(mut self, factor: f64) -> Self {
        self.jitter_factor = factor.clamp(0.0, 1.0);
        self
    }

    /// 设置重连超时时间
    pub fn with_reconnect_timeout(mut self, timeout: Duration) -> Self {
        self.reconnect_timeout = timeout;
        self
    }

    /// 设置连接稳定时间
    pub fn with_stable_connection_time(mut self, time: Duration) -> Self {
        self.stable_connection_time = time;
        self
    }

    /// 计算下次重连延迟（指数退避 + 随机抖动）
    pub fn calculate_delay(&self, attempt: u32) -> Duration {
        if attempt == 0 {
            return self.initial_delay;
        }

        // 指数退避计算
        let base_delay = self.initial_delay.as_millis() as f64
            * self.backoff_multiplier.powi(attempt.saturating_sub(1) as i32);
        
        // 限制最大延迟
        let capped_delay = base_delay.min(self.max_delay.as_millis() as f64);
        
        // 添加随机抖动
        let jitter = if self.jitter_factor > 0.0 {
            let mut rng = rand::thread_rng();
            let jitter_range = capped_delay * self.jitter_factor;
            rng.gen_range(-jitter_range..jitter_range)
        } else {
            0.0
        };
        
        let final_delay = (capped_delay + jitter).max(0.0) as u64;
        Duration::from_millis(final_delay)
    }

    /// 检查是否应该继续重连
    pub fn should_continue(&self, attempt: u32) -> bool {
        if !self.enabled {
            return false;
        }
        
        match self.max_attempts {
            Some(max) => attempt < max,
            None => true,
        }
    }
}

/// 重连器 - 负责执行智能重连逻辑
#[derive(Clone)]
pub struct Reconnector {
    config: ReconnectConfig,
    connection_manager: Arc<ConnectionManager>,
    last_stable_connection: Option<Instant>,
    current_attempt: u32,
}

impl Reconnector {
    /// 创建新的重连器
    pub fn new(config: ReconnectConfig, connection_manager: Arc<ConnectionManager>) -> Self {
        Self {
            config,
            connection_manager,
            last_stable_connection: None,
            current_attempt: 0,
        }
    }

    /// 开始智能重连流程
    pub async fn smart_reconnect<F, Fut>(&mut self, reconnect_fn: F) -> Result<(), ChainListenerError>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = Result<(), ChainListenerError>>,
    {
        if !self.config.enabled {
            return Err(ChainListenerError::Connection("Reconnection is disabled".to_string()));
        }

        tracing::info!("Starting smart reconnect process");
        
        // 重置重连计数如果上次连接足够稳定
        self.reset_if_stable_connection().await;

        while self.config.should_continue(self.current_attempt) {
            self.current_attempt += 1;
            
            tracing::info!("Reconnect attempt {} of {:?}", 
                self.current_attempt, 
                self.config.max_attempts
            );

            // 更新连接状态为重连中
            self.connection_manager.update_state(ConnectionState::Reconnecting).await;
            self.connection_manager.record_reconnect_attempt().await;

            // 计算延迟时间
            let delay = self.config.calculate_delay(self.current_attempt);
            tracing::info!("Waiting {:?} before reconnect attempt", delay);
            
            sleep(delay).await;

            // 尝试重连
            match tokio::time::timeout(self.config.reconnect_timeout, reconnect_fn()).await {
                Ok(Ok(())) => {
                    tracing::info!("Reconnection successful after {} attempts", self.current_attempt);
                    
                    // 连接成功，重置计数器并记录稳定连接时间
                    self.current_attempt = 0;
                    self.last_stable_connection = Some(Instant::now());
                    self.connection_manager.update_state(ConnectionState::Connected).await;
                    
                    return Ok(());
                }
                Ok(Err(e)) => {
                    tracing::warn!("Reconnect attempt {} failed: {}", self.current_attempt, e);
                    self.connection_manager.record_error().await;
                }
                Err(_) => {
                    tracing::warn!("Reconnect attempt {} timed out after {:?}", 
                        self.current_attempt, 
                        self.config.reconnect_timeout
                    );
                    self.connection_manager.record_error().await;
                }
            }
        }

        // 所有重连尝试都失败了
        self.connection_manager.update_state(ConnectionState::Failed).await;
        let error_msg = format!(
            "All reconnection attempts failed. Attempted {} times.", 
            self.current_attempt
        );
        
        tracing::error!("{}", error_msg);
        Err(ChainListenerError::Connection(error_msg))
    }

    /// 如果上次连接足够稳定，重置重连计数
    async fn reset_if_stable_connection(&mut self) {
        if let Some(last_stable) = self.last_stable_connection {
            let elapsed = Instant::now().duration_since(last_stable);
            if elapsed >= self.config.stable_connection_time {
                tracing::info!("Previous connection was stable for {:?}, resetting reconnect counter", elapsed);
                self.current_attempt = 0;
            }
        }
    }

    /// 记录连接稳定
    pub fn mark_connection_stable(&mut self) {
        self.last_stable_connection = Some(Instant::now());
        self.current_attempt = 0;
    }

    /// 获取当前重连尝试次数
    pub fn current_attempts(&self) -> u32 {
        self.current_attempt
    }

    /// 检查是否可以重连
    pub fn can_reconnect(&self) -> bool {
        self.config.should_continue(self.current_attempt)
    }

    /// 强制重置重连状态
    pub fn reset(&mut self) {
        self.current_attempt = 0;
        self.last_stable_connection = None;
    }

    /// 获取下次重连延迟
    pub fn next_delay(&self) -> Duration {
        self.config.calculate_delay(self.current_attempt + 1)
    }
}

/// 重连策略枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ReconnectStrategy {
    /// 线性退避
    Linear,
    /// 指数退避
    Exponential,
    /// 固定延迟
    Fixed,
    /// 自定义策略
    Custom,
}

/// 高级重连配置构建器
pub struct ReconnectConfigBuilder {
    config: ReconnectConfig,
    strategy: ReconnectStrategy,
}

impl ReconnectConfigBuilder {
    /// 创建新的配置构建器
    pub fn new() -> Self {
        Self {
            config: ReconnectConfig::default(),
            strategy: ReconnectStrategy::Exponential,
        }
    }

    /// 设置重连策略
    pub fn strategy(mut self, strategy: ReconnectStrategy) -> Self {
        self.strategy = strategy;
        self
    }

    /// 设置快速重连（适合网络抖动）
    pub fn fast_reconnect(mut self) -> Self {
        self.config.initial_delay = Duration::from_millis(100);
        self.config.max_delay = Duration::from_secs(5);
        self.config.backoff_multiplier = 1.5;
        self.config.max_attempts = Some(20);
        self.config.jitter_factor = 0.2;
        self
    }

    /// 设置稳定重连（适合服务器维护）
    pub fn stable_reconnect(mut self) -> Self {
        self.config.initial_delay = Duration::from_secs(5);
        self.config.max_delay = Duration::from_secs(60);
        self.config.backoff_multiplier = 2.0;
        self.config.max_attempts = Some(10);
        self.config.jitter_factor = 0.1;
        self
    }

    /// 设置持久重连（适合长期连接）
    pub fn persistent_reconnect(mut self) -> Self {
        self.config.initial_delay = Duration::from_secs(1);
        self.config.max_delay = Duration::from_secs(30);
        self.config.backoff_multiplier = 1.8;
        self.config.max_attempts = None; // 无限重连
        self.config.jitter_factor = 0.15;
        self
    }

    /// 构建配置
    pub fn build(mut self) -> ReconnectConfig {
        // 根据策略调整参数
        match self.strategy {
            ReconnectStrategy::Linear => {
                self.config.backoff_multiplier = 1.0;
            }
            ReconnectStrategy::Exponential => {
                // 已经是默认配置
            }
            ReconnectStrategy::Fixed => {
                self.config.backoff_multiplier = 1.0;
                self.config.jitter_factor = 0.0;
            }
            ReconnectStrategy::Custom => {
                // 保持用户自定义设置
            }
        }
        
        self.config
    }
}

impl Default for ReconnectConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::connection::ConnectionManager;
    use std::sync::atomic::{AtomicU32, Ordering};
    use std::sync::Arc;

    #[test]
    fn test_reconnect_config_default() {
        let config = ReconnectConfig::default();
        assert!(config.enabled);
        assert_eq!(config.initial_delay, Duration::from_millis(1000));
        assert_eq!(config.max_delay, Duration::from_secs(30));
        assert_eq!(config.backoff_multiplier, 2.0);
        assert_eq!(config.max_attempts, Some(10));
        assert_eq!(config.jitter_factor, 0.1);
    }

    #[test]
    fn test_reconnect_config_builder() {
        let config = ReconnectConfigBuilder::new()
            .strategy(ReconnectStrategy::Linear)
            .build();
        
        assert_eq!(config.backoff_multiplier, 1.0);
    }

    #[test]
    fn test_reconnect_config_calculate_delay() {
        let config = ReconnectConfig {
            initial_delay: Duration::from_millis(1000),
            backoff_multiplier: 2.0,
            jitter_factor: 0.0, // 禁用抖动以便测试
            ..Default::default()
        };

        assert_eq!(config.calculate_delay(0), Duration::from_millis(1000));
        assert_eq!(config.calculate_delay(1), Duration::from_millis(1000));
        assert_eq!(config.calculate_delay(2), Duration::from_millis(2000));
        assert_eq!(config.calculate_delay(3), Duration::from_millis(4000));
    }

    #[test]
    fn test_reconnect_config_max_delay_cap() {
        let config = ReconnectConfig {
            initial_delay: Duration::from_millis(1000),
            max_delay: Duration::from_millis(3000),
            backoff_multiplier: 2.0,
            jitter_factor: 0.0,
            ..Default::default()
        };

        // 第4次尝试应该被限制在max_delay
        assert_eq!(config.calculate_delay(4), Duration::from_millis(3000));
    }

    #[test]
    fn test_reconnect_config_should_continue() {
        let config = ReconnectConfig {
            enabled: true,
            max_attempts: Some(3),
            ..Default::default()
        };

        assert!(config.should_continue(0));
        assert!(config.should_continue(1));
        assert!(config.should_continue(2));
        assert!(!config.should_continue(3));
    }

    #[test]
    fn test_reconnect_config_disabled() {
        let config = ReconnectConfig {
            enabled: false,
            ..Default::default()
        };

        assert!(!config.should_continue(0));
    }

    #[test]
    fn test_reconnect_config_unlimited_attempts() {
        let config = ReconnectConfig {
            enabled: true,
            max_attempts: None,
            ..Default::default()
        };

        assert!(config.should_continue(100));
        assert!(config.should_continue(1000));
    }

    #[tokio::test]
    async fn test_reconnector_creation() {
        let config = ReconnectConfig::default();
        let manager = ConnectionManager::new();
        let reconnector = Reconnector::new(config, std::sync::Arc::new(manager));
        
        assert_eq!(reconnector.current_attempts(), 0);
        assert!(reconnector.can_reconnect());
    }

    #[tokio::test]
    async fn test_reconnector_successful_reconnect() {
        let config = ReconnectConfig {
            initial_delay: Duration::from_millis(10),
            max_attempts: Some(3),
            ..Default::default()
        };
        let manager = ConnectionManager::new();
        let mut reconnector = Reconnector::new(config, std::sync::Arc::new(manager));

        let attempt_count = Arc::new(AtomicU32::new(0));
        let attempt_count_clone = attempt_count.clone();

        let reconnect_fn = move || {
            let count = attempt_count_clone.fetch_add(1, Ordering::Relaxed);
            async move {
                if count == 1 { // 第二次尝试成功
                    Ok(())
                } else {
                    Err(ChainListenerError::Connection("Test failure".to_string()))
                }
            }
        };

        let result = reconnector.smart_reconnect(reconnect_fn).await;
        assert!(result.is_ok());
        assert_eq!(attempt_count.load(Ordering::Relaxed), 2);
    }

    #[tokio::test]
    async fn test_reconnector_max_attempts_exceeded() {
        let config = ReconnectConfig {
            initial_delay: Duration::from_millis(10),
            max_attempts: Some(2),
            ..Default::default()
        };
        let manager = ConnectionManager::new();
        let mut reconnector = Reconnector::new(config, std::sync::Arc::new(manager));

        let reconnect_fn = || async {
            Err(ChainListenerError::Connection("Always fails".to_string()))
        };

        let result = reconnector.smart_reconnect(reconnect_fn).await;
        assert!(result.is_err());
        assert_eq!(reconnector.current_attempts(), 2);
    }

    #[test]
    fn test_reconnect_config_fast_preset() {
        let config = ReconnectConfigBuilder::new()
            .fast_reconnect()
            .build();

        assert_eq!(config.initial_delay, Duration::from_millis(100));
        assert_eq!(config.max_delay, Duration::from_secs(5));
        assert_eq!(config.backoff_multiplier, 1.5);
        assert_eq!(config.max_attempts, Some(20));
    }

    #[test]
    fn test_reconnect_config_persistent_preset() {
        let config = ReconnectConfigBuilder::new()
            .persistent_reconnect()
            .build();

        assert_eq!(config.max_attempts, None);
        assert_eq!(config.backoff_multiplier, 1.8);
    }
}