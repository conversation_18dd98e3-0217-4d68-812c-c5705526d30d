//! 账号数据解析示例
//!
//! 演示如何使用新的账号数据解析功能来解析 Raydium 和 Meteora 的账号数据

use data_parser::{
    AccountParserRegistry, AnchorParserManager, create_anchor_parser_manager
};
use shared::anchor_types::{
    RAYDIUM_CLMM_PROGRAM_ID, METEORA_DLMM_PROGRAM_ID
};
use solana_sdk::pubkey::Pubkey;
use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== 账号数据解析示例 ===");

    // 1. 创建账号解析器注册表
    let registry = AccountParserRegistry::new();

    // 2. 创建并注册 Anchor 解析器管理器
    let anchor_manager = Arc::new(create_anchor_parser_manager());
    registry.register_parser(anchor_manager)?;
    println!("✅ 已注册 Anchor 解析器管理器");

    // 4. 显示支持的程序ID
    let supported_programs = registry.get_supported_program_ids();
    println!("支持的程序ID:");
    for program_id in supported_programs {
        println!("  - {}", program_id);
    }

    // 5. 显示支持的账号类型
    let supported_types = registry.get_supported_account_types();
    println!("支持的账号类型:");
    for account_type in supported_types {
        println!("  - {:?}", account_type);
    }

    // 6. 演示根据程序ID查找解析器
    let raydium_parsers = registry.find_parsers_for_program(&RAYDIUM_CLMM_PROGRAM_ID);
    println!("找到 {} 个 Raydium 解析器", raydium_parsers.len());

    let meteora_parsers = registry.find_parsers_for_program(&METEORA_DLMM_PROGRAM_ID);
    println!("找到 {} 个 Meteora 解析器", meteora_parsers.len());

    // 7. 显示解析器元数据
    let metadata = registry.get_parser_metadata();
    println!("解析器详情:");
    for meta in metadata {
        println!("  解析器: {}", meta.name);
        println!("    版本: {}", meta.version);
        println!("    描述: {}", meta.description);
        println!("    优先级: {}", meta.priority);
        println!("    支持的程序: {}", meta.supported_programs.join(", "));
    }

    // 8. 演示解析模拟数据（实际使用中这里会是真实的账号数据）
    println!("\n=== 模拟账号数据解析 ===");

    // 模拟一个 1544 字节的 Raydium PoolState 数据（实际应该从链上获取）
    let mock_pool_data = vec![0u8; 1544];
    let mock_address = Pubkey::new_unique();

    // 尝试解析（这会失败，因为数据是模拟的，但演示了解析流程）
    match registry.parse_account(mock_address, RAYDIUM_CLMM_PROGRAM_ID, &mock_pool_data).await {
        Ok(parsed_account) => {
            println!("✅ 成功解析账号:");
            println!("  地址: {}", parsed_account.data.address());
            println!("  类型: {:?}", parsed_account.data.account_type());
            println!("  数据大小: {}", parsed_account.raw_data_size);
        }
        Err(e) => {
            println!("❌ 解析失败 (预期，因为是模拟数据): {}", e);
        }
    }

    // 9. 显示解析统计
    let stats = registry.get_stats();
    println!("\n=== 解析统计 ===");
    for (parser_name, stat) in stats {
        println!("解析器 {}: 成功 {}, 失败 {}, 平均耗时 {:.2}ms",
            parser_name, stat.successful_parses, stat.failed_parses, stat.average_parse_time_ms);
    }

    // 10. 健康检查
    let health = registry.health_check_all().await;
    println!("\n=== 健康检查 ===");
    for (parser_name, is_healthy) in health {
        println!("解析器 {}: {}", parser_name, if is_healthy { "✅ 健康" } else { "❌ 不健康" });
    }

    println!("\n=== 示例完成 ===");

    Ok(())
}
