{"address": "cpamdpZCGKUy5JxQXB4dcpGPiikHawvSWAd6mEn1sGG", "metadata": {"name": "cp_amm", "version": "0.1.3", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "add_liquidity", "discriminator": [181, 157, 89, 67, 143, 182, 52, 72], "accounts": [{"name": "pool", "writable": true, "relations": ["position"]}, {"name": "position", "writable": true}, {"name": "token_a_account", "docs": ["The user token a account"], "writable": true}, {"name": "token_b_account", "docs": ["The user token b account"], "writable": true}, {"name": "token_a_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "token_b_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "token_a_mint", "docs": ["The mint of token a"], "relations": ["pool"]}, {"name": "token_b_mint", "docs": ["The mint of token b"], "relations": ["pool"]}, {"name": "position_nft_account", "docs": ["The token account for nft"]}, {"name": "owner", "docs": ["owner of position"], "signer": true}, {"name": "token_a_program", "docs": ["Token a program"]}, {"name": "token_b_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "AddLiquidityParameters"}}}]}, {"name": "claim_partner_fee", "discriminator": [97, 206, 39, 105, 94, 94, 126, 148], "accounts": [{"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "writable": true}, {"name": "token_a_account", "docs": ["The treasury token a account"], "writable": true}, {"name": "token_b_account", "docs": ["The treasury token b account"], "writable": true}, {"name": "token_a_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "token_b_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "token_a_mint", "docs": ["The mint of token a"], "relations": ["pool"]}, {"name": "token_b_mint", "docs": ["The mint of token b"], "relations": ["pool"]}, {"name": "partner", "signer": true, "relations": ["pool"]}, {"name": "token_a_program", "docs": ["Token a program"]}, {"name": "token_b_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "max_amount_a", "type": "u64"}, {"name": "max_amount_b", "type": "u64"}]}, {"name": "claim_position_fee", "discriminator": [180, 38, 154, 17, 133, 33, 162, 211], "accounts": [{"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "relations": ["position"]}, {"name": "position", "writable": true}, {"name": "token_a_account", "docs": ["The user token a account"], "writable": true}, {"name": "token_b_account", "docs": ["The user token b account"], "writable": true}, {"name": "token_a_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "token_b_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "token_a_mint", "docs": ["The mint of token a"], "relations": ["pool"]}, {"name": "token_b_mint", "docs": ["The mint of token b"], "relations": ["pool"]}, {"name": "position_nft_account", "docs": ["The token account for nft"]}, {"name": "owner", "docs": ["owner of position"], "signer": true}, {"name": "token_a_program", "docs": ["Token a program"]}, {"name": "token_b_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "claim_protocol_fee", "discriminator": [165, 228, 133, 48, 99, 249, 255, 33], "accounts": [{"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "writable": true}, {"name": "token_a_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "token_b_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "token_a_mint", "docs": ["The mint of token a"], "relations": ["pool"]}, {"name": "token_b_mint", "docs": ["The mint of token b"], "relations": ["pool"]}, {"name": "token_a_account", "docs": ["The treasury token a account"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [48, 9, 89, 123, 106, 114, 131, 251, 50, 173, 254, 250, 10, 80, 160, 84, 143, 100, 81, 249, 134, 112, 30, 213, 50, 166, 239, 78, 53, 175, 188, 85]}, {"kind": "account", "path": "token_a_program"}, {"kind": "account", "path": "token_a_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "token_b_account", "docs": ["The treasury token b account"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [48, 9, 89, 123, 106, 114, 131, 251, 50, 173, 254, 250, 10, 80, 160, 84, 143, 100, 81, 249, 134, 112, 30, 213, 50, 166, 239, 78, 53, 175, 188, 85]}, {"kind": "account", "path": "token_b_program"}, {"kind": "account", "path": "token_b_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "claim_fee_operator", "docs": ["Claim fee operator"]}, {"name": "operator", "docs": ["Operator"], "signer": true, "relations": ["claim_fee_operator"]}, {"name": "token_a_program", "docs": ["Token a program"]}, {"name": "token_b_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "max_amount_a", "type": "u64"}, {"name": "max_amount_b", "type": "u64"}]}, {"name": "claim_reward", "discriminator": [149, 95, 181, 242, 94, 90, 158, 162], "accounts": [{"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "writable": true, "relations": ["position"]}, {"name": "position", "writable": true}, {"name": "reward_vault", "docs": ["The vault token account for reward token"], "writable": true}, {"name": "reward_mint"}, {"name": "user_token_account", "writable": true}, {"name": "position_nft_account", "docs": ["The token account for nft"]}, {"name": "owner", "docs": ["owner of position"], "signer": true}, {"name": "token_program"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u8"}, {"name": "skip_reward", "type": "u8"}]}, {"name": "close_claim_fee_operator", "discriminator": [38, 134, 82, 216, 95, 124, 17, 99], "accounts": [{"name": "claim_fee_operator", "writable": true}, {"name": "rent_receiver", "writable": true}, {"name": "admin", "signer": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "close_config", "discriminator": [145, 9, 72, 157, 95, 125, 61, 85], "accounts": [{"name": "config", "writable": true}, {"name": "admin", "writable": true, "signer": true}, {"name": "rent_receiver", "writable": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "close_position", "discriminator": [123, 134, 81, 0, 49, 68, 98, 98], "accounts": [{"name": "position_nft_mint", "docs": ["position_nft_mint"], "writable": true}, {"name": "position_nft_account", "docs": ["The token account for nft"], "writable": true}, {"name": "pool", "writable": true, "relations": ["position"]}, {"name": "position", "writable": true}, {"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "rent_receiver", "writable": true}, {"name": "owner", "docs": ["Owner of position"], "signer": true}, {"name": "token_program", "docs": ["Program to create NFT mint/token account and transfer for token22 account"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "close_token_badge", "discriminator": [108, 146, 86, 110, 179, 254, 10, 104], "accounts": [{"name": "token_badge", "writable": true}, {"name": "admin", "writable": true, "signer": true}, {"name": "rent_receiver", "writable": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "create_claim_fee_operator", "discriminator": [169, 62, 207, 107, 58, 187, 162, 109], "accounts": [{"name": "claim_fee_operator", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 102, 95, 111, 112, 101, 114, 97, 116, 111, 114]}, {"kind": "account", "path": "operator"}]}}, {"name": "operator"}, {"name": "admin", "writable": true, "signer": true}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "create_config", "docs": ["ADMIN FUNCTIONS /////"], "discriminator": [201, 207, 243, 114, 75, 111, 47, 189], "accounts": [{"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "arg", "path": "index"}]}}, {"name": "admin", "writable": true, "signer": true}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "index", "type": "u64"}, {"name": "config_parameters", "type": {"defined": {"name": "StaticConfigParameters"}}}]}, {"name": "create_dynamic_config", "discriminator": [81, 251, 122, 78, 66, 57, 208, 82], "accounts": [{"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "arg", "path": "index"}]}}, {"name": "admin", "writable": true, "signer": true}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "index", "type": "u64"}, {"name": "config_parameters", "type": {"defined": {"name": "DynamicConfigParameters"}}}]}, {"name": "create_position", "discriminator": [48, 215, 197, 153, 96, 203, 180, 133], "accounts": [{"name": "owner"}, {"name": "position_nft_mint", "docs": ["position_nft_mint"], "writable": true, "signer": true}, {"name": "position_nft_account", "docs": ["position nft account"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110, 95, 110, 102, 116, 95, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "pool", "writable": true}, {"name": "position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "payer", "docs": ["Address paying to create the position. Can be anyone"], "writable": true, "signer": true}, {"name": "token_program", "docs": ["Program to create NFT mint/token account and transfer for token22 account"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "create_token_badge", "discriminator": [88, 206, 0, 91, 60, 175, 151, 118], "accounts": [{"name": "token_badge", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 98, 97, 100, 103, 101]}, {"kind": "account", "path": "token_mint"}]}}, {"name": "token_mint"}, {"name": "admin", "writable": true, "signer": true}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "fund_reward", "discriminator": [188, 50, 249, 165, 93, 151, 38, 63], "accounts": [{"name": "pool", "writable": true}, {"name": "reward_vault", "writable": true}, {"name": "reward_mint"}, {"name": "funder_token_account", "writable": true}, {"name": "funder", "signer": true}, {"name": "token_program"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u8"}, {"name": "amount", "type": "u64"}, {"name": "carry_forward", "type": "bool"}]}, {"name": "initialize_customizable_pool", "discriminator": [20, 161, 241, 24, 189, 221, 180, 2], "accounts": [{"name": "creator"}, {"name": "position_nft_mint", "docs": ["position_nft_mint"], "writable": true, "signer": true}, {"name": "position_nft_account", "docs": ["position nft account"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110, 95, 110, 102, 116, 95, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "payer", "docs": ["Address paying to create the pool. Can be anyone"], "writable": true, "signer": true}, {"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "docs": ["Initialize an account to store the pool state"], "writable": true}, {"name": "position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "token_a_mint", "docs": ["Token a mint"]}, {"name": "token_b_mint", "docs": ["Token b mint"]}, {"name": "token_a_vault", "docs": ["Token a vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "token_a_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "token_b_vault", "docs": ["To<PERSON> b vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "token_b_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "payer_token_a", "docs": ["payer token a account"], "writable": true}, {"name": "payer_token_b", "docs": ["creator token b account"], "writable": true}, {"name": "token_a_program", "docs": ["Program to create mint account and mint tokens"]}, {"name": "token_b_program", "docs": ["Program to create mint account and mint tokens"]}, {"name": "token_2022_program", "docs": ["Program to create NFT mint/token account and transfer for token22 account"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "InitializeCustomizablePoolParameters"}}}]}, {"name": "initialize_pool", "docs": ["USER FUNCTIONS ////"], "discriminator": [95, 180, 10, 172, 84, 174, 232, 40], "accounts": [{"name": "creator"}, {"name": "position_nft_mint", "docs": ["position_nft_mint"], "writable": true, "signer": true}, {"name": "position_nft_account", "docs": ["position nft account"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110, 95, 110, 102, 116, 95, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "payer", "docs": ["Address paying to create the pool. Can be anyone"], "writable": true, "signer": true}, {"name": "config", "docs": ["Which config the pool belongs to."]}, {"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "docs": ["Initialize an account to store the pool state"], "writable": true}, {"name": "position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "token_a_mint", "docs": ["Token a mint"]}, {"name": "token_b_mint", "docs": ["Token b mint"]}, {"name": "token_a_vault", "docs": ["Token a vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "token_a_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "token_b_vault", "docs": ["To<PERSON> b vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "token_b_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "payer_token_a", "docs": ["payer token a account"], "writable": true}, {"name": "payer_token_b", "docs": ["creator token b account"], "writable": true}, {"name": "token_a_program", "docs": ["Program to create mint account and mint tokens"]}, {"name": "token_b_program", "docs": ["Program to create mint account and mint tokens"]}, {"name": "token_2022_program", "docs": ["Program to create NFT mint/token account and transfer for token22 account"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "InitializePoolParameters"}}}]}, {"name": "initialize_pool_with_dynamic_config", "discriminator": [149, 82, 72, 197, 253, 252, 68, 15], "accounts": [{"name": "creator"}, {"name": "position_nft_mint", "docs": ["position_nft_mint"], "writable": true, "signer": true}, {"name": "position_nft_account", "docs": ["position nft account"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110, 95, 110, 102, 116, 95, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "payer", "docs": ["Address paying to create the pool. Can be anyone"], "writable": true, "signer": true}, {"name": "pool_creator_authority", "signer": true, "relations": ["config"]}, {"name": "config", "docs": ["Which config the pool belongs to."]}, {"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "docs": ["Initialize an account to store the pool state"], "writable": true}, {"name": "position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "token_a_mint", "docs": ["Token a mint"]}, {"name": "token_b_mint", "docs": ["Token b mint"]}, {"name": "token_a_vault", "docs": ["Token a vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "token_a_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "token_b_vault", "docs": ["To<PERSON> b vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "token_b_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "payer_token_a", "docs": ["payer token a account"], "writable": true}, {"name": "payer_token_b", "docs": ["creator token b account"], "writable": true}, {"name": "token_a_program", "docs": ["Program to create mint account and mint tokens"]}, {"name": "token_b_program", "docs": ["Program to create mint account and mint tokens"]}, {"name": "token_2022_program", "docs": ["Program to create NFT mint/token account and transfer for token22 account"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "InitializeCustomizablePoolParameters"}}}]}, {"name": "initialize_reward", "discriminator": [95, 135, 192, 196, 242, 129, 230, 68], "accounts": [{"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "writable": true}, {"name": "reward_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [114, 101, 119, 97, 114, 100, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "pool"}, {"kind": "arg", "path": "reward_index"}]}}, {"name": "reward_mint"}, {"name": "signer", "signer": true}, {"name": "payer", "writable": true, "signer": true}, {"name": "token_program"}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u8"}, {"name": "reward_duration", "type": "u64"}, {"name": "funder", "type": "pubkey"}]}, {"name": "lock_position", "discriminator": [227, 62, 2, 252, 247, 10, 171, 185], "accounts": [{"name": "pool", "relations": ["position"]}, {"name": "position", "writable": true}, {"name": "vesting", "writable": true, "signer": true}, {"name": "position_nft_account", "docs": ["The token account for nft"]}, {"name": "owner", "docs": ["owner of position"], "signer": true}, {"name": "payer", "writable": true, "signer": true}, {"name": "system_program", "address": "****************************1111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "VestingParameters"}}}]}, {"name": "permanent_lock_position", "discriminator": [165, 176, 125, 6, 231, 171, 186, 213], "accounts": [{"name": "pool", "writable": true, "relations": ["position"]}, {"name": "position", "writable": true}, {"name": "position_nft_account", "docs": ["The token account for nft"]}, {"name": "owner", "docs": ["owner of position"], "signer": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "permanent_lock_liquidity", "type": "u128"}]}, {"name": "refresh_vesting", "discriminator": [9, 94, 216, 14, 116, 204, 247, 0], "accounts": [{"name": "pool", "relations": ["position"]}, {"name": "position", "writable": true}, {"name": "position_nft_account", "docs": ["The token account for nft"]}, {"name": "owner"}], "args": []}, {"name": "remove_all_liquidity", "discriminator": [10, 51, 61, 35, 112, 105, 24, 85], "accounts": [{"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "writable": true, "relations": ["position"]}, {"name": "position", "writable": true}, {"name": "token_a_account", "docs": ["The user token a account"], "writable": true}, {"name": "token_b_account", "docs": ["The user token b account"], "writable": true}, {"name": "token_a_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "token_b_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "token_a_mint", "docs": ["The mint of token a"], "relations": ["pool"]}, {"name": "token_b_mint", "docs": ["The mint of token b"], "relations": ["pool"]}, {"name": "position_nft_account", "docs": ["The token account for nft"]}, {"name": "owner", "docs": ["owner of position"], "signer": true}, {"name": "token_a_program", "docs": ["Token a program"]}, {"name": "token_b_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "token_a_amount_threshold", "type": "u64"}, {"name": "token_b_amount_threshold", "type": "u64"}]}, {"name": "remove_liquidity", "discriminator": [80, 85, 209, 72, 24, 206, 177, 108], "accounts": [{"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "writable": true, "relations": ["position"]}, {"name": "position", "writable": true}, {"name": "token_a_account", "docs": ["The user token a account"], "writable": true}, {"name": "token_b_account", "docs": ["The user token b account"], "writable": true}, {"name": "token_a_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "token_b_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "token_a_mint", "docs": ["The mint of token a"], "relations": ["pool"]}, {"name": "token_b_mint", "docs": ["The mint of token b"], "relations": ["pool"]}, {"name": "position_nft_account", "docs": ["The token account for nft"]}, {"name": "owner", "docs": ["owner of position"], "signer": true}, {"name": "token_a_program", "docs": ["Token a program"]}, {"name": "token_b_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "RemoveLiquidityParameters"}}}]}, {"name": "set_pool_status", "discriminator": [112, 87, 135, 223, 83, 204, 132, 53], "accounts": [{"name": "pool", "writable": true}, {"name": "admin", "signer": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "status", "type": "u8"}]}, {"name": "split_position", "discriminator": [172, 241, 221, 138, 161, 29, 253, 42], "accounts": [{"name": "pool", "writable": true, "relations": ["first_position", "second_position"]}, {"name": "first_position", "docs": ["The first position"], "writable": true}, {"name": "first_position_nft_account", "docs": ["The token account for position nft"]}, {"name": "second_position", "docs": ["The second position"], "writable": true}, {"name": "second_position_nft_account", "docs": ["The token account for position nft"]}, {"name": "first_owner", "docs": ["Owner of first position"], "signer": true}, {"name": "second_owner", "docs": ["Owner of second position"], "signer": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "SplitPositionParameters"}}}]}, {"name": "swap", "discriminator": [248, 198, 158, 145, 225, 117, 135, 200], "accounts": [{"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "docs": ["Pool account"], "writable": true}, {"name": "input_token_account", "docs": ["The user token account for input token"], "writable": true}, {"name": "output_token_account", "docs": ["The user token account for output token"], "writable": true}, {"name": "token_a_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "token_b_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "token_a_mint", "docs": ["The mint of token a"]}, {"name": "token_b_mint", "docs": ["The mint of token b"]}, {"name": "payer", "docs": ["The user performing the swap"], "signer": true}, {"name": "token_a_program", "docs": ["Token a program"]}, {"name": "token_b_program", "docs": ["Token b program"]}, {"name": "referral_token_account", "docs": ["referral token account"], "writable": true, "optional": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "SwapParameters"}}}]}, {"name": "update_reward_duration", "discriminator": [138, 174, 196, 169, 213, 235, 254, 107], "accounts": [{"name": "pool", "writable": true}, {"name": "signer", "signer": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u8"}, {"name": "new_duration", "type": "u64"}]}, {"name": "update_reward_funder", "discriminator": [211, 28, 48, 32, 215, 160, 35, 23], "accounts": [{"name": "pool", "writable": true}, {"name": "signer", "signer": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u8"}, {"name": "new_funder", "type": "pubkey"}]}, {"name": "withdraw_ineligible_reward", "discriminator": [148, 206, 42, 195, 247, 49, 103, 8], "accounts": [{"name": "pool_authority", "address": "HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC"}, {"name": "pool", "writable": true}, {"name": "reward_vault", "writable": true}, {"name": "reward_mint"}, {"name": "funder_token_account", "writable": true}, {"name": "funder", "signer": true}, {"name": "token_program"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "reward_index", "type": "u8"}]}], "accounts": [{"name": "ClaimFeeOperator", "discriminator": [166, 48, 134, 86, 34, 200, 188, 150]}, {"name": "Config", "discriminator": [155, 12, 170, 224, 30, 250, 204, 130]}, {"name": "Pool", "discriminator": [241, 154, 109, 4, 17, 177, 109, 188]}, {"name": "Position", "discriminator": [170, 188, 143, 228, 122, 64, 247, 208]}, {"name": "TokenBadge", "discriminator": [116, 219, 204, 229, 249, 116, 255, 150]}, {"name": "Vesting", "discriminator": [100, 149, 66, 138, 95, 200, 128, 241]}], "events": [{"name": "EvtAddLiquidity", "discriminator": [175, 242, 8, 157, 30, 247, 185, 169]}, {"name": "EvtClaimPartnerFee", "discriminator": [118, 99, 77, 10, 226, 1, 1, 87]}, {"name": "EvtClaimPositionFee", "discriminator": [198, 182, 183, 52, 97, 12, 49, 56]}, {"name": "EvtClaimProtocolFee", "discriminator": [186, 244, 75, 251, 188, 13, 25, 33]}, {"name": "EvtClaimReward", "discriminator": [218, 86, 147, 200, 235, 188, 215, 231]}, {"name": "EvtCloseClaimFeeOperator", "discriminator": [111, 39, 37, 55, 110, 216, 194, 23]}, {"name": "EvtCloseConfig", "discriminator": [36, 30, 239, 45, 58, 132, 14, 5]}, {"name": "EvtClosePosition", "discriminator": [20, 145, 144, 68, 143, 142, 214, 178]}, {"name": "EvtCreateClaimFeeOperator", "discriminator": [21, 6, 153, 120, 68, 116, 28, 177]}, {"name": "EvtCreateConfig", "discriminator": [131, 207, 180, 174, 180, 73, 165, 54]}, {"name": "EvtCreateDynamicConfig", "discriminator": [231, 197, 13, 164, 248, 213, 133, 152]}, {"name": "EvtCreatePosition", "discriminator": [156, 15, 119, 198, 29, 181, 221, 55]}, {"name": "EvtCreateTokenBadge", "discriminator": [141, 120, 134, 116, 34, 28, 114, 160]}, {"name": "EvtFundReward", "discriminator": [104, 233, 237, 122, 199, 191, 121, 85]}, {"name": "EvtInitializePool", "discriminator": [228, 50, 246, 85, 203, 66, 134, 37]}, {"name": "EvtInitializeReward", "discriminator": [129, 91, 188, 3, 246, 52, 185, 249]}, {"name": "EvtLockPosition", "discriminator": [168, 63, 108, 83, 219, 82, 2, 200]}, {"name": "EvtPermanentLockPosition", "discriminator": [145, 143, 162, 218, 218, 80, 67, 11]}, {"name": "EvtRemoveLiquidity", "discriminator": [87, 46, 88, 98, 175, 96, 34, 91]}, {"name": "EvtSetPoolStatus", "discriminator": [100, 213, 74, 3, 95, 91, 228, 146]}, {"name": "EvtSplitPosition", "discriminator": [182, 138, 42, 254, 27, 94, 82, 221]}, {"name": "EvtSwap", "discriminator": [27, 60, 21, 213, 138, 170, 187, 147]}, {"name": "EvtUpdateRewardDuration", "discriminator": [149, 135, 65, 231, 129, 153, 65, 57]}, {"name": "EvtUpdateRewardFunder", "discriminator": [76, 154, 208, 13, 40, 115, 246, 146]}, {"name": "EvtWithdrawIneligibleReward", "discriminator": [248, 215, 184, 78, 31, 180, 179, 168]}], "errors": [{"code": 6000, "name": "MathOverflow", "msg": "Math operation overflow"}, {"code": 6001, "name": "InvalidFee", "msg": "Invalid fee setup"}, {"code": 6002, "name": "ExceededSlippage", "msg": "Exceeded slippage tolerance"}, {"code": 6003, "name": "PoolDisabled", "msg": "Pool disabled"}, {"code": 6004, "name": "ExceedMaxFeeBps", "msg": "Exceeded max fee bps"}, {"code": 6005, "name": "InvalidAdmin", "msg": "Invalid admin"}, {"code": 6006, "name": "AmountIsZero", "msg": "Amount is zero"}, {"code": 6007, "name": "TypeCastFailed", "msg": "Type cast error"}, {"code": 6008, "name": "UnableToModifyActivationPoint", "msg": "Unable to modify activation point"}, {"code": 6009, "name": "InvalidAuthorityToCreateThePool", "msg": "Invalid authority to create the pool"}, {"code": 6010, "name": "InvalidActivationType", "msg": "Invalid activation type"}, {"code": 6011, "name": "InvalidActivationPoint", "msg": "Invalid activation point"}, {"code": 6012, "name": "InvalidQuoteMint", "msg": "Quote token must be SOL,USDC"}, {"code": 6013, "name": "InvalidFeeCurve", "msg": "Invalid fee curve"}, {"code": 6014, "name": "InvalidPriceRange", "msg": "Invalid Price Range"}, {"code": 6015, "name": "PriceRangeViolation", "msg": "Trade is over price range"}, {"code": 6016, "name": "InvalidParameters", "msg": "Invalid parameters"}, {"code": 6017, "name": "InvalidCollectFeeMode", "msg": "Invalid collect fee mode"}, {"code": 6018, "name": "InvalidInput", "msg": "Invalid input"}, {"code": 6019, "name": "CannotCreateTokenBadgeOnSupportedMint", "msg": "Cannot create token badge on supported mint"}, {"code": 6020, "name": "InvalidTokenBadge", "msg": "Invalid token badge"}, {"code": 6021, "name": "InvalidMinimumLiquidity", "msg": "Invalid minimum liquidity"}, {"code": 6022, "name": "InvalidVestingInfo", "msg": "Invalid vesting information"}, {"code": 6023, "name": "InsufficientLiquidity", "msg": "Insufficient liquidity"}, {"code": 6024, "name": "InvalidVestingAccount", "msg": "Invalid vesting account"}, {"code": 6025, "name": "InvalidPoolStatus", "msg": "Invalid pool status"}, {"code": 6026, "name": "UnsupportNativeMintToken2022", "msg": "Unsupported native mint token2022"}, {"code": 6027, "name": "InvalidRewardIndex", "msg": "Invalid reward index"}, {"code": 6028, "name": "InvalidRewardDuration", "msg": "Invalid reward duration"}, {"code": 6029, "name": "RewardInitialized", "msg": "<PERSON><PERSON> already initialized"}, {"code": 6030, "name": "RewardUninitialized", "msg": "Reward not initialized"}, {"code": 6031, "name": "InvalidRewardVault", "msg": "Invalid reward vault"}, {"code": 6032, "name": "MustWithdrawnIneligibleReward", "msg": "Must withdraw ineligible reward"}, {"code": 6033, "name": "IdenticalRewardDuration", "msg": "Reward duration is the same"}, {"code": 6034, "name": "RewardCampaignInProgress", "msg": "Reward campaign in progress"}, {"code": 6035, "name": "IdenticalFunder", "msg": "Identical funder"}, {"code": 6036, "name": "InvalidFunder", "msg": "Invalid funder"}, {"code": 6037, "name": "RewardNotEnded", "msg": "<PERSON><PERSON> not ended"}, {"code": 6038, "name": "FeeInverseIsIncorrect", "msg": "Fee inverse is incorrect"}, {"code": 6039, "name": "PositionIsNotEmpty", "msg": "Position is not empty"}, {"code": 6040, "name": "InvalidPoolCreatorAuthority", "msg": "Invalid pool creator authority"}, {"code": 6041, "name": "InvalidConfigType", "msg": "Invalid config type"}, {"code": 6042, "name": "InvalidPoolCreator", "msg": "Invalid pool creator"}, {"code": 6043, "name": "RewardVaultFrozenSkipRequired", "msg": "Reward vault is frozen, must skip reward to proceed"}, {"code": 6044, "name": "InvalidSplitPositionParameters", "msg": "Invalid parameters for split position"}, {"code": 6045, "name": "UnsupportPositionHasVestingLock", "msg": "Unsupported split position has vesting lock"}, {"code": 6046, "name": "SamePosition", "msg": "Same position"}], "types": [{"name": "AddLiquidityParameters", "type": {"kind": "struct", "fields": [{"name": "liquidity_delta", "docs": ["delta liquidity"], "type": "u128"}, {"name": "token_a_amount_threshold", "docs": ["maximum token a amount"], "type": "u64"}, {"name": "token_b_amount_threshold", "docs": ["maximum token b amount"], "type": "u64"}]}}, {"name": "BaseFeeConfig", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "cliff_fee_numerator", "type": "u64"}, {"name": "fee_scheduler_mode", "type": "u8"}, {"name": "padding", "type": {"array": ["u8", 5]}}, {"name": "number_of_period", "type": "u16"}, {"name": "period_frequency", "type": "u64"}, {"name": "reduction_factor", "type": "u64"}]}}, {"name": "BaseFeeParameters", "type": {"kind": "struct", "fields": [{"name": "cliff_fee_numerator", "type": "u64"}, {"name": "number_of_period", "type": "u16"}, {"name": "period_frequency", "type": "u64"}, {"name": "reduction_factor", "type": "u64"}, {"name": "fee_scheduler_mode", "type": "u8"}]}}, {"name": "BaseFeeStruct", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "cliff_fee_numerator", "type": "u64"}, {"name": "fee_scheduler_mode", "type": "u8"}, {"name": "padding_0", "type": {"array": ["u8", 5]}}, {"name": "number_of_period", "type": "u16"}, {"name": "period_frequency", "type": "u64"}, {"name": "reduction_factor", "type": "u64"}, {"name": "padding_1", "type": "u64"}]}}, {"name": "ClaimFeeOperator", "docs": ["Parameter that set by the protocol"], "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "operator", "docs": ["operator"], "type": "pubkey"}, {"name": "_padding", "docs": ["Reserve"], "type": {"array": ["u8", 128]}}]}}, {"name": "Config", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "vault_config_key", "docs": ["<PERSON><PERSON> config key"], "type": "pubkey"}, {"name": "pool_creator_authority", "docs": ["Only pool_creator_authority can use the current config to initialize new pool. When it's Pubkey::default, it's a public config."], "type": "pubkey"}, {"name": "pool_fees", "docs": ["Pool fee"], "type": {"defined": {"name": "PoolFeesConfig"}}}, {"name": "activation_type", "docs": ["Activation type"], "type": "u8"}, {"name": "collect_fee_mode", "docs": ["Collect fee mode"], "type": "u8"}, {"name": "config_type", "docs": ["Config type mode, 0 for static, 1 for dynamic"], "type": "u8"}, {"name": "_padding_0", "docs": ["padding 0"], "type": {"array": ["u8", 5]}}, {"name": "index", "docs": ["config index"], "type": "u64"}, {"name": "sqrt_min_price", "docs": ["sqrt min price"], "type": "u128"}, {"name": "sqrt_max_price", "docs": ["sqrt max price"], "type": "u128"}, {"name": "_padding_1", "docs": ["Fee curve point", "Padding for further use"], "type": {"array": ["u64", 10]}}]}}, {"name": "DynamicConfigParameters", "type": {"kind": "struct", "fields": [{"name": "pool_creator_authority", "type": "pubkey"}]}}, {"name": "DynamicFeeConfig", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "initialized", "type": "u8"}, {"name": "padding", "type": {"array": ["u8", 7]}}, {"name": "max_volatility_accumulator", "type": "u32"}, {"name": "variable_fee_control", "type": "u32"}, {"name": "bin_step", "type": "u16"}, {"name": "filter_period", "type": "u16"}, {"name": "decay_period", "type": "u16"}, {"name": "reduction_factor", "type": "u16"}, {"name": "padding_1", "type": {"array": ["u8", 8]}}, {"name": "bin_step_u128", "type": "u128"}]}}, {"name": "DynamicFeeParameters", "type": {"kind": "struct", "fields": [{"name": "bin_step", "type": "u16"}, {"name": "bin_step_u128", "type": "u128"}, {"name": "filter_period", "type": "u16"}, {"name": "decay_period", "type": "u16"}, {"name": "reduction_factor", "type": "u16"}, {"name": "max_volatility_accumulator", "type": "u32"}, {"name": "variable_fee_control", "type": "u32"}]}}, {"name": "DynamicFeeStruct", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "initialized", "type": "u8"}, {"name": "padding", "type": {"array": ["u8", 7]}}, {"name": "max_volatility_accumulator", "type": "u32"}, {"name": "variable_fee_control", "type": "u32"}, {"name": "bin_step", "type": "u16"}, {"name": "filter_period", "type": "u16"}, {"name": "decay_period", "type": "u16"}, {"name": "reduction_factor", "type": "u16"}, {"name": "last_update_timestamp", "type": "u64"}, {"name": "bin_step_u128", "type": "u128"}, {"name": "sqrt_price_reference", "type": "u128"}, {"name": "volatility_accumulator", "type": "u128"}, {"name": "volatility_reference", "type": "u128"}]}}, {"name": "EvtAddLiquidity", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "params", "type": {"defined": {"name": "AddLiquidityParameters"}}}, {"name": "token_a_amount", "type": "u64"}, {"name": "token_b_amount", "type": "u64"}, {"name": "total_amount_a", "type": "u64"}, {"name": "total_amount_b", "type": "u64"}]}}, {"name": "EvtClaimPartnerFee", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "token_a_amount", "type": "u64"}, {"name": "token_b_amount", "type": "u64"}]}}, {"name": "EvtClaimPositionFee", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "fee_a_claimed", "type": "u64"}, {"name": "fee_b_claimed", "type": "u64"}]}}, {"name": "EvtClaimProtocolFee", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "token_a_amount", "type": "u64"}, {"name": "token_b_amount", "type": "u64"}]}}, {"name": "EvtClaimReward", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "mint_reward", "type": "pubkey"}, {"name": "reward_index", "type": "u8"}, {"name": "total_reward", "type": "u64"}]}}, {"name": "EvtCloseClaimFeeOperator", "docs": ["Close claim fee operator"], "type": {"kind": "struct", "fields": [{"name": "claim_fee_operator", "type": "pubkey"}, {"name": "operator", "type": "pubkey"}]}}, {"name": "EvtCloseConfig", "docs": ["Close config"], "type": {"kind": "struct", "fields": [{"name": "config", "docs": ["Config pubkey"], "type": "pubkey"}, {"name": "admin", "docs": ["admin pk"], "type": "pubkey"}]}}, {"name": "EvtClosePosition", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "position_nft_mint", "type": "pubkey"}]}}, {"name": "EvtCreateClaimFeeOperator", "docs": ["Create claim fee operator"], "type": {"kind": "struct", "fields": [{"name": "operator", "type": "pubkey"}]}}, {"name": "EvtCreateConfig", "docs": ["Create static config"], "type": {"kind": "struct", "fields": [{"name": "pool_fees", "type": {"defined": {"name": "PoolFeeParameters"}}}, {"name": "vault_config_key", "type": "pubkey"}, {"name": "pool_creator_authority", "type": "pubkey"}, {"name": "activation_type", "type": "u8"}, {"name": "sqrt_min_price", "type": "u128"}, {"name": "sqrt_max_price", "type": "u128"}, {"name": "collect_fee_mode", "type": "u8"}, {"name": "index", "type": "u64"}, {"name": "config", "type": "pubkey"}]}}, {"name": "EvtCreateDynamicConfig", "docs": ["Create dynamic config"], "type": {"kind": "struct", "fields": [{"name": "config", "type": "pubkey"}, {"name": "pool_creator_authority", "type": "pubkey"}, {"name": "index", "type": "u64"}]}}, {"name": "EvtCreatePosition", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "position_nft_mint", "type": "pubkey"}]}}, {"name": "EvtCreateTokenBadge", "docs": ["Create token badge"], "type": {"kind": "struct", "fields": [{"name": "token_mint", "type": "pubkey"}]}}, {"name": "EvtFundReward", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "funder", "type": "pubkey"}, {"name": "mint_reward", "type": "pubkey"}, {"name": "reward_index", "type": "u8"}, {"name": "amount", "type": "u64"}, {"name": "transfer_fee_excluded_amount_in", "type": "u64"}, {"name": "reward_duration_end", "type": "u64"}, {"name": "pre_reward_rate", "type": "u128"}, {"name": "post_reward_rate", "type": "u128"}]}}, {"name": "EvtInitializePool", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "token_a_mint", "type": "pubkey"}, {"name": "token_b_mint", "type": "pubkey"}, {"name": "creator", "type": "pubkey"}, {"name": "payer", "type": "pubkey"}, {"name": "alpha_vault", "type": "pubkey"}, {"name": "pool_fees", "type": {"defined": {"name": "PoolFeeParameters"}}}, {"name": "sqrt_min_price", "type": "u128"}, {"name": "sqrt_max_price", "type": "u128"}, {"name": "activation_type", "type": "u8"}, {"name": "collect_fee_mode", "type": "u8"}, {"name": "liquidity", "type": "u128"}, {"name": "sqrt_price", "type": "u128"}, {"name": "activation_point", "type": "u64"}, {"name": "token_a_flag", "type": "u8"}, {"name": "token_b_flag", "type": "u8"}, {"name": "token_a_amount", "type": "u64"}, {"name": "token_b_amount", "type": "u64"}, {"name": "total_amount_a", "type": "u64"}, {"name": "total_amount_b", "type": "u64"}, {"name": "pool_type", "type": "u8"}]}}, {"name": "EvtInitializeReward", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "reward_mint", "type": "pubkey"}, {"name": "funder", "type": "pubkey"}, {"name": "creator", "type": "pubkey"}, {"name": "reward_index", "type": "u8"}, {"name": "reward_duration", "type": "u64"}]}}, {"name": "EvtLockPosition", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "vesting", "type": "pubkey"}, {"name": "cliff_point", "type": "u64"}, {"name": "period_frequency", "type": "u64"}, {"name": "cliff_unlock_liquidity", "type": "u128"}, {"name": "liquidity_per_period", "type": "u128"}, {"name": "number_of_period", "type": "u16"}]}}, {"name": "EvtPermanentLockPosition", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "lock_liquidity_amount", "type": "u128"}, {"name": "total_permanent_locked_liquidity", "type": "u128"}]}}, {"name": "EvtRemoveLiquidity", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "position", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "params", "type": {"defined": {"name": "RemoveLiquidityParameters"}}}, {"name": "token_a_amount", "type": "u64"}, {"name": "token_b_amount", "type": "u64"}]}}, {"name": "EvtSetPoolStatus", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "status", "type": "u8"}]}}, {"name": "EvtSplitPosition", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "first_owner", "type": "pubkey"}, {"name": "second_owner", "type": "pubkey"}, {"name": "first_position", "type": "pubkey"}, {"name": "second_position", "type": "pubkey"}, {"name": "current_sqrt_price", "type": "u128"}, {"name": "amount_splits", "type": {"defined": {"name": "SplitAmountInfo"}}}, {"name": "first_position_info", "type": {"defined": {"name": "SplitPositionInfo"}}}, {"name": "second_position_info", "type": {"defined": {"name": "SplitPositionInfo"}}}, {"name": "split_position_parameters", "type": {"defined": {"name": "SplitPositionParameters"}}}]}}, {"name": "EvtSwap", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "trade_direction", "type": "u8"}, {"name": "has_referral", "type": "bool"}, {"name": "params", "type": {"defined": {"name": "SwapParameters"}}}, {"name": "swap_result", "type": {"defined": {"name": "SwapResult"}}}, {"name": "actual_amount_in", "type": "u64"}, {"name": "current_timestamp", "type": "u64"}]}}, {"name": "EvtUpdateRewardDuration", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "reward_index", "type": "u8"}, {"name": "old_reward_duration", "type": "u64"}, {"name": "new_reward_duration", "type": "u64"}]}}, {"name": "EvtUpdateRewardFunder", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "reward_index", "type": "u8"}, {"name": "old_funder", "type": "pubkey"}, {"name": "new_funder", "type": "pubkey"}]}}, {"name": "EvtWithdrawIneligibleReward", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "reward_mint", "type": "pubkey"}, {"name": "amount", "type": "u64"}]}}, {"name": "InitializeCustomizablePoolParameters", "type": {"kind": "struct", "fields": [{"name": "pool_fees", "docs": ["pool fees"], "type": {"defined": {"name": "PoolFeeParameters"}}}, {"name": "sqrt_min_price", "docs": ["sqrt min price"], "type": "u128"}, {"name": "sqrt_max_price", "docs": ["sqrt max price"], "type": "u128"}, {"name": "has_alpha_vault", "docs": ["has alpha vault"], "type": "bool"}, {"name": "liquidity", "docs": ["initialize liquidity"], "type": "u128"}, {"name": "sqrt_price", "docs": ["The init price of the pool as a sqrt(token_b/token_a) Q64.64 value"], "type": "u128"}, {"name": "activation_type", "docs": ["activation type"], "type": "u8"}, {"name": "collect_fee_mode", "docs": ["collect fee mode"], "type": "u8"}, {"name": "activation_point", "docs": ["activation point"], "type": {"option": "u64"}}]}}, {"name": "InitializePoolParameters", "type": {"kind": "struct", "fields": [{"name": "liquidity", "docs": ["initialize liquidity"], "type": "u128"}, {"name": "sqrt_price", "docs": ["The init price of the pool as a sqrt(token_b/token_a) Q64.64 value"], "type": "u128"}, {"name": "activation_point", "docs": ["activation point"], "type": {"option": "u64"}}]}}, {"name": "Pool", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "pool_fees", "docs": ["Pool fee"], "type": {"defined": {"name": "PoolFeesStruct"}}}, {"name": "token_a_mint", "docs": ["token a mint"], "type": "pubkey"}, {"name": "token_b_mint", "docs": ["token b mint"], "type": "pubkey"}, {"name": "token_a_vault", "docs": ["token a vault"], "type": "pubkey"}, {"name": "token_b_vault", "docs": ["token b vault"], "type": "pubkey"}, {"name": "whitelisted_vault", "docs": ["Whitelisted vault to be able to buy pool before activation_point"], "type": "pubkey"}, {"name": "partner", "docs": ["partner"], "type": "pubkey"}, {"name": "liquidity", "docs": ["liquidity share"], "type": "u128"}, {"name": "_padding", "docs": ["padding, previous reserve amount, be careful to use that field"], "type": "u128"}, {"name": "protocol_a_fee", "docs": ["protocol a fee"], "type": "u64"}, {"name": "protocol_b_fee", "docs": ["protocol b fee"], "type": "u64"}, {"name": "partner_a_fee", "docs": ["partner a fee"], "type": "u64"}, {"name": "partner_b_fee", "docs": ["partner b fee"], "type": "u64"}, {"name": "sqrt_min_price", "docs": ["min price"], "type": "u128"}, {"name": "sqrt_max_price", "docs": ["max price"], "type": "u128"}, {"name": "sqrt_price", "docs": ["current price"], "type": "u128"}, {"name": "activation_point", "docs": ["Activation point, can be slot or timestamp"], "type": "u64"}, {"name": "activation_type", "docs": ["Activation type, 0 means by slot, 1 means by timestamp"], "type": "u8"}, {"name": "pool_status", "docs": ["pool status, 0: enable, 1 disable"], "type": "u8"}, {"name": "token_a_flag", "docs": ["token a flag"], "type": "u8"}, {"name": "token_b_flag", "docs": ["token b flag"], "type": "u8"}, {"name": "collect_fee_mode", "docs": ["0 is collect fee in both token, 1 only collect fee in token a, 2 only collect fee in token b"], "type": "u8"}, {"name": "pool_type", "docs": ["pool type"], "type": "u8"}, {"name": "_padding_0", "docs": ["padding"], "type": {"array": ["u8", 2]}}, {"name": "fee_a_per_liquidity", "docs": ["cumulative"], "type": {"array": ["u8", 32]}}, {"name": "fee_b_per_liquidity", "docs": ["cumulative"], "type": {"array": ["u8", 32]}}, {"name": "permanent_lock_liquidity", "type": "u128"}, {"name": "metrics", "docs": ["metrics"], "type": {"defined": {"name": "PoolMetrics"}}}, {"name": "creator", "docs": ["pool creator"], "type": "pubkey"}, {"name": "_padding_1", "docs": ["Padding for further use"], "type": {"array": ["u64", 6]}}, {"name": "reward_infos", "docs": ["Farming reward information"], "type": {"array": [{"defined": {"name": "RewardInfo"}}, 2]}}]}}, {"name": "PoolFeeParameters", "docs": ["Information regarding fee charges"], "type": {"kind": "struct", "fields": [{"name": "base_fee", "docs": ["Base fee"], "type": {"defined": {"name": "BaseFeeParameters"}}}, {"name": "padding", "docs": ["padding"], "type": {"array": ["u8", 3]}}, {"name": "dynamic_fee", "docs": ["dynamic fee"], "type": {"option": {"defined": {"name": "DynamicFeeParameters"}}}}]}}, {"name": "PoolFeesConfig", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "base_fee", "type": {"defined": {"name": "BaseFeeConfig"}}}, {"name": "dynamic_fee", "type": {"defined": {"name": "DynamicFeeConfig"}}}, {"name": "protocol_fee_percent", "type": "u8"}, {"name": "partner_fee_percent", "type": "u8"}, {"name": "referral_fee_percent", "type": "u8"}, {"name": "padding_0", "type": {"array": ["u8", 5]}}, {"name": "padding_1", "type": {"array": ["u64", 5]}}]}}, {"name": "PoolFeesStruct", "docs": ["Information regarding fee charges", "trading_fee = amount * trade_fee_numerator / denominator", "protocol_fee = trading_fee * protocol_fee_percentage / 100", "referral_fee = protocol_fee * referral_percentage / 100", "partner_fee = (protocol_fee - referral_fee) * partner_fee_percentage / denominator"], "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "base_fee", "docs": ["Trade fees are extra token amounts that are held inside the token", "accounts during a trade, making the value of liquidity tokens rise.", "Trade fee numerator"], "type": {"defined": {"name": "BaseFeeStruct"}}}, {"name": "protocol_fee_percent", "docs": ["Protocol trading fees are extra token amounts that are held inside the token", "accounts during a trade, with the equivalent in pool tokens minted to", "the protocol of the program.", "Protocol trade fee numerator"], "type": "u8"}, {"name": "partner_fee_percent", "docs": ["partner fee"], "type": "u8"}, {"name": "referral_fee_percent", "docs": ["referral fee"], "type": "u8"}, {"name": "padding_0", "docs": ["padding"], "type": {"array": ["u8", 5]}}, {"name": "dynamic_fee", "docs": ["dynamic fee"], "type": {"defined": {"name": "DynamicFeeStruct"}}}, {"name": "padding_1", "docs": ["padding"], "type": {"array": ["u64", 2]}}]}}, {"name": "PoolMetrics", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "total_lp_a_fee", "type": "u128"}, {"name": "total_lp_b_fee", "type": "u128"}, {"name": "total_protocol_a_fee", "type": "u64"}, {"name": "total_protocol_b_fee", "type": "u64"}, {"name": "total_partner_a_fee", "type": "u64"}, {"name": "total_partner_b_fee", "type": "u64"}, {"name": "total_position", "type": "u64"}, {"name": "padding", "type": "u64"}]}}, {"name": "Position", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "nft_mint", "docs": ["nft mint"], "type": "pubkey"}, {"name": "fee_a_per_token_checkpoint", "docs": ["fee a checkpoint"], "type": {"array": ["u8", 32]}}, {"name": "fee_b_per_token_checkpoint", "docs": ["fee b checkpoint"], "type": {"array": ["u8", 32]}}, {"name": "fee_a_pending", "docs": ["fee a pending"], "type": "u64"}, {"name": "fee_b_pending", "docs": ["fee b pending"], "type": "u64"}, {"name": "unlocked_liquidity", "docs": ["unlock liquidity"], "type": "u128"}, {"name": "vested_liquidity", "docs": ["vesting liquidity"], "type": "u128"}, {"name": "permanent_locked_liquidity", "docs": ["permanent locked liquidity"], "type": "u128"}, {"name": "metrics", "docs": ["metrics"], "type": {"defined": {"name": "PositionMetrics"}}}, {"name": "reward_infos", "docs": ["Farming reward information"], "type": {"array": [{"defined": {"name": "UserRewardInfo"}}, 2]}}, {"name": "padding", "docs": ["padding for future usage"], "type": {"array": ["u128", 6]}}]}}, {"name": "PositionMetrics", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "total_claimed_a_fee", "type": "u64"}, {"name": "total_claimed_b_fee", "type": "u64"}]}}, {"name": "RemoveLiquidityParameters", "type": {"kind": "struct", "fields": [{"name": "liquidity_delta", "docs": ["delta liquidity"], "type": "u128"}, {"name": "token_a_amount_threshold", "docs": ["minimum token a amount"], "type": "u64"}, {"name": "token_b_amount_threshold", "docs": ["minimum token b amount"], "type": "u64"}]}}, {"name": "RewardInfo", "docs": ["Stores the state relevant for tracking liquidity mining rewards"], "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "initialized", "docs": ["Indicates if the reward has been initialized"], "type": "u8"}, {"name": "reward_token_flag", "docs": ["reward token flag"], "type": "u8"}, {"name": "_padding_0", "docs": ["padding"], "type": {"array": ["u8", 6]}}, {"name": "_padding_1", "docs": ["Padding to ensure `reward_rate: u128` is 16-byte aligned"], "type": {"array": ["u8", 8]}}, {"name": "mint", "docs": ["Reward token mint."], "type": "pubkey"}, {"name": "vault", "docs": ["Reward vault token account."], "type": "pubkey"}, {"name": "funder", "docs": ["Authority account that allows to fund rewards"], "type": "pubkey"}, {"name": "reward_duration", "docs": ["reward duration"], "type": "u64"}, {"name": "reward_duration_end", "docs": ["reward duration end"], "type": "u64"}, {"name": "reward_rate", "docs": ["reward rate"], "type": "u128"}, {"name": "reward_per_token_stored", "docs": ["Reward per token stored"], "type": {"array": ["u8", 32]}}, {"name": "last_update_time", "docs": ["The last time reward states were updated."], "type": "u64"}, {"name": "cumulative_seconds_with_empty_liquidity_reward", "docs": ["Accumulated seconds when the farm distributed rewards but the bin was empty.", "These rewards will be carried over to the next reward time window."], "type": "u64"}]}}, {"name": "SplitAmountInfo", "type": {"kind": "struct", "fields": [{"name": "permanent_locked_liquidity", "type": "u128"}, {"name": "unlocked_liquidity", "type": "u128"}, {"name": "fee_a", "type": "u64"}, {"name": "fee_b", "type": "u64"}, {"name": "reward_0", "type": "u64"}, {"name": "reward_1", "type": "u64"}]}}, {"name": "SplitPositionInfo", "type": {"kind": "struct", "fields": [{"name": "liquidity", "type": "u128"}, {"name": "fee_a", "type": "u64"}, {"name": "fee_b", "type": "u64"}, {"name": "reward_0", "type": "u64"}, {"name": "reward_1", "type": "u64"}]}}, {"name": "SplitPositionParameters", "type": {"kind": "struct", "fields": [{"name": "unlocked_liquidity_percentage", "docs": ["Percentage of unlocked liquidity to split to the second position"], "type": "u8"}, {"name": "permanent_locked_liquidity_percentage", "docs": ["Percentage of permanent locked liquidity to split to the second position"], "type": "u8"}, {"name": "fee_a_percentage", "docs": ["Percentage of fee A pending to split to the second position"], "type": "u8"}, {"name": "fee_b_percentage", "docs": ["Percentage of fee B pending to split to the second position"], "type": "u8"}, {"name": "reward_0_percentage", "docs": ["Percentage of reward 0 pending to split to the second position"], "type": "u8"}, {"name": "reward_1_percentage", "docs": ["Percentage of reward 1 pending to split to the second position"], "type": "u8"}, {"name": "padding", "docs": ["padding for future"], "type": {"array": ["u8", 16]}}]}}, {"name": "StaticConfigParameters", "type": {"kind": "struct", "fields": [{"name": "pool_fees", "type": {"defined": {"name": "PoolFeeParameters"}}}, {"name": "sqrt_min_price", "type": "u128"}, {"name": "sqrt_max_price", "type": "u128"}, {"name": "vault_config_key", "type": "pubkey"}, {"name": "pool_creator_authority", "type": "pubkey"}, {"name": "activation_type", "type": "u8"}, {"name": "collect_fee_mode", "type": "u8"}]}}, {"name": "SwapParameters", "type": {"kind": "struct", "fields": [{"name": "amount_in", "type": "u64"}, {"name": "minimum_amount_out", "type": "u64"}]}}, {"name": "SwapResult", "docs": ["Encodes all results of swapping"], "type": {"kind": "struct", "fields": [{"name": "output_amount", "type": "u64"}, {"name": "next_sqrt_price", "type": "u128"}, {"name": "lp_fee", "type": "u64"}, {"name": "protocol_fee", "type": "u64"}, {"name": "partner_fee", "type": "u64"}, {"name": "referral_fee", "type": "u64"}]}}, {"name": "TokenBadge", "docs": ["Parameter that set by the protocol"], "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "token_mint", "docs": ["token mint"], "type": "pubkey"}, {"name": "_padding", "docs": ["Reserve"], "type": {"array": ["u8", 128]}}]}}, {"name": "UserRewardInfo", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "reward_per_token_checkpoint", "docs": ["The latest update reward checkpoint"], "type": {"array": ["u8", 32]}}, {"name": "reward_pendings", "docs": ["Current pending rewards"], "type": "u64"}, {"name": "total_claimed_rewards", "docs": ["Total claimed rewards"], "type": "u64"}]}}, {"name": "Vesting", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "position", "type": "pubkey"}, {"name": "cliff_point", "type": "u64"}, {"name": "period_frequency", "type": "u64"}, {"name": "cliff_unlock_liquidity", "type": "u128"}, {"name": "liquidity_per_period", "type": "u128"}, {"name": "total_released_liquidity", "type": "u128"}, {"name": "number_of_period", "type": "u16"}, {"name": "padding", "type": {"array": ["u8", 14]}}, {"name": "padding2", "type": {"array": ["u128", 4]}}]}}, {"name": "VestingParameters", "type": {"kind": "struct", "fields": [{"name": "cliff_point", "type": {"option": "u64"}}, {"name": "period_frequency", "type": "u64"}, {"name": "cliff_unlock_liquidity", "type": "u128"}, {"name": "liquidity_per_period", "type": "u128"}, {"name": "number_of_period", "type": "u16"}]}}]}