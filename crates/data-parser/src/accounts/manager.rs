//! Anchor 解析器管理器
//!
//! 统一管理所有 Anchor 解析器，提供统一的接口

use async_trait::async_trait;
use solana_sdk::pubkey::Pubkey;
use shared::Result;
use std::sync::Arc;

use crate::accounts::traits::{
    AccountParser, AccountType, ParsedAccount
};
use crate::accounts::raydium::RaydiumAnchorAccountParser;
use crate::accounts::raydium_cpmm::RaydiumCpmmAccountParser;
use crate::accounts::meteora::MeteoraAnchorAccountParser;
use crate::accounts::meteora_damm::MeteoraDammAnchorAccountParser;
use crate::accounts::pump_swap::PumpSwapAnchorAccountParser;

/// Anchor 解析器管理器
pub struct AnchorParserManager {
    name: String,
    parsers: Vec<Arc<dyn AccountParser>>,
}

impl AnchorParserManager {
    /// 创建新的 Anchor 解析器管理器
    pub fn new() -> Self {
        let parsers: Vec<Arc<dyn AccountParser>> = vec![
            Arc::new(RaydiumAnchorAccountParser::new()),
            Arc::new(RaydiumCpmmAccountParser::new()),
            Arc::new(MeteoraAnchorAccountParser::new()),
            Arc::new(MeteoraDammAnchorAccountParser::new()),
            Arc::new(PumpSwapAnchorAccountParser::new()),
        ];

        Self {
            name: "AnchorParserManager".to_string(),
            parsers,
        }
    }

    /// 添加解析器
    pub fn add_parser(&mut self, parser: Arc<dyn AccountParser>) {
        self.parsers.push(parser);
    }

    /// 获取所有支持的解析器
    fn get_parsers(&self) -> &[Arc<dyn AccountParser>] {
        &self.parsers
    }

    /// 根据程序ID选择合适的解析器
    fn select_parser(&self, program_id: &Pubkey) -> Option<&Arc<dyn AccountParser>> {
        self.parsers.iter().find(|parser| {
            parser.supported_program_ids().contains(program_id)
        })
    }

    /// 根据程序ID和数据选择最佳解析器
    fn select_best_parser(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<&Arc<dyn AccountParser>> {
        self.parsers.iter().find(|parser| {
            parser.can_parse(program_id, account_data)
        })
    }
}

impl Default for AnchorParserManager {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl AccountParser for AnchorParserManager {
    fn name(&self) -> &str {
        &self.name
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        self.parsers
            .iter()
            .flat_map(|parser| parser.supported_program_ids())
            .collect()
    }

    fn supported_account_types(&self) -> Vec<AccountType> {
        self.parsers
            .iter()
            .flat_map(|parser| parser.supported_account_types())
            .collect()
    }

    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool {
        self.select_best_parser(program_id, account_data).is_some()
    }

    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType> {
        self.select_best_parser(program_id, account_data)?
            .identify_account_type(program_id, account_data)
    }

    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        if let Some(parser) = self.select_best_parser(&program_id, account_data) {
            parser.parse_account(address, program_id, account_data).await
        } else {
            Err(shared::EchoesError::Parse(
                format!("No parser available for program ID: {}", program_id)
            ))
        }
    }
}

/// 创建默认的 Anchor 解析器管理器实例
pub fn create_anchor_parser_manager() -> AnchorParserManager {
    AnchorParserManager::new()
}