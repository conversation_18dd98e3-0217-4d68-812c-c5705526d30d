//! Meteora 账号数据 Anchor 解析器实现
//!
//! 使用简单直接的方法，避免复杂的宏系统

use async_trait::async_trait;
use anchor_lang::prelude::*;
use solana_sdk::pubkey::Pubkey;
use shared::Result;
use std::collections::HashMap;

use crate::accounts::traits::{
    AccountParser, AccountType, ParsedAccount, ParsedAccountData
};
use shared::anchor_types::meteora::{
    METEORA_DLMM_PROGRAM_ID, LbPair, BinArray, BinArrayBitmapExtension,
    Oracle, Position, PositionV2
};

/// Meteora Anchor 账号解析器
pub struct MeteoraAnchorAccountParser {
    name: String,
    discriminator_map: HashMap<[u8; 8], AccountType>,
}

impl MeteoraAnchorAccountParser {
    /// 创建新的 Meteora Anchor 账号解析器
    pub fn new() -> Self {
        let mut discriminator_map = HashMap::new();

        // 根据 Anchor discriminator 计算规则进行映射
        discriminator_map.insert([33, 11, 49, 98, 181, 101, 177, 13], AccountType::MeteoraLbPair);
        discriminator_map.insert([92, 142, 92, 220, 5, 148, 70, 181], AccountType::MeteoraBinArray);
        discriminator_map.insert([80, 111, 124, 113, 55, 237, 18, 5], AccountType::MeteoraBinArrayBitmapExtension);
        discriminator_map.insert([139, 194, 131, 179, 140, 179, 229, 244], AccountType::MeteoraOracle);
        discriminator_map.insert([170, 188, 143, 228, 122, 64, 247, 208], AccountType::MeteoraPosition);
        discriminator_map.insert([117, 176, 212, 199, 245, 180, 133, 182], AccountType::MeteoraPositionV2);

        Self {
            name: "MeteoraAnchorAccountParser".to_string(),
            discriminator_map,
        }
    }

    /// 使用 discriminator 识别账号类型
    fn identify_account_type_by_discriminator(&self, data: &[u8]) -> Option<AccountType> {
        if data.len() < 8 {
            return None;
        }

        let discriminator: [u8; 8] = data[0..8].try_into().ok()?;
        self.discriminator_map.get(&discriminator).cloned()
    }

    /// 解析 LbPair 账号
    fn parse_lb_pair(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let lb_pair = LbPair::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize LbPair: {}", e)))?;

        Ok(ParsedAccountData::MeteoraLbPair {
            address,
            data: lb_pair,
        })
    }

    /// 解析 BinArray 账号
    fn parse_bin_array(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let bin_array = BinArray::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize BinArray: {}", e)))?;

        Ok(ParsedAccountData::MeteoraBinArray {
            address,
            data: bin_array,
        })
    }

    /// 解析 BinArrayBitmapExtension 账号
    fn parse_bin_array_bitmap_extension(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let bitmap_extension = BinArrayBitmapExtension::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize BinArrayBitmapExtension: {}", e)))?;

        Ok(ParsedAccountData::MeteoraBinArrayBitmapExtension {
            address,
            data: bitmap_extension,
        })
    }

    /// 解析 Oracle 账号
    fn parse_oracle(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let oracle = Oracle::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize Oracle: {}", e)))?;

        Ok(ParsedAccountData::MeteoraOracle {
            address,
            data: oracle,
        })
    }

    /// 解析 Position 账号
    fn parse_position(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let position = Position::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize Position: {}", e)))?;

        Ok(ParsedAccountData::MeteoraPosition {
            address,
            data: position,
        })
    }

    /// 解析 PositionV2 账号
    fn parse_position_v2(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let position_v2 = PositionV2::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize PositionV2: {}", e)))?;

        Ok(ParsedAccountData::MeteoraPositionV2 {
            address,
            data: position_v2,
        })
    }
}

impl Default for MeteoraAnchorAccountParser {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl AccountParser for MeteoraAnchorAccountParser {
    fn name(&self) -> &str {
        &self.name
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        vec![METEORA_DLMM_PROGRAM_ID]
    }

    fn supported_account_types(&self) -> Vec<AccountType> {
        vec![
            AccountType::MeteoraLbPair,
            AccountType::MeteoraBinArray,
            AccountType::MeteoraBinArrayBitmapExtension,
            AccountType::MeteoraOracle,
            AccountType::MeteoraPosition,
            AccountType::MeteoraPositionV2,
        ]
    }

    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool {
        *program_id == METEORA_DLMM_PROGRAM_ID &&
        self.identify_account_type_by_discriminator(account_data).is_some()
    }

    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType> {
        if *program_id != METEORA_DLMM_PROGRAM_ID {
            return None;
        }

        self.identify_account_type_by_discriminator(account_data)
    }

    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        if program_id != METEORA_DLMM_PROGRAM_ID {
            return Err(shared::EchoesError::Parse(
                format!("Unsupported program ID: {}", program_id)
            ));
        }

        let account_type = self.identify_account_type_by_discriminator(account_data)
            .ok_or_else(|| shared::EchoesError::Parse(
                format!("Cannot identify account type for data length: {}", account_data.len())
            ))?;

        let parsed_data = match account_type {
            AccountType::MeteoraLbPair => {
                self.parse_lb_pair(address, account_data)?
            }
            AccountType::MeteoraBinArray => {
                self.parse_bin_array(address, account_data)?
            }
            AccountType::MeteoraBinArrayBitmapExtension => {
                self.parse_bin_array_bitmap_extension(address, account_data)?
            }
            AccountType::MeteoraOracle => {
                self.parse_oracle(address, account_data)?
            }
            AccountType::MeteoraPosition => {
                self.parse_position(address, account_data)?
            }
            AccountType::MeteoraPositionV2 => {
                self.parse_position_v2(address, account_data)?
            }
            _ => {
                return Err(shared::EchoesError::Parse(
                    format!("Unsupported account type: {:?}", account_type)
                ));
            }
        };

        let parsed_account = ParsedAccount::new(
            parsed_data,
            account_data.len(),
        );

        // 验证解析结果
        self.validate_parsed_data(&parsed_account)?;

        Ok(parsed_account)
    }
}


