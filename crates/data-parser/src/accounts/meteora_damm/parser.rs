//! Meteora DAMM V2 账号数据 Anchor 解析器实现
//!
//! 使用简单直接的方法，避免复杂的宏系统

use async_trait::async_trait;
use anchor_lang::prelude::*;
use solana_sdk::pubkey::Pubkey;
use shared::Result;
use std::collections::HashMap;

use crate::accounts::traits::{
    AccountParser, AccountType, ParsedAccount, ParsedAccountData
};
use shared::anchor_types::meteora_damm::{
    METEORA_DAMM_V2_PROGRAM_ID, Pool as MeteoraDammPool,
    Position as MeteoraDammPosition, Config as MeteoraDammConfig,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ator as MeteoraDammClaimFeeOperator,
    TokenBadge as MeteoraDammTokenBadge, Vesting as MeteoraDammVesting
};

/// Meteora DAMM V2 Anchor 账号解析器
pub struct MeteoraDammAnchorAccountParser {
    name: String,
    discriminator_map: HashMap<[u8; 8], AccountType>,
}

impl MeteoraDammAnchorAccountParser {
    /// 创建新的 Meteora DAMM V2 Anchor 账号解析器
    pub fn new() -> Self {
        let mut discriminator_map = HashMap::new();

        // 根据 Anchor discriminator 计算规则进行映射
        // 基于 meteora_damm_v2.json IDL 文件中的 discriminator 定义
        discriminator_map.insert([241, 154, 109, 4, 17, 177, 109, 188], AccountType::MeteoraDammPool);
        discriminator_map.insert([170, 188, 143, 228, 122, 64, 247, 208], AccountType::MeteoraDammPosition);
        discriminator_map.insert([155, 12, 170, 224, 30, 250, 204, 130], AccountType::MeteoraDammConfig);
        discriminator_map.insert([166, 48, 134, 86, 34, 200, 188, 150], AccountType::MeteoraDammClaimFeeOperator);
        discriminator_map.insert([116, 219, 204, 229, 249, 116, 255, 150], AccountType::MeteoraDammTokenBadge);
        discriminator_map.insert([100, 149, 66, 138, 95, 200, 128, 241], AccountType::MeteoraDammVesting);

        Self {
            name: "MeteoraDammAnchorAccountParser".to_string(),
            discriminator_map,
        }
    }

    /// 使用 discriminator 识别账号类型
    fn identify_account_type_by_discriminator(&self, data: &[u8]) -> Option<AccountType> {
        if data.len() < 8 {
            return None;
        }

        let discriminator: [u8; 8] = data[0..8].try_into().ok()?;
        self.discriminator_map.get(&discriminator).cloned()
    }

    /// 解析 Pool 账号
    fn parse_pool(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let pool = MeteoraDammPool::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize MeteoraDammPool: {}", e)))?;

        Ok(ParsedAccountData::MeteoraDammPool {
            address,
            data: pool,
        })
    }

    /// 解析 Position 账号
    fn parse_position(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let position = MeteoraDammPosition::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize MeteoraDammPosition: {}", e)))?;

        Ok(ParsedAccountData::MeteoraDammPosition {
            address,
            data: position,
        })
    }

    /// 解析 Config 账号
    fn parse_config(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let config = MeteoraDammConfig::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize MeteoraDammConfig: {}", e)))?;

        Ok(ParsedAccountData::MeteoraDammConfig {
            address,
            data: config,
        })
    }

    /// 解析 ClaimFeeOperator 账号
    fn parse_claim_fee_operator(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let claim_fee_operator = MeteoraDammClaimFeeOperator::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize MeteoraDammClaimFeeOperator: {}", e)))?;

        Ok(ParsedAccountData::MeteoraDammClaimFeeOperator {
            address,
            data: claim_fee_operator,
        })
    }

    /// 解析 TokenBadge 账号
    fn parse_token_badge(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let token_badge = MeteoraDammTokenBadge::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize MeteoraDammTokenBadge: {}", e)))?;

        Ok(ParsedAccountData::MeteoraDammTokenBadge {
            address,
            data: token_badge,
        })
    }

    /// 解析 Vesting 账号
    fn parse_vesting(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let vesting = MeteoraDammVesting::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize MeteoraDammVesting: {}", e)))?;

        Ok(ParsedAccountData::MeteoraDammVesting {
            address,
            data: vesting,
        })
    }
}

impl Default for MeteoraDammAnchorAccountParser {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl AccountParser for MeteoraDammAnchorAccountParser {
    fn name(&self) -> &str {
        &self.name
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        vec![METEORA_DAMM_V2_PROGRAM_ID]
    }

    fn supported_account_types(&self) -> Vec<AccountType> {
        vec![
            AccountType::MeteoraDammPool,
            AccountType::MeteoraDammPosition,
            AccountType::MeteoraDammConfig,
            AccountType::MeteoraDammClaimFeeOperator,
            AccountType::MeteoraDammTokenBadge,
            AccountType::MeteoraDammVesting,
        ]
    }

    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool {
        *program_id == METEORA_DAMM_V2_PROGRAM_ID &&
        self.identify_account_type_by_discriminator(account_data).is_some()
    }

    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType> {
        if *program_id != METEORA_DAMM_V2_PROGRAM_ID {
            return None;
        }

        self.identify_account_type_by_discriminator(account_data)
    }

    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        if program_id != METEORA_DAMM_V2_PROGRAM_ID {
            return Err(shared::EchoesError::Parse(
                format!("Unsupported program ID: {}", program_id)
            ));
        }

        let account_type = self.identify_account_type_by_discriminator(account_data)
            .ok_or_else(|| shared::EchoesError::Parse(
                format!("Cannot identify account type for data length: {}", account_data.len())
            ))?;

        let parsed_data = match account_type {
            AccountType::MeteoraDammPool => {
                self.parse_pool(address, account_data)?
            }
            AccountType::MeteoraDammPosition => {
                self.parse_position(address, account_data)?
            }
            AccountType::MeteoraDammConfig => {
                self.parse_config(address, account_data)?
            }
            AccountType::MeteoraDammClaimFeeOperator => {
                self.parse_claim_fee_operator(address, account_data)?
            }
            AccountType::MeteoraDammTokenBadge => {
                self.parse_token_badge(address, account_data)?
            }
            AccountType::MeteoraDammVesting => {
                self.parse_vesting(address, account_data)?
            }
            _ => {
                return Err(shared::EchoesError::Parse(
                    format!("Unsupported account type: {:?}", account_type)
                ));
            }
        };

        let parsed_account = ParsedAccount::new(
            parsed_data,
            account_data.len(),
        );

        // 验证解析结果
        self.validate_parsed_data(&parsed_account)?;

        Ok(parsed_account)
    }
}
