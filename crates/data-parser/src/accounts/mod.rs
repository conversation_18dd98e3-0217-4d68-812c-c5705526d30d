//! 账号数据解析模块
//!
//! 提供对不同 DEX 账号数据的结构化解析能力

pub mod traits;
pub mod registry;
pub mod raydium;
pub mod raydium_cpmm;
pub mod meteora;
pub mod meteora_damm;
pub mod pump_swap;

mod manager;

pub use traits::{AccountParser, AccountType, ParsedAccount, ParsedAccountData};
pub use registry::{AccountParserRegistry, AccountParserMetadata, global_account_registry};
pub use raydium::{RaydiumAnchorAccountParser};
pub use raydium_cpmm::{RaydiumCpmmAccountParser};
pub use meteora::{MeteoraAnchorAccountParser};
pub use meteora_damm::{MeteoraDammAnchorAccountParser};
pub use pump_swap::{PumpSwapAnchorAccountParser};
pub use manager::{AnchorParserManager, create_anchor_parser_manager};
