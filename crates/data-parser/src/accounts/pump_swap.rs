//! Pump Swap 账号解析器实现
//!
//! 基于 Anchor 的 Pump Swap 账号数据解析

use anchor_lang::AnchorDeserialize;
use async_trait::async_trait;
use solana_sdk::pubkey::Pubkey;
use shared::{Result};
use shared::anchor_types::pump_swap::{
    PUMP_SWAP_PROGRAM_ID, GLOBAL_CONFIG_DISCRIMINATOR, POOL_DISCRIMINATOR,
    identify_account_type, PumpSwapAccountType
};
use tracing::{debug, error, warn};
use shared::PumpSwapAccountType::Pool;
use shared::anchor_types::pump_swap::Pool as PumpSwapPool;
use super::traits::{
    AccountParser, AccountType, ParsedAccount, ParsedAccountData,
    AccountParseError
};

/// Pump Swap Anchor 账号解析器
///
/// 使用 Anchor 反序列化功能解析 Pump Swap 协议的账号数据
#[derive(Debug, Clone)]
pub struct PumpSwapAnchorAccountParser {
    name: String,
    version: String,
}

impl Default for PumpSwapAnchorAccountParser {
    fn default() -> Self {
        Self::new()
    }
}

impl PumpSwapAnchorAccountParser {
    /// 创建新的 Pump Swap 账号解析器
    pub fn new() -> Self {
        Self {
            name: "PumpSwapAnchorAccountParser".to_string(),
            version: "1.0.0".to_string(),
        }
    }

    /// 从原始数据解析 GlobalConfig 账号
    fn parse_global_config(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccount> {
        if data.len() < 8 {
            return Err(shared::EchoesError::Parse(
                AccountParseError::insufficient_data(8, data.len()).to_string()
            ));
        }

        // 验证 discriminator
        if data[0..8] != GLOBAL_CONFIG_DISCRIMINATOR {
            return Err(shared::EchoesError::Parse(
                "Invalid GlobalConfig discriminator".to_string()
            ));
        }

        // 解析 GlobalConfig 数据（跳过 8 字节的 discriminator）
        let account_data = &data[8..];

        // 使用 Anchor 的反序列化
        let global_config = match anchor_lang::AnchorDeserialize::try_from_slice(account_data) {
            Ok(config) => config,
            Err(e) => {
                error!("Failed to deserialize GlobalConfig: {}", e);
                return Err(shared::EchoesError::Parse(
                    AccountParseError::deserialization_error(format!("GlobalConfig deserialization failed: {}", e)).to_string()
                ));
            }
        };

        let parsed_data = ParsedAccountData::PumpSwapGlobalConfig {
            address,
            data: global_config,
        };

        Ok(ParsedAccount::new(parsed_data, data.len()))
    }

    /// 从原始数据解析 Pool 账号
    fn parse_pool(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccount> {
        if data.len() < 8 {
            return Err(shared::EchoesError::Parse(
                AccountParseError::insufficient_data(8, data.len()).to_string()
            ));
        }

        // 验证 discriminator
        if data[0..8] != POOL_DISCRIMINATOR {
            return Err(shared::EchoesError::Parse(
                "Invalid Pool discriminator".to_string()
            ));
        }

        let pool = PumpSwapPool::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Pump AMM Pool deserialization failed: {}", e)))?;

        let parsed_data = ParsedAccountData::PumpSwapPool {
            address,
            data: pool,
        };

        Ok(ParsedAccount::new(parsed_data, data.len()))
    }

    /// 验证程序 ID
    fn validate_program_id(&self, program_id: &Pubkey) -> Result<()> {
        if *program_id != PUMP_SWAP_PROGRAM_ID {
            return Err(shared::EchoesError::Parse(
                AccountParseError::unsupported_program_id(*program_id).to_string()
            ));
        }
        Ok(())
    }

    /// 验证账号数据最小长度
    fn validate_min_data_length(&self, data: &[u8], min_length: usize) -> Result<()> {
        if data.len() < min_length {
            return Err(shared::EchoesError::Parse(
                AccountParseError::insufficient_data(min_length, data.len()).to_string()
            ));
        }
        Ok(())
    }
}

#[async_trait]
impl AccountParser for PumpSwapAnchorAccountParser {
    fn name(&self) -> &str {
        &self.name
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        vec![PUMP_SWAP_PROGRAM_ID]
    }

    fn supported_account_types(&self) -> Vec<AccountType> {
        vec![
            AccountType::PumpSwapGlobalConfig,
            AccountType::PumpSwapPool,
        ]
    }

    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool {
        // 检查程序 ID
        if *program_id != PUMP_SWAP_PROGRAM_ID {
            return false;
        }

        // 检查最小数据长度
        if account_data.len() < 8 {
            return false;
        }

        // 检查 discriminator
        identify_account_type(account_data).is_some()
    }

    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType> {
        if !self.can_parse(program_id, account_data) {
            return None;
        }

        match identify_account_type(account_data) {
            Some(PumpSwapAccountType::GlobalConfig) => Some(AccountType::PumpSwapGlobalConfig),
            Some(PumpSwapAccountType::Pool) => Some(AccountType::PumpSwapPool),
            None => None,
        }
    }

    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        debug!(
            "Parsing Pump Swap account: {} for program: {}, data_size: {}",
            address, program_id, account_data.len()
        );

        // 验证程序 ID
        self.validate_program_id(&program_id)?;

        // 验证最小数据长度
        self.validate_min_data_length(account_data, 8)?;

        // 识别账号类型并解析
        match identify_account_type(account_data) {
            Some(PumpSwapAccountType::GlobalConfig) => {
                debug!("Parsing as GlobalConfig account: {}", address);
                self.parse_global_config(address, account_data)
            }
            Some(PumpSwapAccountType::Pool) => {
                debug!("Parsing as Pool account: {}", address);
                self.parse_pool(address, account_data)
            }
            None => {
                warn!(
                    "Unknown Pump Swap account type for account: {}, discriminator: {:?}",
                    address, &account_data[0..8.min(account_data.len())]
                );
                Err(shared::EchoesError::Parse(
                    AccountParseError::unknown_account_type(program_id, account_data.len()).to_string()
                ))
            }
        }
    }

    fn version(&self) -> &str {
        &self.version
    }

    fn priority(&self) -> u8 {
        120 // 高于默认优先级，确保 Anchor 解析器优先使用
    }

    async fn health_check(&self) -> Result<()> {
        debug!("Pump Swap account parser health check passed");
        Ok(())
    }
}
