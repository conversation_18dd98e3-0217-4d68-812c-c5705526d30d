//! Raydium CPMM 账号数据解析器
//!
//! 实现 Raydium CPMM 协议的账号数据解析逻辑

use async_trait::async_trait;
use anchor_lang::prelude::*;
use solana_sdk::pubkey::Pubkey;
use shared::Result;
use std::collections::HashMap;

use crate::accounts::traits::{
    AccountParser, AccountType, ParsedAccount, ParsedAccountData
};
use shared::anchor_types::raydium_cpmm::{
    RAYDIUM_CPMM_PROGRAM_ID, PoolState as RaydiumCpmmPoolState,
    AmmConfig as RaydiumCpmmAmmConfig, ObservationState as RaydiumCpmmObservationState,
    AMM_CONFIG_DISCRIMINATOR, POOL_STATE_DISCRIMINATOR, OBSERVATION_STATE_DISCRIMINATOR
};

/// Raydium CPMM 账号解析器
pub struct RaydiumCpmmAccountParser {
    name: String,
    discriminator_map: HashMap<[u8; 8], AccountType>,
}

impl RaydiumCpmmAccountParser {
    /// 创建新的 Raydium CPMM 账号解析器
    pub fn new() -> Self {
        let mut discriminator_map = HashMap::new();

        // 根据实际的 discriminator 值进行映射
        // 注意：这些值需要从实际部署的程序中获取
        discriminator_map.insert(AMM_CONFIG_DISCRIMINATOR, AccountType::RaydiumCpmmAmmConfig);
        discriminator_map.insert(POOL_STATE_DISCRIMINATOR, AccountType::RaydiumCpmmPoolState);
        discriminator_map.insert(OBSERVATION_STATE_DISCRIMINATOR, AccountType::RaydiumCpmmObservationState);

        Self {
            name: "RaydiumCpmmAccountParser".to_string(),
            discriminator_map,
        }
    }

    /// 使用 discriminator 识别账号类型
    fn identify_account_type_by_discriminator(&self, data: &[u8]) -> Option<AccountType> {
        if data.len() < 8 {
            return None;
        }

        let discriminator: [u8; 8] = data[0..8].try_into().ok()?;
        self.discriminator_map.get(&discriminator).cloned()
    }

    /// 解析 AmmConfig 账号
    fn parse_amm_config(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let amm_config = RaydiumCpmmAmmConfig::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize AmmConfig: {}", e)))?;

        Ok(ParsedAccountData::RaydiumCpmmAmmConfig {
            address,
            data: amm_config,
        })
    }

    /// 解析 PoolState 账号
    fn parse_pool_state(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let pool_state = RaydiumCpmmPoolState::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize PoolState: {}", e)))?;

        Ok(ParsedAccountData::RaydiumCpmmPoolState {
            address,
            data: pool_state,
        })
    }

    /// 解析 ObservationState 账号
    fn parse_observation_state(&self, address: Pubkey, data: &[u8]) -> Result<ParsedAccountData> {
        let observation_state = RaydiumCpmmObservationState::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize ObservationState: {}", e)))?;

        Ok(ParsedAccountData::RaydiumCpmmObservationState {
            address,
            data: observation_state,
        })
    }
}

impl Default for RaydiumCpmmAccountParser {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl AccountParser for RaydiumCpmmAccountParser {
    fn name(&self) -> &str {
        &self.name
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        vec![RAYDIUM_CPMM_PROGRAM_ID]
    }

    fn supported_account_types(&self) -> Vec<AccountType> {
        vec![
            AccountType::RaydiumCpmmAmmConfig,
            AccountType::RaydiumCpmmPoolState,
            AccountType::RaydiumCpmmObservationState,
        ]
    }

    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool {
        *program_id == RAYDIUM_CPMM_PROGRAM_ID &&
        self.identify_account_type_by_discriminator(account_data).is_some()
    }

    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType> {
        if *program_id != RAYDIUM_CPMM_PROGRAM_ID {
            return None;
        }

        self.identify_account_type_by_discriminator(account_data)
    }

    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        if program_id != RAYDIUM_CPMM_PROGRAM_ID {
            return Err(shared::EchoesError::Parse(
                format!("Unsupported program ID: {}", program_id)
            ));
        }

        let account_type = self.identify_account_type_by_discriminator(account_data)
            .ok_or_else(|| shared::EchoesError::Parse(
                format!("Cannot identify account type for data length: {}", account_data.len())
            ))?;

        let parsed_data = match account_type {
            AccountType::RaydiumCpmmAmmConfig => {
                self.parse_amm_config(address, account_data)?
            }
            AccountType::RaydiumCpmmPoolState => {
                self.parse_pool_state(address, account_data)?
            }
            AccountType::RaydiumCpmmObservationState => {
                self.parse_observation_state(address, account_data)?
            }
            _ => {
                return Err(shared::EchoesError::Parse(
                    format!("Unsupported account type: {:?}", account_type)
                ));
            }
        };

        let parsed_account = ParsedAccount::new(
            parsed_data,
            account_data.len(),
        );

        // 验证解析结果
        self.validate_parsed_data(&parsed_account)?;

        Ok(parsed_account)
    }
}