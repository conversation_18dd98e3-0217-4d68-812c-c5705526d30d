//! 账号解析器核心trait和类型定义

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;
use async_trait::async_trait;
use shared::Result;
use shared::anchor_types::raydium::{
    PoolState as RaydiumPoolState, TickArrayState as RaydiumTickArrayState,
    TickArrayBitmapExtension as RaydiumTickArrayBitmapExtension,
    ObservationState as RaydiumObservationState, PersonalPositionState as RaydiumPersonalPosition,
    ProtocolPositionState as RaydiumProtocolPosition
};
use shared::anchor_types::raydium_cpmm::{
    PoolState as RaydiumCpmmPoolState, AmmConfig as RaydiumCpmmAmmConfig,
    ObservationState as RaydiumCpmmObservationState
};
use shared::anchor_types::meteora::{
    LbPair as MeteoraLbPair, BinArray as MeteoraBinArray,
    BinArrayBitmapExtension as MeteoraBinArrayBitmapExtension,
    Oracle as MeteoraOracle, Position as MeteoraPosition,
    PositionV2 as MeteoraPositionV2
};
use shared::anchor_types::meteora_damm::{
    Pool as MeteoraDammPool, Position as MeteoraDammPosition,
    Config as MeteoraDammConfig, ClaimFeeOperator as MeteoraDammClaimFeeOperator,
    TokenBadge as MeteoraDammTokenBadge, Vesting as MeteoraDammVesting
};
use shared::anchor_types::pump_swap::{
    GlobalConfig as PumpSwapGlobalConfig, Pool as PumpSwapPool
};

/// 账号类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Hash)]
pub enum AccountType {
    // Raydium CLMM
    RaydiumPoolState,
    RaydiumTickArrayState,
    RaydiumTickArrayBitmapExtension,
    RaydiumObservationState,
    RaydiumPersonalPosition,
    RaydiumProtocolPosition,
    
    // Raydium CPMM
    RaydiumCpmmPoolState,
    RaydiumCpmmAmmConfig,
    RaydiumCpmmObservationState,

    // Meteora DLMM
    MeteoraLbPair,
    MeteoraBinArray,
    MeteoraBinArrayBitmapExtension,
    MeteoraOracle,
    MeteoraPosition,
    MeteoraPositionV2,

    // Meteora DAMM V2
    MeteoraDammPool,
    MeteoraDammPosition,
    MeteoraDammConfig,
    MeteoraDammClaimFeeOperator,
    MeteoraDammTokenBadge,
    MeteoraDammVesting,

    // Pump Swap
    PumpSwapGlobalConfig,
    PumpSwapPool,

    // 通用类型
    Unknown,
}

impl AccountType {
    /// 获取账号类型的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            AccountType::RaydiumPoolState => "RaydiumPoolState",
            AccountType::RaydiumTickArrayState => "RaydiumTickArrayState",
            AccountType::RaydiumTickArrayBitmapExtension => "RaydiumTickArrayBitmapExtension",
            AccountType::RaydiumObservationState => "RaydiumObservationState",
            AccountType::RaydiumPersonalPosition => "RaydiumPersonalPosition",
            AccountType::RaydiumProtocolPosition => "RaydiumProtocolPosition",
            AccountType::RaydiumCpmmPoolState => "RaydiumCpmmPoolState",
            AccountType::RaydiumCpmmAmmConfig => "RaydiumCpmmAmmConfig",
            AccountType::RaydiumCpmmObservationState => "RaydiumCpmmObservationState",
            AccountType::MeteoraLbPair => "MeteoraLbPair",
            AccountType::MeteoraBinArray => "MeteoraBinArray",
            AccountType::MeteoraBinArrayBitmapExtension => "MeteoraBinArrayBitmapExtension",
            AccountType::MeteoraOracle => "MeteoraOracle",
            AccountType::MeteoraPosition => "MeteoraPosition",
            AccountType::MeteoraPositionV2 => "MeteoraPositionV2",
            AccountType::MeteoraDammPool => "MeteoraDammPool",
            AccountType::MeteoraDammPosition => "MeteoraDammPosition",
            AccountType::MeteoraDammConfig => "MeteoraDammConfig",
            AccountType::MeteoraDammClaimFeeOperator => "MeteoraDammClaimFeeOperator",
            AccountType::MeteoraDammTokenBadge => "MeteoraDammTokenBadge",
            AccountType::MeteoraDammVesting => "MeteoraDammVesting",
            AccountType::PumpSwapGlobalConfig => "PumpSwapGlobalConfig",
            AccountType::PumpSwapPool => "PumpSwapPool",
            AccountType::Unknown => "Unknown",
        }
    }

    /// 判断是否为 Raydium 相关账号类型
    pub fn is_raydium(&self) -> bool {
        matches!(self,
            AccountType::RaydiumPoolState |
            AccountType::RaydiumTickArrayState |
            AccountType::RaydiumTickArrayBitmapExtension |
            AccountType::RaydiumObservationState |
            AccountType::RaydiumPersonalPosition |
            AccountType::RaydiumProtocolPosition |
            AccountType::RaydiumCpmmPoolState |
            AccountType::RaydiumCpmmAmmConfig |
            AccountType::RaydiumCpmmObservationState
        )
    }

    /// 判断是否为 Meteora 相关账号类型
    pub fn is_meteora(&self) -> bool {
        matches!(self,
            AccountType::MeteoraLbPair |
            AccountType::MeteoraBinArray |
            AccountType::MeteoraBinArrayBitmapExtension |
            AccountType::MeteoraOracle |
            AccountType::MeteoraPosition |
            AccountType::MeteoraPositionV2 |
            AccountType::MeteoraDammPool |
            AccountType::MeteoraDammPosition |
            AccountType::MeteoraDammConfig |
            AccountType::MeteoraDammClaimFeeOperator |
            AccountType::MeteoraDammTokenBadge |
            AccountType::MeteoraDammVesting
        )
    }

    /// 判断是否为 Pump Swap 相关账号类型
    pub fn is_pump_swap(&self) -> bool {
        matches!(self,
            AccountType::PumpSwapGlobalConfig |
            AccountType::PumpSwapPool
        )
    }
}

/// 解析后的账号数据枚举
/// 使用简单的枚举替代复杂的 trait object，提供直接访问和类型安全
#[derive(Debug, Clone)]
pub enum ParsedAccountData {
    // Raydium CLMM 账号类型
    RaydiumPoolState {
        address: Pubkey,
        data: RaydiumPoolState,
    },
    RaydiumTickArrayState {
        address: Pubkey,
        data: RaydiumTickArrayState,
    },
    RaydiumTickArrayBitmapExtension {
        address: Pubkey,
        data: RaydiumTickArrayBitmapExtension,
    },
    RaydiumObservationState {
        address: Pubkey,
        data: RaydiumObservationState,
    },
    RaydiumPersonalPosition {
        address: Pubkey,
        data: RaydiumPersonalPosition,
    },
    RaydiumProtocolPosition {
        address: Pubkey,
        data: RaydiumProtocolPosition,
    },

    // Raydium CPMM 账号类型
    RaydiumCpmmPoolState {
        address: Pubkey,
        data: RaydiumCpmmPoolState,
    },
    RaydiumCpmmAmmConfig {
        address: Pubkey,
        data: RaydiumCpmmAmmConfig,
    },
    RaydiumCpmmObservationState {
        address: Pubkey,
        data: RaydiumCpmmObservationState,
    },

    // Meteora DLMM 账号类型
    MeteoraLbPair {
        address: Pubkey,
        data: MeteoraLbPair,
    },
    MeteoraBinArray {
        address: Pubkey,
        data: MeteoraBinArray,
    },
    MeteoraBinArrayBitmapExtension {
        address: Pubkey,
        data: MeteoraBinArrayBitmapExtension,
    },
    MeteoraOracle {
        address: Pubkey,
        data: MeteoraOracle,
    },
    MeteoraPosition {
        address: Pubkey,
        data: MeteoraPosition,
    },
    MeteoraPositionV2 {
        address: Pubkey,
        data: MeteoraPositionV2,
    },

    // Meteora DAMM V2 账号类型
    MeteoraDammPool {
        address: Pubkey,
        data: MeteoraDammPool,
    },
    MeteoraDammPosition {
        address: Pubkey,
        data: MeteoraDammPosition,
    },
    MeteoraDammConfig {
        address: Pubkey,
        data: MeteoraDammConfig,
    },
    MeteoraDammClaimFeeOperator {
        address: Pubkey,
        data: MeteoraDammClaimFeeOperator,
    },
    MeteoraDammTokenBadge {
        address: Pubkey,
        data: MeteoraDammTokenBadge,
    },
    MeteoraDammVesting {
        address: Pubkey,
        data: MeteoraDammVesting,
    },

    // Pump Swap 账号类型
    PumpSwapGlobalConfig {
        address: Pubkey,
        data: PumpSwapGlobalConfig,
    },
    PumpSwapPool {
        address: Pubkey,
        data: PumpSwapPool,
    },

    // 未知类型的备用选项
    Unknown {
        address: Pubkey,
        account_type: AccountType,
        raw_data: Vec<u8>,
    },
}

impl ParsedAccountData {
    /// 获取账号地址
    pub fn address(&self) -> Pubkey {
        match self {
            ParsedAccountData::RaydiumPoolState { address, .. } => *address,
            ParsedAccountData::RaydiumTickArrayState { address, .. } => *address,
            ParsedAccountData::RaydiumTickArrayBitmapExtension { address, .. } => *address,
            ParsedAccountData::RaydiumObservationState { address, .. } => *address,
            ParsedAccountData::RaydiumPersonalPosition { address, .. } => *address,
            ParsedAccountData::RaydiumProtocolPosition { address, .. } => *address,
            ParsedAccountData::RaydiumCpmmPoolState { address, .. } => *address,
            ParsedAccountData::RaydiumCpmmAmmConfig { address, .. } => *address,
            ParsedAccountData::RaydiumCpmmObservationState { address, .. } => *address,
            ParsedAccountData::MeteoraLbPair { address, .. } => *address,
            ParsedAccountData::MeteoraBinArray { address, .. } => *address,
            ParsedAccountData::MeteoraBinArrayBitmapExtension { address, .. } => *address,
            ParsedAccountData::MeteoraOracle { address, .. } => *address,
            ParsedAccountData::MeteoraPosition { address, .. } => *address,
            ParsedAccountData::MeteoraPositionV2 { address, .. } => *address,
            ParsedAccountData::MeteoraDammPool { address, .. } => *address,
            ParsedAccountData::MeteoraDammPosition { address, .. } => *address,
            ParsedAccountData::MeteoraDammConfig { address, .. } => *address,
            ParsedAccountData::MeteoraDammClaimFeeOperator { address, .. } => *address,
            ParsedAccountData::MeteoraDammTokenBadge { address, .. } => *address,
            ParsedAccountData::MeteoraDammVesting { address, .. } => *address,
            ParsedAccountData::PumpSwapGlobalConfig { address, .. } => *address,
            ParsedAccountData::PumpSwapPool { address, .. } => *address,
            ParsedAccountData::Unknown { address, .. } => *address,
        }
    }

    /// 获取账号类型
    pub fn account_type(&self) -> AccountType {
        match self {
            ParsedAccountData::RaydiumPoolState { .. } => AccountType::RaydiumPoolState,
            ParsedAccountData::RaydiumTickArrayState { .. } => AccountType::RaydiumTickArrayState,
            ParsedAccountData::RaydiumTickArrayBitmapExtension { .. } => AccountType::RaydiumTickArrayBitmapExtension,
            ParsedAccountData::RaydiumObservationState { .. } => AccountType::RaydiumObservationState,
            ParsedAccountData::RaydiumPersonalPosition { .. } => AccountType::RaydiumPersonalPosition,
            ParsedAccountData::RaydiumProtocolPosition { .. } => AccountType::RaydiumProtocolPosition,
            ParsedAccountData::RaydiumCpmmPoolState { .. } => AccountType::RaydiumCpmmPoolState,
            ParsedAccountData::RaydiumCpmmAmmConfig { .. } => AccountType::RaydiumCpmmAmmConfig,
            ParsedAccountData::RaydiumCpmmObservationState { .. } => AccountType::RaydiumCpmmObservationState,
            ParsedAccountData::MeteoraLbPair { .. } => AccountType::MeteoraLbPair,
            ParsedAccountData::MeteoraBinArray { .. } => AccountType::MeteoraBinArray,
            ParsedAccountData::MeteoraBinArrayBitmapExtension { .. } => AccountType::MeteoraBinArrayBitmapExtension,
            ParsedAccountData::MeteoraOracle { .. } => AccountType::MeteoraOracle,
            ParsedAccountData::MeteoraPosition { .. } => AccountType::MeteoraPosition,
            ParsedAccountData::MeteoraPositionV2 { .. } => AccountType::MeteoraPositionV2,
            ParsedAccountData::MeteoraDammPool { .. } => AccountType::MeteoraDammPool,
            ParsedAccountData::MeteoraDammPosition { .. } => AccountType::MeteoraDammPosition,
            ParsedAccountData::MeteoraDammConfig { .. } => AccountType::MeteoraDammConfig,
            ParsedAccountData::MeteoraDammClaimFeeOperator { .. } => AccountType::MeteoraDammClaimFeeOperator,
            ParsedAccountData::MeteoraDammTokenBadge { .. } => AccountType::MeteoraDammTokenBadge,
            ParsedAccountData::MeteoraDammVesting { .. } => AccountType::MeteoraDammVesting,
            ParsedAccountData::PumpSwapGlobalConfig { .. } => AccountType::PumpSwapGlobalConfig,
            ParsedAccountData::PumpSwapPool { .. } => AccountType::PumpSwapPool,
            ParsedAccountData::Unknown { account_type, .. } => account_type.clone(),
        }
    }

    /// 获取程序ID
    pub fn program_id(&self) -> Pubkey {
        match self {
            ParsedAccountData::RaydiumPoolState { .. } |
            ParsedAccountData::RaydiumTickArrayState { .. } |
            ParsedAccountData::RaydiumTickArrayBitmapExtension { .. } |
            ParsedAccountData::RaydiumObservationState { .. } |
            ParsedAccountData::RaydiumPersonalPosition { .. } |
            ParsedAccountData::RaydiumProtocolPosition { .. } => {
                shared::anchor_types::raydium::RAYDIUM_CLMM_PROGRAM_ID
            },
            ParsedAccountData::RaydiumCpmmPoolState { .. } |
            ParsedAccountData::RaydiumCpmmAmmConfig { .. } |
            ParsedAccountData::RaydiumCpmmObservationState { .. } => {
                shared::anchor_types::raydium_cpmm::RAYDIUM_CPMM_PROGRAM_ID
            },
            ParsedAccountData::MeteoraLbPair { .. } |
            ParsedAccountData::MeteoraBinArray { .. } |
            ParsedAccountData::MeteoraBinArrayBitmapExtension { .. } |
            ParsedAccountData::MeteoraOracle { .. } |
            ParsedAccountData::MeteoraPosition { .. } |
            ParsedAccountData::MeteoraPositionV2 { .. } => {
                shared::anchor_types::meteora::METEORA_DLMM_PROGRAM_ID
            },
            ParsedAccountData::MeteoraDammPool { .. } |
            ParsedAccountData::MeteoraDammPosition { .. } |
            ParsedAccountData::MeteoraDammConfig { .. } |
            ParsedAccountData::MeteoraDammClaimFeeOperator { .. } |
            ParsedAccountData::MeteoraDammTokenBadge { .. } |
            ParsedAccountData::MeteoraDammVesting { .. } => {
                shared::anchor_types::meteora_damm::METEORA_DAMM_V2_PROGRAM_ID
            },
            ParsedAccountData::PumpSwapGlobalConfig { .. } |
            ParsedAccountData::PumpSwapPool { .. } => {
                shared::anchor_types::pump_swap::PUMP_SWAP_PROGRAM_ID
            },
            ParsedAccountData::Unknown { .. } => Pubkey::default(),
        }
    }

    /// 获取账号的显示名称
    pub fn display_name(&self) -> String {
        format!("{}:{}", self.account_type().as_str(), self.address())
    }


    /// 获取相关联的 mint 地址（如果适用）
    pub fn associated_mints(&self) -> Vec<Pubkey> {
        match self {
            ParsedAccountData::RaydiumPoolState { data, .. } => {
                vec![data.token_mint_0, data.token_mint_1]
            },
            ParsedAccountData::RaydiumCpmmPoolState { data, .. } => {
                vec![data.token0_mint, data.token1_mint]
            },
            ParsedAccountData::MeteoraLbPair { data, .. } => {
                vec![data.token_x_mint, data.token_y_mint]
            },
            ParsedAccountData::MeteoraDammPool { data, .. } => {
                vec![data.token_a_mint, data.token_b_mint]
            },
            ParsedAccountData::MeteoraDammTokenBadge { data, .. } => {
                vec![data.token_mint]
            },
            ParsedAccountData::PumpSwapPool { data, .. } => {
                vec![data.base_mint, data.quote_mint]
            },
            _ => vec![],
        }
    }

    /// 获取账号状态（如果适用）
    pub fn status(&self) -> Option<u8> {
        match self {
            ParsedAccountData::RaydiumPoolState { data, .. } => Some(data.status),
            ParsedAccountData::RaydiumCpmmPoolState { data, .. } => Some(data.status),
            ParsedAccountData::MeteoraLbPair { data, .. } => Some(data.status),
            ParsedAccountData::MeteoraDammPool { data, .. } => Some(data.pool_status),
            ParsedAccountData::MeteoraDammConfig { data, .. } => Some(data.config_type),
            _ => None,
        }
    }

    /// 获取账号创建者（如果适用）
    pub fn creator(&self) -> Option<Pubkey> {
        match self {
            ParsedAccountData::RaydiumPoolState { data, .. } => Some(data.owner),
            ParsedAccountData::RaydiumCpmmPoolState { data, .. } => Some(data.pool_creator),
            ParsedAccountData::MeteoraLbPair { data, .. } => Some(data.creator),
            ParsedAccountData::MeteoraDammPool { data, .. } => Some(data.partner),
            ParsedAccountData::MeteoraDammConfig { data, .. } => Some(data.pool_creator_authority),
            ParsedAccountData::MeteoraDammClaimFeeOperator { data, .. } => Some(data.operator),
            ParsedAccountData::PumpSwapPool { data, .. } => Some(data.creator),
            _ => None,
        }
    }
}

/// 解析后的账号数据容器
#[derive(Debug, Clone)]
pub struct ParsedAccount {
    /// 解析后的账号数据
    pub data: ParsedAccountData,
    /// 原始数据大小
    pub raw_data_size: usize,
    /// 解析时间戳（毫秒）
    pub parsed_at: i64,
    /// 数据版本（用于跟踪变更）
    pub version: u64,
}

impl ParsedAccount {
    /// 创建新的解析账号
    pub fn new(
        data: ParsedAccountData,
        raw_data_size: usize,
    ) -> Self {
        Self {
            data,
            raw_data_size,
            parsed_at: chrono::Utc::now().timestamp_millis(),
            version: 1,
        }
    }

    /// 更新账号数据
    pub fn update_data(&mut self, new_data: ParsedAccountData) {
        self.data = new_data;
        self.version += 1;
        self.parsed_at = chrono::Utc::now().timestamp_millis();
    }

    /// 获取账号地址
    pub fn address(&self) -> Pubkey {
        self.data.address()
    }

    /// 获取程序ID
    pub fn program_id(&self) -> Pubkey {
        self.data.program_id()
    }

    /// 获取账号类型
    pub fn account_type(&self) -> AccountType {
        self.data.account_type()
    }

    /// 获取账号摘要信息
    pub fn summary(&self) -> AccountSummary {
        AccountSummary {
            address: self.address(),
            account_type: self.account_type(),
            program_id: self.program_id(),
            data_size: self.raw_data_size,
            version: self.version,
            last_updated: self.parsed_at,
        }
    }
}

/// 账号摘要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountSummary {
    pub address: Pubkey,
    pub account_type: AccountType,
    pub program_id: Pubkey,
    pub data_size: usize,
    pub version: u64,
    pub last_updated: i64,
}

/// 账号解析器trait
#[async_trait]
pub trait AccountParser: Send + Sync {
    /// 获取解析器名称
    fn name(&self) -> &str;

    /// 获取支持的程序ID
    fn supported_program_ids(&self) -> Vec<Pubkey>;

    /// 获取支持的账号类型
    fn supported_account_types(&self) -> Vec<AccountType>;

    /// 检查是否可以解析指定的账号
    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool;

    /// 识别账号类型
    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType>;

    /// 解析账号数据
    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount>;

    /// 批量解析账号数据
    async fn parse_accounts(
        &self,
        accounts: Vec<(Pubkey, Pubkey, Vec<u8>)>, // (address, program_id, data)
    ) -> Result<Vec<ParsedAccount>> {
        let mut parsed_accounts = Vec::new();

        for (address, program_id, data) in accounts {
            match self.parse_account(address, program_id, &data).await {
                Ok(parsed) => parsed_accounts.push(parsed),
                Err(e) => {
                    tracing::warn!(
                        "Failed to parse account {} for program {}: {}",
                        address, program_id, e
                    );
                }
            }
        }

        Ok(parsed_accounts)
    }

    /// 验证解析后的数据
    fn validate_parsed_data(&self, parsed: &ParsedAccount) -> Result<()> {
        // 默认实现：基础验证
        if parsed.data.address() != parsed.address() {
            return Err(shared::EchoesError::Parse(
                "Address mismatch in parsed account data".to_string()
            ));
        }

        if parsed.data.program_id() != parsed.program_id() {
            return Err(shared::EchoesError::Parse(
                "Program ID mismatch in parsed account data".to_string()
            ));
        }

        Ok(())
    }

    /// 获取解析器版本
    fn version(&self) -> &str {
        "1.0.0"
    }

    /// 获取解析器优先级（数字越大优先级越高）
    fn priority(&self) -> u8 {
        100
    }

    /// 健康检查
    async fn health_check(&self) -> Result<()> {
        Ok(())
    }

    /// 获取支持的数据版本（针对有版本变化的账号结构）
    fn supported_data_versions(&self) -> Vec<u8> {
        vec![1]
    }
}

/// 账号解析错误
#[derive(Debug, thiserror::Error)]
pub enum AccountParseError {
    #[error("不支持的程序ID: {program_id}")]
    UnsupportedProgramId { program_id: Pubkey },

    #[error("无法识别账号类型: program_id={program_id}, data_size={data_size}")]
    UnknownAccountType { program_id: Pubkey, data_size: usize },

    #[error("数据长度不足: 需要 {required} 字节，但只有 {actual} 字节")]
    InsufficientData { required: usize, actual: usize },

    #[error("数据版本不支持: {version}")]
    UnsupportedDataVersion { version: u8 },

    #[error("反序列化失败: {message}")]
    DeserializationError { message: String },

    #[error("数据验证失败: {message}")]
    ValidationError { message: String },

    #[error("通用解析错误: {message}")]
    Generic { message: String },
}

impl AccountParseError {
    pub fn unsupported_program_id(program_id: Pubkey) -> Self {
        Self::UnsupportedProgramId { program_id }
    }

    pub fn unknown_account_type(program_id: Pubkey, data_size: usize) -> Self {
        Self::UnknownAccountType { program_id, data_size }
    }

    pub fn insufficient_data(required: usize, actual: usize) -> Self {
        Self::InsufficientData { required, actual }
    }

    pub fn unsupported_data_version(version: u8) -> Self {
        Self::UnsupportedDataVersion { version }
    }

    pub fn deserialization_error(message: impl Into<String>) -> Self {
        Self::DeserializationError { message: message.into() }
    }

    pub fn validation_error(message: impl Into<String>) -> Self {
        Self::ValidationError { message: message.into() }
    }

    pub fn generic(message: impl Into<String>) -> Self {
        Self::Generic { message: message.into() }
    }
}

/// 账号解析统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct AccountParseStats {
    /// 总解析次数
    pub total_parses: u64,
    /// 成功解析次数
    pub successful_parses: u64,
    /// 失败解析次数
    pub failed_parses: u64,
    /// 按账号类型分组的解析次数
    pub parses_by_type: HashMap<AccountType, u64>,
    /// 总解析时间（毫秒）
    pub total_parse_time_ms: u64,
    /// 平均解析时间（毫秒）
    pub average_parse_time_ms: f64,
    /// 最后解析时间
    pub last_parse_time: Option<i64>,
}

impl AccountParseStats {
    /// 记录成功解析
    pub fn record_success(&mut self, account_type: AccountType, duration_ms: u64) {
        self.total_parses += 1;
        self.successful_parses += 1;
        self.total_parse_time_ms += duration_ms;
        self.average_parse_time_ms = self.total_parse_time_ms as f64 / self.total_parses as f64;
        self.last_parse_time = Some(chrono::Utc::now().timestamp_millis());

        *self.parses_by_type.entry(account_type).or_insert(0) += 1;
    }

    /// 记录解析失败
    pub fn record_failure(&mut self, duration_ms: u64) {
        self.total_parses += 1;
        self.failed_parses += 1;
        self.total_parse_time_ms += duration_ms;
        self.average_parse_time_ms = self.total_parse_time_ms as f64 / self.total_parses as f64;
        self.last_parse_time = Some(chrono::Utc::now().timestamp_millis());
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_parses == 0 {
            0.0
        } else {
            self.successful_parses as f64 / self.total_parses as f64
        }
    }
}
