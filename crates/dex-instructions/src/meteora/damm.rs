use borsh::{<PERSON>rshDeserialize, Borsh<PERSON>erialize};
use solana_sdk::{
    instruction::{AccountMeta, Instruction},
    pubkey,
    pubkey::Pubkey,
};
use spl_associated_token_account::get_associated_token_address;
use shared::ArbitrageError;
use crate::traits::SwapInstructionBuilder;

const METEORA_DAMM_PROGRAM_ID: Pubkey = pubkey!("cpamdpZCGKUy5JxQXB4dcpGPiikHawvSWAd6mEn1sGG");
const POOL_AUTHORITY: Pubkey = pubkey!("HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC");
const TOKEN_PROGRAM_ID: Pubkey = pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");

pub const SWAP_IX_DISCM: [u8; 8] = [248, 198, 158, 145, 225, 117, 135, 200];

#[derive(Bo<PERSON><PERSON><PERSON><PERSON><PERSON>ize, Bo<PERSON><PERSON><PERSON>erialize, <PERSON><PERSON>, Debug, PartialEq)]
pub struct SwapParameters {
    pub amount_in: u64,
    pub minimum_amount_out: u64,
}

#[derive(Clone, Debug, PartialEq)]
pub struct SwapIxData(pub SwapParameters);

impl SwapIxData {
    pub fn new(amount_in: u64, minimum_amount_out: u64) -> Self {
        Self(SwapParameters {
            amount_in,
            minimum_amount_out,
        })
    }

    pub fn try_to_vec(&self) -> Result<Vec<u8>, std::io::Error> {
        let mut data = Vec::new();
        data.extend_from_slice(&SWAP_IX_DISCM);
        self.0.serialize(&mut data)?;
        Ok(data)
    }
}

#[derive(Clone, Debug)]
pub struct MeteoraDAMMPool {
    pub pool: Pubkey,
    pub token_a_vault: Pubkey,
    pub token_b_vault: Pubkey,
    pub token_a_mint: Pubkey,
    pub token_b_mint: Pubkey,
}

#[derive(Copy, Clone, Debug, PartialEq)]
pub struct SwapKeys {
    pub pool_authority: Pubkey,
    pub pool: Pubkey,
    pub input_token_account: Pubkey,
    pub output_token_account: Pubkey,
    pub token_a_vault: Pubkey,
    pub token_b_vault: Pubkey,
    pub token_a_mint: Pubkey,
    pub token_b_mint: Pubkey,
    pub payer: Pubkey,
    pub token_a_program: Pubkey,
    pub token_b_program: Pubkey,
    pub referral_token_account: Option<Pubkey>,
    pub event_authority: Pubkey,
    pub program: Pubkey,
}

impl SwapKeys {
    pub fn to_account_metas(&self) -> Vec<AccountMeta> {
        let mut accounts = vec![
            AccountMeta::new_readonly(self.pool_authority, false),
            AccountMeta::new(self.pool, false),
            AccountMeta::new(self.input_token_account, false),
            AccountMeta::new(self.output_token_account, false),
            AccountMeta::new(self.token_a_vault, false),
            AccountMeta::new(self.token_b_vault, false),
            AccountMeta::new_readonly(self.token_a_mint, false),
            AccountMeta::new_readonly(self.token_b_mint, false),
            AccountMeta::new_readonly(self.payer, true),
            AccountMeta::new_readonly(self.token_a_program, false),
            AccountMeta::new_readonly(self.token_b_program, false),
        ];

        if let Some(referral) = self.referral_token_account {
            accounts.push(AccountMeta::new(referral, false));
        }

        accounts.extend_from_slice(&[
            AccountMeta::new_readonly(self.event_authority, false),
            AccountMeta::new_readonly(self.program, false),
        ]);

        accounts
    }
}

pub struct MeteoraDAMMSwapInstruction;

impl MeteoraDAMMSwapInstruction {
    pub fn derive_event_authority_pda() -> (Pubkey, u8) {
        Pubkey::find_program_address(&[b"__event_authority"], &METEORA_DAMM_PROGRAM_ID)
    }

    pub fn build_swap_instruction(
        pool_data: &MeteoraDAMMPool,
        user: Pubkey,
        amount_in: u64,
        minimum_amount_out: u64,
        swap_a_to_b: bool,
        referral: Option<Pubkey>,
    ) -> Result<Instruction, ArbitrageError> {
        let (event_authority, _) = Self::derive_event_authority_pda();

        let (input_token_account, output_token_account) = if swap_a_to_b {
            (
                get_associated_token_address(&user, &pool_data.token_a_mint),
                get_associated_token_address(&user, &pool_data.token_b_mint),
            )
        } else {
            (
                get_associated_token_address(&user, &pool_data.token_b_mint),
                get_associated_token_address(&user, &pool_data.token_a_mint),
            )
        };

        let referral_token_account = referral.map(|ref_user| {
            if swap_a_to_b {
                get_associated_token_address(&ref_user, &pool_data.token_b_mint)
            } else {
                get_associated_token_address(&ref_user, &pool_data.token_a_mint)
            }
        });

        let swap_keys = SwapKeys {
            pool_authority: POOL_AUTHORITY,
            pool: pool_data.pool,
            input_token_account,
            output_token_account,
            token_a_vault: pool_data.token_a_vault,
            token_b_vault: pool_data.token_b_vault,
            token_a_mint: pool_data.token_a_mint,
            token_b_mint: pool_data.token_b_mint,
            payer: user,
            token_a_program: TOKEN_PROGRAM_ID,
            token_b_program: TOKEN_PROGRAM_ID,
            referral_token_account,
            event_authority,
            program: METEORA_DAMM_PROGRAM_ID,
        };

        let accounts = swap_keys.to_account_metas();

        let data = SwapIxData::new(amount_in, minimum_amount_out)
            .try_to_vec()
            .map_err(|e| ArbitrageError::Serialization(format!("Failed to serialize swap instruction data: {}", e)))?;

        Ok(Instruction {
            program_id: METEORA_DAMM_PROGRAM_ID,
            accounts,
            data,
        })
    }
}

impl SwapInstructionBuilder for MeteoraDAMMSwapInstruction {
    fn build_buy_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        _slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Err(ArbitrageError::UnsupportedOperation(
            "Meteora DAMM买入指令需要额外的池状态信息，请使用build_swap_instruction".to_string()
        ))
    }

    fn build_sell_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        _slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Err(ArbitrageError::UnsupportedOperation(
            "Meteora DAMM卖出指令需要额外的池状态信息，请使用build_swap_instruction".to_string()
        ))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    #[test]
    fn test_swap_data_serialization() {
        let data = SwapIxData::new(1000000, 950000);
        let serialized = data.try_to_vec().unwrap();
        
        assert_eq!(&serialized[0..8], &SWAP_IX_DISCM);
        assert!(serialized.len() > 8);
    }

    #[test]
    fn test_derive_event_authority() {
        let (event_authority, _) = MeteoraDAMMSwapInstruction::derive_event_authority_pda();
        assert_ne!(event_authority, Pubkey::default());
    }

    #[test]
    fn test_swap_keys_to_account_metas() {
        let user = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let pool = Pubkey::default();
        
        let swap_keys = SwapKeys {
            pool_authority: POOL_AUTHORITY,
            pool,
            input_token_account: Pubkey::default(),
            output_token_account: Pubkey::default(),
            token_a_vault: Pubkey::default(),
            token_b_vault: Pubkey::default(),
            token_a_mint: Pubkey::default(),
            token_b_mint: Pubkey::default(),
            payer: user,
            token_a_program: TOKEN_PROGRAM_ID,
            token_b_program: TOKEN_PROGRAM_ID,
            referral_token_account: None,
            event_authority: Pubkey::default(),
            program: METEORA_DAMM_PROGRAM_ID,
        };

        let account_metas = swap_keys.to_account_metas();
        assert_eq!(account_metas.len(), 13); // 没有referral的情况
        assert_eq!(account_metas[8].pubkey, user);
        assert!(account_metas[8].is_signer);
    }

    #[test]
    fn test_swap_keys_with_referral() {
        let user = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let pool = Pubkey::default();
        let referral = Pubkey::new_unique();
        
        let swap_keys = SwapKeys {
            pool_authority: POOL_AUTHORITY,
            pool,
            input_token_account: Pubkey::default(),
            output_token_account: Pubkey::default(),
            token_a_vault: Pubkey::default(),
            token_b_vault: Pubkey::default(),
            token_a_mint: Pubkey::default(),
            token_b_mint: Pubkey::default(),
            payer: user,
            token_a_program: TOKEN_PROGRAM_ID,
            token_b_program: TOKEN_PROGRAM_ID,
            referral_token_account: Some(referral),
            event_authority: Pubkey::default(),
            program: METEORA_DAMM_PROGRAM_ID,
        };

        let account_metas = swap_keys.to_account_metas();
        assert_eq!(account_metas.len(), 14); // 有referral的情况
        assert_eq!(account_metas[11].pubkey, referral);
    }
}