use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::{
    instruction::{AccountMeta, Instruction},
    pubkey,
    pubkey::Pubkey,
};
use spl_associated_token_account::get_associated_token_address;
use shared::{ArbitrageError, anchor_types::meteora::LbPair};
use crate::traits::SwapInstructionBuilder;

const METEORA_DLMM_PROGRAM_ID: Pubkey = pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo");
const TOKEN_PROGRAM_ID: Pubkey = pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
const BIN_ARRAY_SEED: &[u8] = b"bin_array";
const BIN_ARRAY_BITMAP_EXTENSION_SEED: &[u8] = b"bitmap";

#[derive(Debug, Clone)]
pub struct MeteoraSwapParams {
    pub lb_pair: Pubkey,
    pub amount_in: u64,
    pub min_amount_out: u64,
    pub swap_for_y: bool,
}

#[derive(<PERSON><PERSON>, Debug, BorshDeserialize, BorshSerialize, PartialEq)]
pub struct RemainingAccountsSlice {
    pub accounts_type: AccountsType,
    pub length: u8,
}

#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq)]
pub struct RemainingAccountsInfo {
    pub slices: Vec<RemainingAccountsSlice>,
}

#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq)]
pub enum AccountsType {
    TransferHookX,
    TransferHookY,
    TransferHookReward,
}

pub const SWAP_IX_DISCM: [u8; 8] = [65, 75, 63, 76, 235, 91, 91, 136];

#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
pub struct SwapIxArgs {
    pub amount_in: u64,
    pub min_amount_out: u64,
    pub remaining_accounts_info: RemainingAccountsInfo,
}

#[derive(Clone, Debug, PartialEq)]
pub struct SwapIxData(pub SwapIxArgs);

impl SwapIxData {
    pub fn new(amount_in: u64, min_amount_out: u64) -> Self {
        Self(SwapIxArgs {
            amount_in,
            min_amount_out,
            remaining_accounts_info: RemainingAccountsInfo { slices: vec![] },
        })
    }

    pub fn try_to_vec(&self) -> Result<Vec<u8>, std::io::Error> {
        let mut data = Vec::new();
        data.extend_from_slice(&SWAP_IX_DISCM);
        self.0.serialize(&mut data)?;
        Ok(data)
    }
}

#[derive(Copy, Clone, Debug, PartialEq)]
pub struct SwapKeys {
    pub lb_pair: Pubkey,
    pub bin_array_bitmap_extension: Pubkey,
    pub reserve_x: Pubkey,
    pub reserve_y: Pubkey,
    pub user_token_in: Pubkey,
    pub user_token_out: Pubkey,
    pub token_x_mint: Pubkey,
    pub token_y_mint: Pubkey,
    pub oracle: Pubkey,
    pub host_fee_in: Pubkey,
    pub user: Pubkey,
    pub token_x_program: Pubkey,
    pub token_y_program: Pubkey,
    pub memo_program: Pubkey,
    pub event_authority: Pubkey,
    pub program: Pubkey,
}

const SWAP_IX_ACCOUNTS_LEN: usize = 16;

impl From<SwapKeys> for [AccountMeta; SWAP_IX_ACCOUNTS_LEN] {
    fn from(keys: SwapKeys) -> Self {
        [
            AccountMeta::new(keys.lb_pair, false),
            AccountMeta::new_readonly(keys.bin_array_bitmap_extension, false),
            AccountMeta::new(keys.reserve_x, false),
            AccountMeta::new(keys.reserve_y, false),
            AccountMeta::new(keys.user_token_in, false),
            AccountMeta::new(keys.user_token_out, false),
            AccountMeta::new_readonly(keys.token_x_mint, false),
            AccountMeta::new_readonly(keys.token_y_mint, false),
            AccountMeta::new(keys.oracle, false),
            AccountMeta::new(keys.host_fee_in, false),
            AccountMeta::new_readonly(keys.user, true),
            AccountMeta::new_readonly(keys.token_x_program, false),
            AccountMeta::new_readonly(keys.token_y_program, false),
            AccountMeta::new_readonly(keys.memo_program, false),
            AccountMeta::new_readonly(keys.event_authority, false),
            AccountMeta::new_readonly(keys.program, false),
        ]
    }
}

pub struct MeteoraDLMMSwapInstruction;

impl MeteoraDLMMSwapInstruction {
    pub fn derive_event_authority_pda() -> (Pubkey, u8) {
        Pubkey::find_program_address(&[b"__event_authority"], &METEORA_DLMM_PROGRAM_ID)
    }

    pub fn derive_bin_array_bitmap_extension(lb_pair: Pubkey) -> (Pubkey, u8) {
        Pubkey::find_program_address(
            &[BIN_ARRAY_BITMAP_EXTENSION_SEED, lb_pair.as_ref()],
            &METEORA_DLMM_PROGRAM_ID,
        )
    }

    pub fn derive_bin_array_address(lb_pair: Pubkey, index: i64) -> (Pubkey, u8) {
        Pubkey::find_program_address(
            &[BIN_ARRAY_SEED, lb_pair.as_ref(), &index.to_le_bytes()],
            &METEORA_DLMM_PROGRAM_ID,
        )
    }

    pub fn build_swap_instruction(
        params: MeteoraSwapParams,
        lb_pair: &LbPair,
        user: Pubkey,
    ) -> Result<Instruction, ArbitrageError> {
        let MeteoraSwapParams {
            lb_pair: lb_pair_address,
            amount_in,
            min_amount_out,
            swap_for_y,
        } = params;

        let (event_authority, _) = Self::derive_event_authority_pda();
        let (bin_array_bitmap_extension, _) = Self::derive_bin_array_bitmap_extension(lb_pair_address);

        let (user_token_in, user_token_out) = if swap_for_y {
            (
                get_associated_token_address(&user, &lb_pair.token_x_mint),
                get_associated_token_address(&user, &lb_pair.token_y_mint),
            )
        } else {
            (
                get_associated_token_address(&user, &lb_pair.token_y_mint),
                get_associated_token_address(&user, &lb_pair.token_x_mint),
            )
        };

        let swap_keys = SwapKeys {
            lb_pair: lb_pair_address,
            bin_array_bitmap_extension,
            reserve_x: lb_pair.reserve_x,
            reserve_y: lb_pair.reserve_y,
            user_token_in,
            user_token_out,
            token_x_mint: lb_pair.token_x_mint,
            token_y_mint: lb_pair.token_y_mint,
            oracle: lb_pair.oracle,
            host_fee_in: Pubkey::default(),
            user,
            token_x_program: TOKEN_PROGRAM_ID,
            token_y_program: TOKEN_PROGRAM_ID,
            memo_program: spl_memo::ID,
            event_authority,
            program: METEORA_DLMM_PROGRAM_ID,
        };

        let main_accounts: [AccountMeta; SWAP_IX_ACCOUNTS_LEN] = swap_keys.into();
        let mut accounts = main_accounts.to_vec();

        let bin_array_index = if lb_pair.active_id >= 0 {
            (lb_pair.active_id as f64 / 70.0).ceil() as i64
        } else {
            (lb_pair.active_id as f64 / 70.0).floor() as i64
        };

        let (bin_array_address, _) = Self::derive_bin_array_address(lb_pair_address, bin_array_index);
        accounts.push(AccountMeta::new(bin_array_address, false));

        let data = SwapIxData::new(amount_in, min_amount_out)
            .try_to_vec()
            .map_err(|e| ArbitrageError::Serialization(format!("Failed to serialize swap instruction data: {}", e)))?;

        Ok(Instruction {
            program_id: METEORA_DLMM_PROGRAM_ID,
            accounts,
            data,
        })
    }
}

impl SwapInstructionBuilder for MeteoraDLMMSwapInstruction {
    fn build_buy_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Err(ArbitrageError::UnsupportedOperation("Meteora买入指令需要额外的池状态信息，请使用build_swap_instruction".to_string()))
    }

    fn build_sell_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Err(ArbitrageError::UnsupportedOperation("Meteora卖出指令需要额外的池状态信息，请使用build_swap_instruction".to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    #[test]
    fn test_swap_data_serialization() {
        let data = SwapIxData::new(1000000, 950000);
        let serialized = data.try_to_vec().unwrap();
        
        assert_eq!(&serialized[0..8], &SWAP_IX_DISCM);
        assert!(serialized.len() > 8);
    }

    #[test]
    fn test_derive_addresses() {
        let lb_pair = Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo").unwrap();
        
        let (event_authority, _) = MeteoraDLMMSwapInstruction::derive_event_authority_pda();
        assert_ne!(event_authority, Pubkey::default());
        
        let (bitmap_extension, _) = MeteoraDLMMSwapInstruction::derive_bin_array_bitmap_extension(lb_pair);
        assert_ne!(bitmap_extension, Pubkey::default());
        
        let (bin_array, _) = MeteoraDLMMSwapInstruction::derive_bin_array_address(lb_pair, 0);
        assert_ne!(bin_array, Pubkey::default());
    }

    #[test]
    fn test_swap_keys_conversion() {
        let user = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let lb_pair = Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo").unwrap();
        
        let swap_keys = SwapKeys {
            lb_pair,
            bin_array_bitmap_extension: Pubkey::default(),
            reserve_x: Pubkey::default(),
            reserve_y: Pubkey::default(),
            user_token_in: Pubkey::default(),
            user_token_out: Pubkey::default(),
            token_x_mint: Pubkey::default(),
            token_y_mint: Pubkey::default(),
            oracle: Pubkey::default(),
            host_fee_in: Pubkey::default(),
            user,
            token_x_program: TOKEN_PROGRAM_ID,
            token_y_program: TOKEN_PROGRAM_ID,
            memo_program: spl_memo::ID,
            event_authority: Pubkey::default(),
            program: METEORA_DLMM_PROGRAM_ID,
        };

        let account_metas: [AccountMeta; SWAP_IX_ACCOUNTS_LEN] = swap_keys.into();
        assert_eq!(account_metas.len(), SWAP_IX_ACCOUNTS_LEN);
        assert_eq!(account_metas[0].pubkey, lb_pair);
        assert_eq!(account_metas[10].pubkey, user);
        assert!(account_metas[10].is_signer);
    }
}