//! PumpSwap 交易指令实现
//!
//! 参考原代码设计，提供简洁的PumpSwap交易指令构建功能

use borsh::{BorshDeserialize, BorshSerialize};
use shared::ArbitrageError;
use solana_sdk::{
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    system_program,
};
use spl_associated_token_account::get_associated_token_address_with_program_id;
use super::traits::SwapInstructionBuilder;

/// PumpSwap 常量定义
pub mod constants {
    use solana_sdk::pubkey::Pubkey;

    pub const WSOL_TOKEN_MINT: Pubkey = solana_sdk::pubkey!("So11111111111111111111111111111111111111112");
    pub const GLOBAL_CONFIG: Pubkey = solana_sdk::pubkey!("ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw");  
    pub const EVENT_AUTHORITY: Pubkey = solana_sdk::pubkey!("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR");
    pub const FEE_RECIPIENT: Pubkey = solana_sdk::pubkey!("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV");
    pub const FEE_RECIPIENT_ATA: Pubkey = solana_sdk::pubkey!("94qWNrtmfn42h3ZjUZwWvK1MEo9uVmmrBPd2hpNjYDjb");
    pub const PUMP_PROGRAM_ID: Pubkey = solana_sdk::pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
    pub const TOKEN_PROGRAM_ID: Pubkey = solana_sdk::pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
    pub const ASSOCIATED_TOKEN_PROGRAM_ID: Pubkey = solana_sdk::pubkey!("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL");

    pub const BUY_DISCRIMINATOR: [u8; 8] = [102, 6, 61, 18, 1, 218, 235, 234];
    pub const SELL_DISCRIMINATOR: [u8; 8] = [51, 230, 133, 164, 1, 127, 131, 173];
    
    /// 基点最大值（100% = 10000基点）
    pub const BASIS_POINTS_MAX: u64 = 10000;
}

/// 卖出指令参数
#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
pub struct SellArgs {
    pub token_amount_in: u64,
    pub min_sol_amount_out: u64,
}

/// 买入指令参数  
#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
pub struct BuyArgs {
    pub token_amount_out: u64,
    pub max_sol_amount_in: u64,
}

/// 交换操作类型
#[derive(Clone, Debug, PartialEq)]
pub enum SwapOperation {
    Buy(BuyArgs),
    Sell(SellArgs),
}

/// 交换指令数据
#[derive(Clone, Debug, PartialEq)]
pub struct SwapInstructionData(SwapOperation);

impl SwapInstructionData {
    pub fn new_buy(token_amount_out: u64, max_sol_amount_in: u64) -> Self {
        Self(SwapOperation::Buy(BuyArgs {
            token_amount_out,
            max_sol_amount_in,
        }))
    }

    pub fn new_sell(token_amount_in: u64, min_sol_amount_out: u64) -> Self {
        Self(SwapOperation::Sell(SellArgs {
            token_amount_in,
            min_sol_amount_out,
        }))
    }

    /// 序列化指令数据
    pub fn serialize(&self) -> Result<Vec<u8>, ArbitrageError> {
        let mut data = Vec::new();
        
        match &self.0 {
            SwapOperation::Buy(args) => {
                data.extend_from_slice(&constants::BUY_DISCRIMINATOR);
                let args_data = borsh::to_vec(args)
                    .map_err(|e| ArbitrageError::Serialization(format!("Buy args serialization failed: {}", e)))?;
                data.extend_from_slice(&args_data);
            }
            SwapOperation::Sell(args) => {
                data.extend_from_slice(&constants::SELL_DISCRIMINATOR);
                let args_data = borsh::to_vec(args)
                    .map_err(|e| ArbitrageError::Serialization(format!("Sell args serialization failed: {}", e)))?;
                data.extend_from_slice(&args_data);
            }
        }
        
        Ok(data)
    }
}

/// PumpSwap 账户结构
#[derive(Debug, Clone)]
pub struct SwapAccounts {
    pub pool: Pubkey,
    pub user: Pubkey, 
    pub global_config: Pubkey,
    pub token_mint: Pubkey,
    pub quote_mint: Pubkey,
    pub user_token_account: Pubkey,
    pub user_quote_account: Pubkey,
    pub pool_token_account: Pubkey,
    pub pool_quote_account: Pubkey,
    pub fee_recipient: Pubkey,
    pub fee_recipient_account: Pubkey,
    pub token_program: Pubkey,
    pub quote_program: Pubkey,
    pub system_program: Pubkey,
    pub associated_token_program: Pubkey,
    pub event_authority: Pubkey,
    pub program_id: Pubkey,
    pub coin_creator_vault_ata: Pubkey,
    pub coin_creator_vault_authority: Pubkey,
}

impl SwapAccounts {
    pub fn new(
        pool: Pubkey,
        user: Pubkey,
        token_mint: Pubkey,
        creator: Pubkey,
    ) -> Self {
        let user_token_account = get_associated_token_address_with_program_id(
            &user,
            &token_mint,
            &constants::TOKEN_PROGRAM_ID,
        );

        let user_quote_account = get_associated_token_address_with_program_id(
            &user,
            &constants::WSOL_TOKEN_MINT,
            &constants::TOKEN_PROGRAM_ID,
        );

        let pool_token_account = get_associated_token_address_with_program_id(
            &pool,
            &token_mint,
            &constants::TOKEN_PROGRAM_ID,
        );

        let pool_quote_account = get_associated_token_address_with_program_id(
            &pool,
            &constants::WSOL_TOKEN_MINT,
            &constants::TOKEN_PROGRAM_ID,
        );

        let (coin_creator_vault_ata, coin_creator_vault_authority) = 
            Self::derive_creator_vault_accounts(creator);

        Self {
            pool,
            user,
            global_config: constants::GLOBAL_CONFIG,
            token_mint,
            quote_mint: constants::WSOL_TOKEN_MINT,
            user_token_account,
            user_quote_account,
            pool_token_account,
            pool_quote_account,
            fee_recipient: constants::FEE_RECIPIENT,
            fee_recipient_account: constants::FEE_RECIPIENT_ATA,
            token_program: constants::TOKEN_PROGRAM_ID,
            quote_program: constants::TOKEN_PROGRAM_ID,
            system_program: system_program::id(),
            associated_token_program: constants::ASSOCIATED_TOKEN_PROGRAM_ID,
            event_authority: constants::EVENT_AUTHORITY,
            program_id: constants::PUMP_PROGRAM_ID,
            coin_creator_vault_ata,
            coin_creator_vault_authority,
        }
    }

    /// 派生创建者金库账户
    fn derive_creator_vault_accounts(creator: Pubkey) -> (Pubkey, Pubkey) {
        let (vault_authority, _) = Pubkey::find_program_address(
            &[b"creator_vault", &creator.to_bytes()],
            &constants::PUMP_PROGRAM_ID,
        );
        
        let vault_ata = get_associated_token_address_with_program_id(
            &vault_authority,
            &constants::WSOL_TOKEN_MINT,
            &constants::TOKEN_PROGRAM_ID,
        );

        (vault_ata, vault_authority)
    }

    /// 转换为AccountMeta数组
    pub fn to_account_metas(&self) -> Vec<AccountMeta> {
        vec![
            AccountMeta::new_readonly(self.pool, false),
            AccountMeta::new(self.user, true),
            AccountMeta::new_readonly(self.global_config, false),
            AccountMeta::new_readonly(self.token_mint, false),
            AccountMeta::new_readonly(self.quote_mint, false),
            AccountMeta::new(self.user_token_account, false),
            AccountMeta::new(self.user_quote_account, false),
            AccountMeta::new(self.pool_token_account, false),
            AccountMeta::new(self.pool_quote_account, false),
            AccountMeta::new_readonly(self.fee_recipient, false),
            AccountMeta::new(self.fee_recipient_account, false),
            AccountMeta::new_readonly(self.token_program, false),
            AccountMeta::new_readonly(self.quote_program, false),
            AccountMeta::new_readonly(self.system_program, false),
            AccountMeta::new_readonly(self.associated_token_program, false),
            AccountMeta::new_readonly(self.event_authority, false),
            AccountMeta::new_readonly(self.program_id, false),
            AccountMeta::new(self.coin_creator_vault_ata, false),
            AccountMeta::new_readonly(self.coin_creator_vault_authority, false),
        ]
    }
}

/// PumpSwap 指令构建器
pub struct PumpSwapInstructionBuilder;

impl SwapInstructionBuilder for PumpSwapInstructionBuilder {
    fn build_buy_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        // 根据滑点调整最小输出
        let adjusted_min_out = min_amount_out * (constants::BASIS_POINTS_MAX - slippage_bps) / constants::BASIS_POINTS_MAX;
        
        // 这里需要从池状态获取creator，暂时使用默认值
        let creator = Pubkey::default(); // TODO: 从池状态获取实际的creator
        
        let accounts = SwapAccounts::new(pool, user, mint, creator);
        let data = SwapInstructionData::new_buy(min_amount_out, amount_in);

        Ok(Instruction {
            program_id: constants::PUMP_PROGRAM_ID,
            accounts: accounts.to_account_metas(),
            data: data.serialize()?,
        })
    }

    fn build_sell_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        // 根据滑点调整最小输出
        let adjusted_min_out = min_amount_out * (constants::BASIS_POINTS_MAX - slippage_bps) / constants::BASIS_POINTS_MAX;
        
        // 这里需要从池状态获取creator，暂时使用默认值
        let creator = Pubkey::default(); // TODO: 从池状态获取实际的creator
        
        let accounts = SwapAccounts::new(pool, user, mint, creator);
        let data = SwapInstructionData::new_sell(amount_in, adjusted_min_out);

        Ok(Instruction {
            program_id: constants::PUMP_PROGRAM_ID,
            accounts: accounts.to_account_metas(),
            data: data.serialize()?,
        })
    }
}

/// 便利函数：创建买入指令
pub fn create_buy_instruction(
    user: Pubkey,
    mint: Pubkey,
    pool: Pubkey,
    sol_amount_in: u64,
    min_token_amount_out: u64,
    slippage_bps: u64,
    creator: Pubkey,
) -> Result<Instruction, ArbitrageError> {
    let accounts = SwapAccounts::new(pool, user, mint, creator);
    let adjusted_min_out = min_token_amount_out * (constants::BASIS_POINTS_MAX - slippage_bps) / constants::BASIS_POINTS_MAX;
    let data = SwapInstructionData::new_buy(adjusted_min_out, sol_amount_in);

    Ok(Instruction {
        program_id: constants::PUMP_PROGRAM_ID,
        accounts: accounts.to_account_metas(),
        data: data.serialize()?,
    })
}

/// 便利函数：创建卖出指令
pub fn create_sell_instruction(
    user: Pubkey,
    mint: Pubkey,
    pool: Pubkey,
    token_amount_in: u64,
    min_sol_amount_out: u64,
    slippage_bps: u64,
    creator: Pubkey,
) -> Result<Instruction, ArbitrageError> {
    let accounts = SwapAccounts::new(pool, user, mint, creator);
    let adjusted_min_out = min_sol_amount_out * (constants::BASIS_POINTS_MAX - slippage_bps) / constants::BASIS_POINTS_MAX;
    let data = SwapInstructionData::new_sell(token_amount_in, adjusted_min_out);

    Ok(Instruction {
        program_id: constants::PUMP_PROGRAM_ID,
        accounts: accounts.to_account_metas(),
        data: data.serialize()?,
    })
}