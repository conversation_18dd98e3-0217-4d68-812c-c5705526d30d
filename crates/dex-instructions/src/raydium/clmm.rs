use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::{
    instruction::{AccountMeta, Instruction},
    pubkey,
    pubkey::Pubkey,
};
use spl_associated_token_account::get_associated_token_address;
use shared::ArbitrageError;
use crate::traits::SwapInstructionBuilder;

const RAYDIUM_CLMM_PROGRAM_ID: Pubkey = pubkey!("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK");
const TOKEN_PROGRAM_ID: Pubkey = pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
const TOKEN_PROGRAM_2022_ID: Pubkey = pubkey!("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb");
const MEMO_PROGRAM_ID: Pubkey = pubkey!("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr");

pub const SWAP_V2_IX_DISCM: [u8; 8] = [43, 4, 237, 11, 26, 201, 30, 98];

#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
pub struct SwapV2Args {
    pub amount: u64,
    pub other_amount_threshold: u64,
    pub sqrt_price_limit_x64: u128,
    pub is_base_input: bool,
}

#[derive(Clone, Debug, PartialEq)]
pub struct SwapV2IxData(pub SwapV2Args);

impl SwapV2IxData {
    pub fn new(
        amount: u64,
        other_amount_threshold: u64,
        sqrt_price_limit_x64: u128,
        is_base_input: bool,
    ) -> Self {
        Self(SwapV2Args {
            amount,
            other_amount_threshold,
            sqrt_price_limit_x64,
            is_base_input,
        })
    }

    pub fn try_to_vec(&self) -> Result<Vec<u8>, std::io::Error> {
        let mut data = Vec::new();
        data.extend_from_slice(&SWAP_V2_IX_DISCM);
        self.0.serialize(&mut data)?;
        Ok(data)
    }
}

#[derive(Clone, Debug)]
pub struct RaydiumClmmPool {
    pub pool_state: Pubkey,
    pub amm_config: Pubkey,
    pub input_vault: Pubkey,
    pub output_vault: Pubkey,
    pub input_vault_mint: Pubkey,
    pub output_vault_mint: Pubkey,
    pub observation_state: Pubkey,
    pub tick_arrays: Vec<Pubkey>, // 动态的tick array列表
}

#[derive(Copy, Clone, Debug, PartialEq)]
pub struct SwapV2Keys {
    pub payer: Pubkey,
    pub amm_config: Pubkey,
    pub pool_state: Pubkey,
    pub input_token_account: Pubkey,
    pub output_token_account: Pubkey,
    pub input_vault: Pubkey,
    pub output_vault: Pubkey,
    pub observation_state: Pubkey,
    pub token_program: Pubkey,
    pub token_program_2022: Pubkey,
    pub memo_program: Pubkey,
    pub input_vault_mint: Pubkey,
    pub output_vault_mint: Pubkey,
}

impl SwapV2Keys {
    pub fn to_account_metas(&self, tick_arrays: &[Pubkey]) -> Vec<AccountMeta> {
        let mut accounts = vec![
            AccountMeta::new_readonly(self.payer, true),
            AccountMeta::new_readonly(self.amm_config, false),
            AccountMeta::new(self.pool_state, false),
            AccountMeta::new(self.input_token_account, false),
            AccountMeta::new(self.output_token_account, false),
            AccountMeta::new(self.input_vault, false),
            AccountMeta::new(self.output_vault, false),
            AccountMeta::new(self.observation_state, false),
            AccountMeta::new_readonly(self.token_program, false),
            AccountMeta::new_readonly(self.token_program_2022, false),
            AccountMeta::new_readonly(self.memo_program, false),
            AccountMeta::new_readonly(self.input_vault_mint, false),
            AccountMeta::new_readonly(self.output_vault_mint, false),
        ];

        // 添加tick arrays作为剩余账户
        for tick_array in tick_arrays {
            accounts.push(AccountMeta::new(*tick_array, false));
        }

        accounts
    }
}

pub struct RaydiumClmmSwapInstruction;

impl RaydiumClmmSwapInstruction {
    /// 构建swap v2指令
    pub fn build_swap_v2_instruction(
        pool_data: &RaydiumClmmPool,
        user: Pubkey,
        amount: u64,
        other_amount_threshold: u64,
        sqrt_price_limit_x64: u128,
        is_base_input: bool,
    ) -> Result<Instruction, ArbitrageError> {
        let (input_token_account, output_token_account) = if is_base_input {
            (
                get_associated_token_address(&user, &pool_data.input_vault_mint),
                get_associated_token_address(&user, &pool_data.output_vault_mint),
            )
        } else {
            (
                get_associated_token_address(&user, &pool_data.output_vault_mint),
                get_associated_token_address(&user, &pool_data.input_vault_mint),
            )
        };

        let swap_keys = SwapV2Keys {
            payer: user,
            amm_config: pool_data.amm_config,
            pool_state: pool_data.pool_state,
            input_token_account,
            output_token_account,
            input_vault: pool_data.input_vault,
            output_vault: pool_data.output_vault,
            observation_state: pool_data.observation_state,
            token_program: TOKEN_PROGRAM_ID,
            token_program_2022: TOKEN_PROGRAM_2022_ID,
            memo_program: MEMO_PROGRAM_ID,
            input_vault_mint: pool_data.input_vault_mint,
            output_vault_mint: pool_data.output_vault_mint,
        };

        let accounts = swap_keys.to_account_metas(&pool_data.tick_arrays);

        let data = SwapV2IxData::new(
            amount,
            other_amount_threshold,
            sqrt_price_limit_x64,
            is_base_input,
        )
        .try_to_vec()
        .map_err(|e| ArbitrageError::Serialization(format!("Failed to serialize swap v2 instruction data: {}", e)))?;

        Ok(Instruction {
            program_id: RAYDIUM_CLMM_PROGRAM_ID,
            accounts,
            data,
        })
    }

    /// 构建买入指令（SOL -> Token）
    pub fn build_buy_with_pool_data(
        pool_data: &RaydiumClmmPool,
        user: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        sqrt_price_limit_x64: Option<u128>,
    ) -> Result<Instruction, ArbitrageError> {
        Self::build_swap_v2_instruction(
            pool_data,
            user,
            amount_in,
            min_amount_out,
            sqrt_price_limit_x64.unwrap_or(0),
            true, // base input (SOL -> Token)
        )
    }

    /// 构建卖出指令（Token -> SOL）
    pub fn build_sell_with_pool_data(
        pool_data: &RaydiumClmmPool,
        user: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        sqrt_price_limit_x64: Option<u128>,
    ) -> Result<Instruction, ArbitrageError> {
        Self::build_swap_v2_instruction(
            pool_data,
            user,
            amount_in,
            min_amount_out,
            sqrt_price_limit_x64.unwrap_or(0),
            true, // base input (Token -> SOL)
        )
    }
}

impl SwapInstructionBuilder for RaydiumClmmSwapInstruction {
    fn build_buy_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        _slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Err(ArbitrageError::UnsupportedOperation(
            "Raydium CLMM买入指令需要完整的池状态信息，请使用build_buy_with_pool_data".to_string()
        ))
    }

    fn build_sell_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        _slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Err(ArbitrageError::UnsupportedOperation(
            "Raydium CLMM卖出指令需要完整的池状态信息，请使用build_sell_with_pool_data".to_string()
        ))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    #[test]
    fn test_swap_v2_data_serialization() {
        let data = SwapV2IxData::new(1000000, 950000, 0, true);
        let serialized = data.try_to_vec().unwrap();

        assert_eq!(&serialized[0..8], &SWAP_V2_IX_DISCM);
        assert!(serialized.len() > 8);
    }

    #[test]
    fn test_swap_v2_keys_to_account_metas() {
        let user = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let pool_state = Pubkey::default();

        let swap_keys = SwapV2Keys {
            payer: user,
            amm_config: Pubkey::default(),
            pool_state,
            input_token_account: Pubkey::default(),
            output_token_account: Pubkey::default(),
            input_vault: Pubkey::default(),
            output_vault: Pubkey::default(),
            observation_state: Pubkey::default(),
            token_program: TOKEN_PROGRAM_ID,
            token_program_2022: TOKEN_PROGRAM_2022_ID,
            memo_program: MEMO_PROGRAM_ID,
            input_vault_mint: Pubkey::default(),
            output_vault_mint: Pubkey::default(),
        };

        let tick_arrays = vec![Pubkey::new_unique(), Pubkey::new_unique()];
        let account_metas = swap_keys.to_account_metas(&tick_arrays);

        assert_eq!(account_metas.len(), 13 + tick_arrays.len()); // 13个固定账户 + tick arrays
        assert_eq!(account_metas[0].pubkey, user);
        assert!(account_metas[0].is_signer);
    }

    #[test]
    fn test_raydium_clmm_pool_creation() {
        let pool = RaydiumClmmPool {
            pool_state: Pubkey::default(),
            amm_config: Pubkey::default(),
            input_vault: Pubkey::default(),
            output_vault: Pubkey::default(),
            input_vault_mint: Pubkey::default(),
            output_vault_mint: Pubkey::default(),
            observation_state: Pubkey::default(),
            tick_arrays: vec![Pubkey::new_unique()],
        };

        assert_eq!(pool.tick_arrays.len(), 1);
    }

    #[test]
    fn test_build_swap_v2_instruction() {
        let user = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let pool_data = RaydiumClmmPool {
            pool_state: Pubkey::default(),
            amm_config: Pubkey::default(),
            input_vault: Pubkey::default(),
            output_vault: Pubkey::default(),
            input_vault_mint: Pubkey::default(),
            output_vault_mint: Pubkey::default(),
            observation_state: Pubkey::default(),
            tick_arrays: vec![Pubkey::new_unique()],
        };

        let instruction = RaydiumClmmSwapInstruction::build_swap_v2_instruction(
            &pool_data,
            user,
            1000000,
            950000,
            0,
            true,
        ).unwrap();

        assert_eq!(instruction.program_id, RAYDIUM_CLMM_PROGRAM_ID);
        assert_eq!(instruction.accounts.len(), 14); // 13个固定账户 + 1个tick array
        assert_eq!(instruction.accounts[0].pubkey, user);
        assert!(instruction.accounts[0].is_signer);
    }
}
