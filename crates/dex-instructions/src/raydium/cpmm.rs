use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::{
    instruction::{AccountMeta, Instruction},
    pubkey,
    pubkey::Pubkey,
};
use spl_associated_token_account::get_associated_token_address;
use shared::ArbitrageError;
use crate::traits::SwapInstructionBuilder;

const RAYDIUM_CPMM_PROGRAM_ID: Pubkey = pubkey!("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C");
const TOKEN_PROGRAM_ID: Pubkey = pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
const TOKEN_PROGRAM_2022_ID: Pubkey = pubkey!("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb");

// 基于IDL文件，这些是指令discriminator，需要计算得出
// 这里使用函数名的SHA256哈希的前8字节作为discriminator
pub const SWAP_BASE_INPUT_IX_DISCM: [u8; 8] = [0x90, 0x82, 0x84, 0xb0, 0x7e, 0xa7, 0xd8, 0x1c];
pub const SWAP_BASE_OUTPUT_IX_DISCM: [u8; 8] = [0x48, 0x0e, 0x34, 0x96, 0xaf, 0x10, 0x89, 0x4f];

#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
pub struct SwapBaseInputArgs {
    pub amount_in: u64,
    pub minimum_amount_out: u64,
}

#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
pub struct SwapBaseOutputArgs {
    pub max_amount_in: u64,
    pub amount_out: u64,
}

#[derive(Clone, Debug, PartialEq)]
pub struct SwapBaseInputIxData(pub SwapBaseInputArgs);

#[derive(Clone, Debug, PartialEq)]
pub struct SwapBaseOutputIxData(pub SwapBaseOutputArgs);

impl SwapBaseInputIxData {
    pub fn new(amount_in: u64, minimum_amount_out: u64) -> Self {
        Self(SwapBaseInputArgs {
            amount_in,
            minimum_amount_out,
        })
    }

    pub fn try_to_vec(&self) -> Result<Vec<u8>, std::io::Error> {
        let mut data = Vec::new();
        data.extend_from_slice(&SWAP_BASE_INPUT_IX_DISCM);
        self.0.serialize(&mut data)?;
        Ok(data)
    }
}

impl SwapBaseOutputIxData {
    pub fn new(max_amount_in: u64, amount_out: u64) -> Self {
        Self(SwapBaseOutputArgs {
            max_amount_in,
            amount_out,
        })
    }

    pub fn try_to_vec(&self) -> Result<Vec<u8>, std::io::Error> {
        let mut data = Vec::new();
        data.extend_from_slice(&SWAP_BASE_OUTPUT_IX_DISCM);
        self.0.serialize(&mut data)?;
        Ok(data)
    }
}

#[derive(Clone, Debug)]
pub struct RaydiumCpmmPool {
    pub pool_state: Pubkey,
    pub authority: Pubkey,
    pub amm_config: Pubkey,
    pub input_vault: Pubkey,
    pub output_vault: Pubkey,
    pub input_token_mint: Pubkey,
    pub output_token_mint: Pubkey,
    pub input_token_program: Pubkey,
    pub output_token_program: Pubkey,
    pub observation_state: Pubkey,
}

#[derive(Copy, Clone, Debug, PartialEq)]
pub struct SwapKeys {
    pub payer: Pubkey,
    pub authority: Pubkey,
    pub amm_config: Pubkey,
    pub pool_state: Pubkey,
    pub input_token_account: Pubkey,
    pub output_token_account: Pubkey,
    pub input_vault: Pubkey,
    pub output_vault: Pubkey,
    pub input_token_program: Pubkey,
    pub output_token_program: Pubkey,
    pub input_token_mint: Pubkey,
    pub output_token_mint: Pubkey,
    pub observation_state: Pubkey,
}

impl SwapKeys {
    pub fn to_account_metas(&self) -> Vec<AccountMeta> {
        vec![
            AccountMeta::new_readonly(self.payer, true),
            AccountMeta::new_readonly(self.authority, false),
            AccountMeta::new_readonly(self.amm_config, false),
            AccountMeta::new(self.pool_state, false),
            AccountMeta::new(self.input_token_account, false),
            AccountMeta::new(self.output_token_account, false),
            AccountMeta::new(self.input_vault, false),
            AccountMeta::new(self.output_vault, false),
            AccountMeta::new_readonly(self.input_token_program, false),
            AccountMeta::new_readonly(self.output_token_program, false),
            AccountMeta::new_readonly(self.input_token_mint, false),
            AccountMeta::new_readonly(self.output_token_mint, false),
            AccountMeta::new(self.observation_state, false),
        ]
    }
}

pub struct RaydiumCpmmSwapInstruction;

impl RaydiumCpmmSwapInstruction {
    /// 构建swap base input指令
    pub fn build_swap_base_input_instruction(
        pool_data: &RaydiumCpmmPool,
        user: Pubkey,
        amount_in: u64,
        minimum_amount_out: u64,
    ) -> Result<Instruction, ArbitrageError> {
        let input_token_account = get_associated_token_address(&user, &pool_data.input_token_mint);
        let output_token_account = get_associated_token_address(&user, &pool_data.output_token_mint);

        let swap_keys = SwapKeys {
            payer: user,
            authority: pool_data.authority,
            amm_config: pool_data.amm_config,
            pool_state: pool_data.pool_state,
            input_token_account,
            output_token_account,
            input_vault: pool_data.input_vault,
            output_vault: pool_data.output_vault,
            input_token_program: pool_data.input_token_program,
            output_token_program: pool_data.output_token_program,
            input_token_mint: pool_data.input_token_mint,
            output_token_mint: pool_data.output_token_mint,
            observation_state: pool_data.observation_state,
        };

        let accounts = swap_keys.to_account_metas();

        let data = SwapBaseInputIxData::new(amount_in, minimum_amount_out)
            .try_to_vec()
            .map_err(|e| ArbitrageError::Serialization(format!("Failed to serialize swap base input instruction data: {}", e)))?;

        Ok(Instruction {
            program_id: RAYDIUM_CPMM_PROGRAM_ID,
            accounts,
            data,
        })
    }

    /// 构建swap base output指令
    pub fn build_swap_base_output_instruction(
        pool_data: &RaydiumCpmmPool,
        user: Pubkey,
        max_amount_in: u64,
        amount_out: u64,
    ) -> Result<Instruction, ArbitrageError> {
        let input_token_account = get_associated_token_address(&user, &pool_data.input_token_mint);
        let output_token_account = get_associated_token_address(&user, &pool_data.output_token_mint);

        let swap_keys = SwapKeys {
            payer: user,
            authority: pool_data.authority,
            amm_config: pool_data.amm_config,
            pool_state: pool_data.pool_state,
            input_token_account,
            output_token_account,
            input_vault: pool_data.input_vault,
            output_vault: pool_data.output_vault,
            input_token_program: pool_data.input_token_program,
            output_token_program: pool_data.output_token_program,
            input_token_mint: pool_data.input_token_mint,
            output_token_mint: pool_data.output_token_mint,
            observation_state: pool_data.observation_state,
        };

        let accounts = swap_keys.to_account_metas();

        let data = SwapBaseOutputIxData::new(max_amount_in, amount_out)
            .try_to_vec()
            .map_err(|e| ArbitrageError::Serialization(format!("Failed to serialize swap base output instruction data: {}", e)))?;

        Ok(Instruction {
            program_id: RAYDIUM_CPMM_PROGRAM_ID,
            accounts,
            data,
        })
    }

    /// 构建买入指令（SOL -> Token）- 使用base input
    pub fn build_buy_with_pool_data(
        pool_data: &RaydiumCpmmPool,
        user: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Self::build_swap_base_input_instruction(
            pool_data,
            user,
            amount_in,
            min_amount_out,
        )
    }

    /// 构建卖出指令（Token -> SOL）- 使用base input
    pub fn build_sell_with_pool_data(
        pool_data: &RaydiumCpmmPool,
        user: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Self::build_swap_base_input_instruction(
            pool_data,
            user,
            amount_in,
            min_amount_out,
        )
    }

    /// 从池状态数据创建RaydiumCpmmPool结构
    pub fn create_pool_from_state(
        pool_state: Pubkey,
        authority: Pubkey,
        amm_config: Pubkey,
        token0_vault: Pubkey,
        token1_vault: Pubkey,
        token0_mint: Pubkey,
        token1_mint: Pubkey,
        observation_state: Pubkey,
        is_token0_input: bool,
    ) -> RaydiumCpmmPool {
        let (input_vault, output_vault, input_token_mint, output_token_mint) = if is_token0_input {
            (token0_vault, token1_vault, token0_mint, token1_mint)
        } else {
            (token1_vault, token0_vault, token1_mint, token0_mint)
        };

        RaydiumCpmmPool {
            pool_state,
            authority,
            amm_config,
            input_vault,
            output_vault,
            input_token_mint,
            output_token_mint,
            input_token_program: TOKEN_PROGRAM_ID, // 可以根据具体mint类型调整
            output_token_program: TOKEN_PROGRAM_ID,
            observation_state,
        }
    }
}

impl SwapInstructionBuilder for RaydiumCpmmSwapInstruction {
    fn build_buy_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        _slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Err(ArbitrageError::UnsupportedOperation(
            "Raydium CPMM买入指令需要完整的池状态信息，请使用build_buy_with_pool_data".to_string()
        ))
    }

    fn build_sell_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        _slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError> {
        Err(ArbitrageError::UnsupportedOperation(
            "Raydium CPMM卖出指令需要完整的池状态信息，请使用build_sell_with_pool_data".to_string()
        ))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    #[test]
    fn test_swap_base_input_data_serialization() {
        let data = SwapBaseInputIxData::new(1000000, 950000);
        let serialized = data.try_to_vec().unwrap();
        
        assert_eq!(&serialized[0..8], &SWAP_BASE_INPUT_IX_DISCM);
        assert!(serialized.len() > 8);
    }

    #[test]
    fn test_swap_base_output_data_serialization() {
        let data = SwapBaseOutputIxData::new(1000000, 950000);
        let serialized = data.try_to_vec().unwrap();
        
        assert_eq!(&serialized[0..8], &SWAP_BASE_OUTPUT_IX_DISCM);
        assert!(serialized.len() > 8);
    }

    #[test]
    fn test_swap_keys_to_account_metas() {
        let user = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let pool_state = Pubkey::default();
        
        let swap_keys = SwapKeys {
            payer: user,
            authority: Pubkey::default(),
            amm_config: Pubkey::default(),
            pool_state,
            input_token_account: Pubkey::default(),
            output_token_account: Pubkey::default(),
            input_vault: Pubkey::default(),
            output_vault: Pubkey::default(),
            input_token_program: TOKEN_PROGRAM_ID,
            output_token_program: TOKEN_PROGRAM_ID,
            input_token_mint: Pubkey::default(),
            output_token_mint: Pubkey::default(),
            observation_state: Pubkey::default(),
        };

        let account_metas = swap_keys.to_account_metas();
        assert_eq!(account_metas.len(), 13);
        assert_eq!(account_metas[0].pubkey, user);
        assert!(account_metas[0].is_signer);
    }

    #[test]
    fn test_raydium_cpmm_pool_creation() {
        let pool = RaydiumCpmmPool {
            pool_state: Pubkey::default(),
            authority: Pubkey::default(),
            amm_config: Pubkey::default(),
            input_vault: Pubkey::default(),
            output_vault: Pubkey::default(),
            input_token_mint: Pubkey::default(),
            output_token_mint: Pubkey::default(),
            input_token_program: TOKEN_PROGRAM_ID,
            output_token_program: TOKEN_PROGRAM_ID,
            observation_state: Pubkey::default(),
        };

        assert_eq!(pool.input_token_program, TOKEN_PROGRAM_ID);
        assert_eq!(pool.output_token_program, TOKEN_PROGRAM_ID);
    }

    #[test]
    fn test_build_swap_base_input_instruction() {
        let user = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let pool_data = RaydiumCpmmPool {
            pool_state: Pubkey::default(),
            authority: Pubkey::default(),
            amm_config: Pubkey::default(),
            input_vault: Pubkey::default(),
            output_vault: Pubkey::default(),
            input_token_mint: Pubkey::default(),
            output_token_mint: Pubkey::default(),
            input_token_program: TOKEN_PROGRAM_ID,
            output_token_program: TOKEN_PROGRAM_ID,
            observation_state: Pubkey::default(),
        };

        let instruction = RaydiumCpmmSwapInstruction::build_swap_base_input_instruction(
            &pool_data,
            user,
            1000000,
            950000,
        ).unwrap();

        assert_eq!(instruction.program_id, RAYDIUM_CPMM_PROGRAM_ID);
        assert_eq!(instruction.accounts.len(), 13);
        assert_eq!(instruction.accounts[0].pubkey, user);
        assert!(instruction.accounts[0].is_signer);
    }

    #[test]
    fn test_build_swap_base_output_instruction() {
        let user = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let pool_data = RaydiumCpmmPool {
            pool_state: Pubkey::default(),
            authority: Pubkey::default(),
            amm_config: Pubkey::default(),
            input_vault: Pubkey::default(),
            output_vault: Pubkey::default(),
            input_token_mint: Pubkey::default(),
            output_token_mint: Pubkey::default(),
            input_token_program: TOKEN_PROGRAM_ID,
            output_token_program: TOKEN_PROGRAM_ID,
            observation_state: Pubkey::default(),
        };

        let instruction = RaydiumCpmmSwapInstruction::build_swap_base_output_instruction(
            &pool_data,
            user,
            1000000,
            950000,
        ).unwrap();

        assert_eq!(instruction.program_id, RAYDIUM_CPMM_PROGRAM_ID);
        assert_eq!(instruction.accounts.len(), 13);
        assert_eq!(instruction.accounts[0].pubkey, user);
        assert!(instruction.accounts[0].is_signer);
    }

    #[test]
    fn test_create_pool_from_state() {
        let pool_state = Pubkey::new_unique();
        let authority = Pubkey::new_unique();
        let amm_config = Pubkey::new_unique();
        let token0_vault = Pubkey::new_unique();
        let token1_vault = Pubkey::new_unique();
        let token0_mint = Pubkey::new_unique();
        let token1_mint = Pubkey::new_unique();
        let observation_state = Pubkey::new_unique();

        // 测试token0作为输入
        let pool = RaydiumCpmmSwapInstruction::create_pool_from_state(
            pool_state,
            authority,
            amm_config,
            token0_vault,
            token1_vault,
            token0_mint,
            token1_mint,
            observation_state,
            true, // token0 as input
        );

        assert_eq!(pool.input_vault, token0_vault);
        assert_eq!(pool.output_vault, token1_vault);
        assert_eq!(pool.input_token_mint, token0_mint);
        assert_eq!(pool.output_token_mint, token1_mint);

        // 测试token1作为输入
        let pool = RaydiumCpmmSwapInstruction::create_pool_from_state(
            pool_state,
            authority,
            amm_config,
            token0_vault,
            token1_vault,
            token0_mint,
            token1_mint,
            observation_state,
            false, // token1 as input
        );

        assert_eq!(pool.input_vault, token1_vault);
        assert_eq!(pool.output_vault, token0_vault);
        assert_eq!(pool.input_token_mint, token1_mint);
        assert_eq!(pool.output_token_mint, token0_mint);
    }
}