//! 交换指令的通用特征定义

use solana_sdk::{instruction::Instruction, pubkey::Pubkey};
use shared::ArbitrageError;

/// 交换指令构建特征
pub trait SwapInstructionBuilder {
    /// 构建买入指令
    /// 
    /// # 参数
    /// * `user` - 用户钱包地址
    /// * `mint` - 代币mint地址  
    /// * `pool` - 池子地址
    /// * `amount_in` - 输入金额（SOL数量）
    /// * `min_amount_out` - 最小输出金额（代币数量）
    /// * `slippage_bps` - 滑点容忍度（基点，例如100表示1%）
    fn build_buy_instruction(
        user: Pubkey,
        mint: Pubkey,
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError>;

    /// 构建卖出指令
    ///
    /// # 参数  
    /// * `user` - 用户钱包地址
    /// * `mint` - 代币mint地址
    /// * `pool` - 池子地址  
    /// * `amount_in` - 输入金额（代币数量）
    /// * `min_amount_out` - 最小输出金额（SOL数量）
    /// * `slippage_bps` - 滑点容忍度（基点，例如100表示1%）
    fn build_sell_instruction(
        user: Pubkey,
        mint: Pubkey, 
        pool: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        slippage_bps: u64,
    ) -> Result<Instruction, ArbitrageError>;
}