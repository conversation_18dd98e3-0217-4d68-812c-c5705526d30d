[package]
name = "onchain-executor"
version = "0.1.0"
edition = "2024"

[dependencies]
# 从workspace继承依赖
tokio = { workspace = true }
futures = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
chrono = { workspace = true }
solana-client.workspace = true
solana-sdk = { workspace = true }
solana-transaction-status = { workspace = true }
spl-token = { workspace = true }
spl-associated-token-account = { workspace = true }
bs58 = { workspace = true }
tracing-subscriber = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
bincode.workspace = true


# 项目内部依赖
shared = { path = "../shared" }
arbitrage-engine = { path = "../arbitrage-engine" }
dex-instructions = { path = "../dex-instructions" }

# 新增依赖
uuid = { version = "1.0", features = ["v4"] }
