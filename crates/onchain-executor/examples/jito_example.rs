use onchain_executor::{<PERSON><PERSON>S<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, JITO_TIP_ACCOUNTS};
use solana_sdk::{
    signature::{Keypair, Signer},
    system_instruction,
    transaction::Transaction,
    pubkey::Pubkey,
    commitment_config::CommitmentConfig,
};
use std::{str::FromStr, env};
use std::path::PathBuf;
use shared::KeypairVault;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 Jito发送示例程序");
    println!("===================");

    // 从环境变量获取RPC URL，默认使用公共节点
    let rpc_url = "https://mainnet.helius-rpc.com/?api-key=d836ddde-cf6c-452c-8fb5-b08e06fe3092".to_string();

    println!("📡 RPC URL: {}", rpc_url);

    // 创建Jito发送器
    let jito_sender = JitoSender::new(&rpc_url, None)
        .with_commitment(CommitmentConfig::confirmed());

    // 显示可用的Jito小费账户
    println!("\n💰 可用的Jito小费账户:");
    for (i, account) in JITO_TIP_ACCOUNTS.iter().enumerate() {
        println!("  {}. {}", i + 1, account);
    }

    // 创建一个示例密钥对（在实际使用中，你应该加载真实的密钥）
    let vault_path = PathBuf::new().join("crates/shared/.real_vault.bin");

    let pw = "TestPassword123!";
    let loaded_vault = KeypairVault::load(&vault_path).unwrap();
    let keypair = loaded_vault.decrypt(pw).unwrap();
    println!("\n🔑 使用的钱包地址: {}", keypair.pubkey());

    // 创建一个简单的转账交易作为示例
    let recipient = Pubkey::from_str("7kvsMUa42H2mdzDqxvW1hym1Rz6KDqpUjMCoweFtn7M8")?;
    let transfer_amount = 1000; // 1000 lamports

    let transfer_instruction = system_instruction::transfer(
        &keypair.pubkey(),
        &recipient,
        transfer_amount,
    );

    let transaction = Transaction::new_with_payer(
        &[transfer_instruction],
        Some(&keypair.pubkey()),
    );

    println!("\n📋 交易详情:");
    println!("  转账金额: {} lamports", transfer_amount);
    println!("  接收方: {}", recipient);

    // 配置Jito参数
    let jito_tip = 0.001; // 0.001 SOL 作为小费
    let priority_fee = Some(0.0001); // 0.0001 SOL 作为优先费

    println!("\n⚡ Jito配置:");
    println!("  小费: {} SOL", jito_tip);
    println!("  优先费: {} SOL", priority_fee.unwrap_or(0.0));

    // 发送交易到Jito
    match jito_sender.send_tx_jito(
        jito_tip,
        transaction,
        priority_fee,
        &keypair,
    ).await {
        Ok(signature) => {
            println!("\n✅ 交易发送成功!");
            println!("  交易签名: {}", signature);
            println!("  🔍 在Solscan上查看: https://solscan.io/tx/{}", signature);
        }
        Err(e) => {
            println!("\n❌ 交易发送失败:");
            match e {
                JitoError::NoTipSet => {
                    println!("  错误: 未设置Jito小费");
                }
                JitoError::TransactionTooLarge { size, keys } => {
                    println!("  错误: 交易过大 - 大小: {} 字节, 账户数: {}", size, keys);
                }
                JitoError::SendFailed(msg) => {
                    println!("  错误: 发送失败 - {}", msg);
                }
                JitoError::NotConfirmed(sig) => {
                    println!("  错误: 交易未确认 - {}", sig);
                }
                JitoError::RpcError(msg) => {
                    println!("  错误: RPC错误 - {}", msg);
                }
                JitoError::SerializationError(msg) => {
                    println!("  错误: 序列化错误 - {}", msg);
                }
            }
        }
    }

    println!("\n📚 使用说明:");
    println!("1. 确保你的钱包有足够的SOL余额");
    println!("2. 调整小费金额以获得更好的确认速度");
    println!("3. 在高网络拥堵时期，增加优先费");
    println!("4. 监控交易状态以确保成功执行");

    Ok(())
}

/// 演示如何批量发送多个交易
#[allow(dead_code)]
async fn batch_send_example() -> Result<(), Box<dyn std::error::Error>> {
    let rpc_url = "https://api.mainnet-beta.solana.com";
    let jito_sender = JitoSender::new(rpc_url, None);
    let keypair = Keypair::new();

    let transactions = vec![
        // 创建多个交易...
        Transaction::new_with_payer(&[], Some(&keypair.pubkey())),
    ];

    for (i, transaction) in transactions.into_iter().enumerate() {
        println!("发送交易 {}", i + 1);

        let result = jito_sender.send_tx_jito(
            0.001, // 小费
            transaction,
            Some(0.0001), // 优先费
            &keypair,
        ).await;

        match result {
            Ok(sig) => println!("✅ 交易 {} 成功: {}", i + 1, sig),
            Err(e) => println!("❌ 交易 {} 失败: {}", i + 1, e),
        }

        // 在批量发送时添加延迟以避免过载
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }

    Ok(())
}
