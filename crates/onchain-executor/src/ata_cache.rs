//! ATA缓存管理器
//!
//! 智能管理关联代币账户的缓存，消除RPC查询延迟
//! 核心特性：
//! - 批量查询优化：使用getMultipleAccounts
//! - 智能预热：基于监听池子自动预热常用代币
//! - 持久化缓存：JSON文件存储，程序重启快速加载
//! - TTL管理：缓存有效期管理

use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tracing::{info, warn, debug};
use serde_json;
use solana_sdk::pubkey::Pubkey;
use solana_client::nonblocking::rpc_client::RpcClient;
use spl_associated_token_account::get_associated_token_address;

use crate::{ATACacheEntry, ExecutorError, ExecutorResult};

/// ATA缓存管理器
pub struct ATACache {
    /// 内存缓存: mint -> 缓存条目
    cache: Arc<RwLock<HashMap<Pubkey, ATACacheEntry>>>,
    /// RPC客户端
    rpc_client: Arc<RpcClient>,
    /// 钱包公钥
    owner: Pubkey,
    /// 缓存文件路径
    cache_file_path: PathBuf,
    /// 缓存TTL
    ttl: Duration,
}

impl ATACache {
    pub fn new(
        rpc_client: Arc<RpcClient>,
        owner: Pubkey,
        cache_file_path: PathBuf,
        ttl: Duration,
    ) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            rpc_client,
            owner,
            cache_file_path,
            ttl,
        }
    }

    /// 启动时预热 - 使用 getMultipleAccounts 批量查询
    pub async fn initialize_with_preheat(&self) -> ExecutorResult<()> {
        info!("🚀 开始ATA缓存初始化...");

        // 1. 加载JSON文件缓存
        self.load_from_file().await?;

        // 2. 预热核心代币
        let core_tokens = self.get_core_tokens();

        if !core_tokens.is_empty() {
            info!("📋 需要预热 {} 个核心代币的ATA", core_tokens.len());
            self.batch_query_atas(&core_tokens).await?;
        }

        // 3. 异步保存到JSON文件
        self.save_to_file_async().await;

        info!("✅ ATA缓存预热完成");
        Ok(())
    }

    /// 获取核心代币列表
    #[inline]
    fn get_core_tokens(&self) -> Vec<Pubkey> {
        vec![
            // USDC
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".parse().unwrap(),
            // USDT
            "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB".parse().unwrap(),
            // RAY
            "4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R".parse().unwrap(),
        ]
    }

    /// 使用 getMultipleAccounts 批量查询
    async fn batch_query_atas(&self, mints: &[Pubkey]) -> ExecutorResult<()> {
        // 计算所有ATA地址
        let ata_addresses: Vec<_> = mints.iter()
            .map(|&mint| get_associated_token_address(&self.owner, &mint))
            .collect();

        info!("🔍 批量查询 {} 个ATA账户状态", ata_addresses.len());

        // 使用 getMultipleAccounts 一次性查询所有ATA
        let accounts = self.rpc_client
            .get_multiple_accounts(&ata_addresses)
            .await?;

        // 处理查询结果
        let mut cache_updates = HashMap::new();
        let current_time = SystemTime::now();

        for (i, account_option) in accounts.iter().enumerate() {
            let mint = mints[i];
            let ata_address = ata_addresses[i];
            let exists = account_option.is_some();

            let entry = ATACacheEntry {
                ata_address,
                exists,
                last_checked: current_time,
                created_by_us: false,
            };

            cache_updates.insert(mint, entry);

            debug!("ATA状态: {} -> {} (存在: {})", mint, ata_address, exists);
        }

        // 批量更新内存缓存
        let mut cache = self.cache.write().await;
        cache.extend(cache_updates);

        info!("✅ 批量查询完成，更新了 {} 个ATA缓存条目", accounts.len());
        Ok(())
    }

    /// 批量检查ATA存在性（交易构建时使用）
    pub async fn batch_check_atas(&self, mints: &[Pubkey]) -> ExecutorResult<HashMap<Pubkey, bool>> {
        let mut results = HashMap::with_capacity(mints.len());
        let mut unknown_mints = Vec::with_capacity(mints.len());

        // 1. 检查缓存
        {
            let cache = self.cache.read().await;
            let current_time = SystemTime::now();

            for &mint in mints {
                if let Some(entry) = cache.get(&mint) {
                    let age = current_time.duration_since(entry.last_checked)
                        .unwrap_or_default();

                    if age < self.ttl {
                        results.insert(mint, entry.exists);
                        continue;
                    }
                }
                unknown_mints.push(mint);
            }
        }

        // 2. 如果所有都命中缓存，直接返回
        if unknown_mints.is_empty() {
            return Ok(results);
        }

        // 3. 批量查询未知的mints
        info!("需要批量查询 {} 个未知ATA", unknown_mints.len());

        let ata_addresses: Vec<_> = unknown_mints.iter()
            .map(|&mint| get_associated_token_address(&self.owner, &mint))
            .collect();

        let accounts = self.rpc_client
            .get_multiple_accounts(&ata_addresses)
            .await?;

        // 4. 处理结果并更新缓存
        let current_time = SystemTime::now();
        let mut cache_updates = Vec::with_capacity(unknown_mints.len());

        for (i, account_option) in accounts.iter().enumerate() {
            let mint = unknown_mints[i];
            let ata_address = ata_addresses[i];
            let exists = account_option.is_some();

            results.insert(mint, exists);

            let entry = ATACacheEntry {
                ata_address,
                exists,
                last_checked: current_time,
                created_by_us: false,
            };

            cache_updates.push((mint, entry));
        }

        // 5. 批量更新缓存
        {
            let mut cache = self.cache.write().await;
            for (mint, entry) in cache_updates {
                cache.insert(mint, entry);
            }
        }

        // 6. 异步保存
        self.save_to_file_async().await;

        Ok(results)
    }

    /// 记录ATA创建（程序创建时调用）
    pub async fn mark_ata_created(&self, mint: Pubkey, ata: Pubkey) {
        let entry = ATACacheEntry {
            ata_address: ata,
            exists: true,
            last_checked: SystemTime::now(),
            created_by_us: true,
        };

        self.cache.write().await.insert(mint, entry);
        self.save_to_file_async().await;

        info!("✅ 记录ATA创建: {} -> {}", mint, ata);
    }

    /// 从JSON文件加载缓存
    async fn load_from_file(&self) -> ExecutorResult<()> {
        if !self.cache_file_path.exists() {
            info!("缓存文件不存在，跳过加载");
            return Ok(());
        }

        let file_content = tokio::fs::read_to_string(&self.cache_file_path).await
            .map_err(|e| ExecutorError::IoError(e.to_string()))?;

        let cached_data: HashMap<String, ATACacheEntry> = serde_json::from_str(&file_content)?;

        // 转换 String key 为 Pubkey 并过滤过期条目
        let current_time = SystemTime::now();
        let mut valid_entries = HashMap::new();

        for (mint_str, entry) in cached_data {
            // 检查是否过期
            if current_time.duration_since(entry.last_checked)
                .map(|age| age < self.ttl)
                .unwrap_or(false)
            {
                if let Ok(mint_pubkey) = mint_str.parse::<Pubkey>() {
                    valid_entries.insert(mint_pubkey, entry);
                }
            }
        }

        let loaded_count = valid_entries.len();
        *self.cache.write().await = valid_entries;

        info!("📥 从JSON文件加载了 {} 个有效的ATA缓存条目", loaded_count);
        Ok(())
    }

    /// 异步保存到JSON文件（不阻塞主流程）
    async fn save_to_file_async(&self) {
        let cache = self.cache.clone();
        let file_path = self.cache_file_path.clone();

        tokio::spawn(async move {
            let cache_data = cache.read().await.clone();

            // 转换为可序列化格式
            let serializable_data: HashMap<String, ATACacheEntry> = cache_data
                .into_iter()
                .map(|(pubkey, entry)| (pubkey.to_string(), entry))
                .collect();

            if let Ok(json_content) = serde_json::to_string_pretty(&serializable_data) {
                if let Some(parent) = file_path.parent() {
                    let _ = tokio::fs::create_dir_all(parent).await;
                }

                if let Err(e) = tokio::fs::write(&file_path, json_content).await {
                    warn!("异步保存缓存文件失败: {}", e);
                } else {
                    debug!("异步保存缓存文件成功");
                }
            }
        });
    }
}
