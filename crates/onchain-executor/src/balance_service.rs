//! 简化的余额服务
//!
//! 专注于余额查询和ATA管理，不包含策略相关的风险控制逻辑

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tracing::{debug, info};

use solana_sdk::{pubkey::Pubkey, signature::{Keypair, Signer}};
use solana_client::nonblocking::rpc_client::RpcClient;
use spl_associated_token_account::get_associated_token_address;

use crate::{ExecutorError, ExecutorResult};

/// 资产类型枚举，统一处理SOL和SPL代币
#[derive(Clone, Copy, Debug, Eq, PartialEq, Hash)]
pub enum Asset {
    /// 原生SOL
    NativeSol,
    /// SPL代币
    SplToken(Pubkey),
}

/// ATA缓存条目
#[derive(Clone, Debug)]
struct AtaEntry {
    /// ATA地址
    address: Pubkey,
    /// 代币mint
    mint: Pubkey,
    /// 账户是否存在
    exists: bool,
    /// 最后检查时间
    last_checked: SystemTime,
}

/// 简化的余额服务
pub struct BalanceService {
    /// 钱包密钥对
    wallet: Arc<Keypair>,
    /// RPC客户端
    rpc: Arc<RpcClient>,
    /// ATA缓存
    ata_cache: Arc<RwLock<HashMap<Pubkey, AtaEntry>>>,
    /// ATA缓存有效时间
    ata_ttl: Duration,
    /// 最小SOL保留金额 (lamports)
    min_sol_reserve: u64,
    /// 交易费用缓冲 (lamports)
    fee_buffer: u64,
}

impl BalanceService {
    /// 创建新的余额服务
    pub fn new(
        wallet: Arc<Keypair>,
        rpc: Arc<RpcClient>,
        min_sol_reserve: u64,
        fee_buffer: u64,
        ata_ttl: Duration,
    ) -> Self {
        Self {
            wallet,
            rpc,
            ata_cache: Arc::new(RwLock::new(HashMap::new())),
            ata_ttl,
            min_sol_reserve,
            fee_buffer,
        }
    }

    /// 创建具有默认配置的余额服务
    pub fn with_defaults(wallet: Arc<Keypair>, rpc: Arc<RpcClient>) -> Self {
        Self::new(
            wallet,
            rpc,
            50_000_000,                    // 0.05 SOL 最小保留
            15_000_000,                    // 0.015 SOL 费用缓冲
            Duration::from_secs(300),      // 5分钟 ATA缓存有效期
        )
    }

    /// 获取钱包公钥
    pub fn wallet_pubkey(&self) -> Pubkey {
        self.wallet.pubkey()
    }

    /// 获取钱包密钥对引用
    pub fn wallet_keypair(&self) -> Arc<Keypair> {
        self.wallet.clone()
    }

    /// 获取指定资产的余额
    pub async fn get_balance(&self, asset: Asset) -> ExecutorResult<u64> {
        match asset {
            Asset::NativeSol => {
                let balance = self.rpc
                    .get_balance(&self.wallet.pubkey())
                    .await
                    .map_err(|e| ExecutorError::RpcError(e.to_string()))?;

                info!("SOL余额: {} lamports", balance);
                Ok(balance)
            }
            Asset::SplToken(mint) => {
                let ata = self.get_or_refresh_ata(mint).await?;
                if !ata.exists {
                    info!("代币账户 {} 不存在，余额为0", mint);
                    return Ok(0);
                }

                let balance_response = self.rpc
                    .get_token_account_balance(&ata.address)
                    .await
                    .map_err(|e| ExecutorError::RpcError(e.to_string()))?;

                // 使用 amount 字段避免精度丢失
                let raw_amount: u64 = balance_response.amount
                    .parse()
                    .map_err(|e| ExecutorError::ParseError(format!("解析代币余额失败: {}", e)))?;

                info!("代币余额 {}: {} (原始单位)", mint, raw_amount);
                Ok(raw_amount)
            }
        }
    }

    /// 检查是否有足够的资金
    /// 对于SOL会考虑保留金额和费用缓冲
    /// 对于SPL代币只检查余额是否足够
    pub async fn ensure_funds(&self, asset: Asset, needed: u64) -> ExecutorResult<bool> {
        match asset {
            Asset::NativeSol => {
                let balance = self.get_balance(asset).await?;
                let required_total = needed + self.fee_buffer + self.min_sol_reserve;
                let sufficient = balance >= required_total;

                info!(
                    "SOL资金检查: 需要 {} + {} + {} = {}, 可用 {}, 足够: {}",
                    needed, self.fee_buffer, self.min_sol_reserve,
                    required_total, balance, sufficient
                );

                Ok(sufficient)
            }
            Asset::SplToken(_) => {
                let balance = self.get_balance(asset).await?;
                let sufficient = balance >= needed;

                info!(
                    "代币资金检查 {:?}: 需要 {}, 可用 {}, 足够: {}",
                    asset, needed, balance, sufficient
                );

                Ok(sufficient)
            }
        }
    }

    /// 获取可用余额（扣除保留金额和费用后）
    pub async fn get_available_balance(&self, asset: Asset) -> ExecutorResult<u64> {
        match asset {
            Asset::NativeSol => {
                let total_balance = self.get_balance(asset).await?;
                let reserved = self.fee_buffer + self.min_sol_reserve;
                Ok(total_balance.saturating_sub(reserved))
            }
            Asset::SplToken(_) => {
                self.get_balance(asset).await
            }
        }
    }

    /// 获取或刷新ATA信息
    async fn get_or_refresh_ata(&self, mint: Pubkey) -> ExecutorResult<AtaEntry> {
        // 先检查缓存
        {
            let cache = self.ata_cache.read().await;
            if let Some(entry) = cache.get(&mint) {
                let age = entry.last_checked.elapsed().unwrap_or_default();
                if age < self.ata_ttl {
                    return Ok(entry.clone());
                }
            }
        }

        // 缓存过期或不存在，重新查询
        let address = get_associated_token_address(&self.wallet.pubkey(), &mint);
        let exists = self.rpc.get_account(&address).await.is_ok();

        let entry = AtaEntry {
            address,
            mint,
            exists,
            last_checked: SystemTime::now(),
        };

        // 更新缓存
        {
            let mut cache = self.ata_cache.write().await;
            cache.insert(mint, entry.clone());
        }

        debug!("ATA状态刷新: {} -> {} (存在: {})", mint, address, exists);
        Ok(entry)
    }

    /// 获取ATA地址（不检查存在性）
    pub fn get_ata_address(&self, mint: Pubkey) -> Pubkey {
        get_associated_token_address(&self.wallet.pubkey(), &mint)
    }

    /// 批量检查多个代币的ATA存在性
    pub async fn batch_check_atas(&self, mints: &[Pubkey]) -> ExecutorResult<HashMap<Pubkey, bool>> {
        let mut results = HashMap::with_capacity(mints.len());
        let mut unknown_mints = Vec::new();

        // 检查缓存
        {
            let cache = self.ata_cache.read().await;
            let current_time = SystemTime::now();

            for &mint in mints {
                if let Some(entry) = cache.get(&mint) {
                    let age = current_time.duration_since(entry.last_checked)
                        .unwrap_or_default();

                    if age < self.ata_ttl {
                        results.insert(mint, entry.exists);
                        continue;
                    }
                }
                unknown_mints.push(mint);
            }
        }

        // 批量查询未缓存的
        if !unknown_mints.is_empty() {
            debug!("批量查询 {} 个ATA状态", unknown_mints.len());

            let ata_addresses: Vec<_> = unknown_mints.iter()
                .map(|&mint| get_associated_token_address(&self.wallet.pubkey(), &mint))
                .collect();

            let accounts = self.rpc
                .get_multiple_accounts(&ata_addresses)
                .await
                .map_err(|e| ExecutorError::RpcError(e.to_string()))?;

            // 处理结果并更新缓存
            let current_time = SystemTime::now();
            let mut cache_updates = Vec::with_capacity(unknown_mints.len());

            for (i, account_option) in accounts.iter().enumerate() {
                let mint = unknown_mints[i];
                let ata_address = ata_addresses[i];
                let exists = account_option.is_some();

                results.insert(mint, exists);

                let entry = AtaEntry {
                    address: ata_address,
                    mint,
                    exists,
                    last_checked: current_time,
                };

                cache_updates.push((mint, entry));
            }

            // 批量更新缓存
            {
                let mut cache = self.ata_cache.write().await;
                for (mint, entry) in cache_updates {
                    cache.insert(mint, entry);
                }
            }
        }

        Ok(results)
    }

    /// 标记ATA已创建（外部创建ATA后调用）
    pub async fn mark_ata_created(&self, mint: Pubkey) {
        let ata_address = get_associated_token_address(&self.wallet.pubkey(), &mint);
        let entry = AtaEntry {
            address: ata_address,
            mint,
            exists: true,
            last_checked: SystemTime::now(),
        };

        self.ata_cache.write().await.insert(mint, entry);
        info!("标记ATA已创建: {} -> {}", mint, ata_address);
    }

    /// 获取缓存统计信息
    pub async fn get_cache_stats(&self) -> (usize, usize) {
        let cache = self.ata_cache.read().await;
        let total = cache.len();
        let current_time = SystemTime::now();

        let valid = cache.values()
            .filter(|entry| {
                current_time.duration_since(entry.last_checked)
                    .map(|age| age < self.ata_ttl)
                    .unwrap_or(false)
            })
            .count();

        (total, valid)
    }
}
