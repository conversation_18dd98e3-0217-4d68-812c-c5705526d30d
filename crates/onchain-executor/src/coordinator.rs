//! 交易执行协调器
//!
//! 统筹整个交易执行流程，集成所有组件，支持多种交易类型

use std::sync::Arc;
use std::time::{Duration, SystemTime, Instant};
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};
use uuid::Uuid;

use crate::transaction_builder::TransactionBuilder;
use crate::transaction_executor::TransactionExecutor;
use crate::balance_service::{BalanceService, Asset};
use crate::risk_manager::RiskManager;
use crate::ata_cache::ATACache;
use crate::{ExecutorError, ExecutorResult};
use crate::types::{
    TransactionRequest, TransactionResult, TransactionRecord
};



/// 交易执行协调器配置
#[derive(Debug, Clone)]
pub struct CoordinatorConfig {
    /// 最大并发执行数
    pub max_concurrent_executions: u32,
    /// 最小结果阈值
    pub min_outcome_threshold: f64,
    /// 执行超时时间
    pub execution_timeout: Duration,
    /// 是否启用模拟执行
    pub enable_simulation: bool,
    /// 历史记录保留天数
    pub history_retention_days: u32,
}

impl Default for CoordinatorConfig {
    fn default() -> Self {
        Self {
            max_concurrent_executions: 3,
            min_outcome_threshold: 0.005, // 0.5%
            execution_timeout: Duration::from_secs(30),
            enable_simulation: true,
            history_retention_days: 7,
        }
    }
}

/// 交易执行协调器 - 系统的核心组件
pub struct ExecutionCoordinator {
    /// 交易构建器
    transaction_builder: Arc<TransactionBuilder>,
    /// 交易执行器
    transaction_executor: Arc<TransactionExecutor>,
    /// 资金管理器
    balance_service: Arc<BalanceService>,
    /// 风险管理器
    risk_manager: Arc<RiskManager>,
    /// ATA缓存管理器
    ata_cache: Arc<ATACache>,
    /// 执行历史记录
    execution_history: Arc<RwLock<Vec<TransactionRecord>>>,
    /// 配置
    config: CoordinatorConfig,
}

impl ExecutionCoordinator {
    /// 创建新的协调器实例
    pub fn new(
        transaction_builder: Arc<TransactionBuilder>,
        transaction_executor: Arc<TransactionExecutor>,
        balance_service: Arc<BalanceService>,
        risk_manager: Arc<RiskManager>,
        ata_cache: Arc<ATACache>,
        config: CoordinatorConfig,
    ) -> Self {
        Self {
            transaction_builder,
            transaction_executor,
            balance_service,
            risk_manager,
            ata_cache,
            execution_history: Arc::new(RwLock::new(Vec::new())),
            config,
        }
    }

    /// 执行交易请求 - 主要入口函数
    ///
    /// # 执行流程
    /// 1. 风险评估和预检
    /// 2. 资金充足性检查
    /// 3. 交易构建和模拟
    /// 4. 实际执行和监控
    /// 5. 结果记录和统计
    pub async fn execute_transaction_request(
        &self,
        request: TransactionRequest,
    ) -> ExecutorResult<TransactionResult> {
        let start_time = Instant::now();
        let execution_id = Uuid::new_v4();

        let transaction_description = request.get_transaction_description();

        info!(
            "🚀 开始执行交易请求 [{}]: 类型={}, 指令数量={}",
            execution_id,
            transaction_description,
            request.instructions.len(),
        );

        // 1. 风险评估
        let risk_assessment = self.risk_manager
            .assess_transaction_risk(&request)
            .await?;

        if !risk_assessment.approved {
            warn!("❌ 风险评估失败: {}", risk_assessment.reason);
            return Err(ExecutorError::RiskAssessmentFailed(risk_assessment.reason.to_string()));
        }

        // 2. 资金检查（使用简化的接口）
        let transaction_amount = request.get_total_amount();

        if transaction_amount > 0 {
            // 从交易请求中提取资产类型
            let asset = self.extract_asset_from_request(&request);

            // 使用简化的资金检查
            let sufficient = self.balance_service
                .ensure_funds(asset, transaction_amount)
                .await?;

            if !sufficient {
                let current_balance = self.balance_service.get_balance(asset).await?;
                warn!("❌ 资金不足: 需要 {} 单位，但只有 {} 单位可用",
                      transaction_amount, current_balance);
                return Err(ExecutorError::InsufficientFunds(format!(
                    "需要 {} 单位，但只有 {} 单位可用",
                    transaction_amount, current_balance
                )));
            }
        }

        // 4. 构建交易
        info!("🔨 构建交易");
        let transaction = self.transaction_builder
            .build_transaction_from_request(&request)
            .await?;

        debug!("交易构建完成，指令数量: {}", transaction.message.instructions().len());

        // 5. 执行交易
        info!("📤 提交交易到区块链");
        let execution_result = tokio::time::timeout(
            self.config.execution_timeout,
            self.transaction_executor.execute_transaction(
                transaction,
                Some(request.get_transaction_description()),
                self.config.enable_simulation,
            )
        )
        .await
        .map_err(|_| ExecutorError::ExecutionTimeout)??;

        // 6. 记录执行结果
        self.record_execution_result(
            execution_id,
            &request,
            &execution_result,
            start_time.elapsed(),
        ).await;


        // 8. 输出执行结果
        if execution_result.success {
            info!(
                "✅ 交易执行成功 [{}]: 耗时={:.2}s",
                execution_id,
                execution_result.execution_time.as_secs_f64()
            );
        } else {
            error!(
                "❌ 交易执行失败 [{}]: {}",
                execution_id,
                execution_result.error_message.as_deref().unwrap_or("未知错误")
            );
        }

        Ok(execution_result)
    }

    /// 批量执行多个套利机会
    pub async fn execute_multiple_requests(
        self: Arc<Self>,
        requests: Vec<TransactionRequest>,
    ) -> Vec<ExecutorResult<TransactionResult>> {
        // 预分配结果向量容量
        let mut results = Vec::with_capacity(requests.len());
        let semaphore = Arc::new(tokio::sync::Semaphore::new(
            self.config.max_concurrent_executions as usize
        ));

        info!("🎯 批量执行 {} 个交易请求", requests.len());

        // 预分配任务向量容量，最多5个并发
        let task_capacity = requests.len().min(5);
        let mut tasks = Vec::with_capacity(task_capacity);

        for request in requests.into_iter().take(5) { // 限制最多5个并发
            let coordinator = self.clone();
            let permit = semaphore.clone().acquire_owned().await.unwrap();

            let task = tokio::spawn(async move {
                let _permit = permit; // 确保执行完成后释放许可
                coordinator.execute_transaction_request(request).await
            });

            tasks.push(task);
        }

        // 等待所有任务完成
        for task in tasks {
            match task.await {
                Ok(result) => results.push(result),
                Err(e) => {
                    error!("任务执行错误: {}", e);
                    results.push(Err(ExecutorError::TransactionFailed(e.to_string())));
                }
            }
        }

        info!("✅ 批量执行完成，成功: {}, 失败: {}",
              results.iter().filter(|r| r.is_ok()).count(),
              results.iter().filter(|r| r.is_err()).count());

        results
    }


    /// 记录执行结果
    async fn record_execution_result(
        &self,
        execution_id: Uuid,
        request: &TransactionRequest,
        result: &TransactionResult,
        total_time: Duration,
    ) {
        let transaction_amount = request.get_total_amount();

        let record = TransactionRecord {
            id: execution_id,
            timestamp: SystemTime::now(),
            transaction_description: request.get_transaction_description(),
            input_amount: transaction_amount,
            gas_used: result.gas_used,
            success: result.success,
            error_message: result.error_message.clone(),
            execution_time: total_time,
            signature: Some(result.signature),
        };

        self.execution_history.write().await.push(record);

        // 清理过期记录
        self.cleanup_old_records().await;
    }



    /// 清理过期的执行记录
    async fn cleanup_old_records(&self) {
        let cutoff = SystemTime::now() - Duration::from_secs(
            self.config.history_retention_days as u64 * 24 * 3600
        );

        let mut history = self.execution_history.write().await;
        let original_len = history.len();
        history.retain(|record| record.timestamp > cutoff);

        let removed = original_len - history.len();
        if removed > 0 {
            debug!("🗑️ 清理了 {} 个过期的执行记录", removed);
        }
    }

    /// 获取执行历史
    pub async fn get_execution_history(&self, limit: Option<usize>) -> Vec<TransactionRecord> {
        let history = self.execution_history.read().await;
        match limit {
            Some(n) => history.iter().rev().take(n).cloned().collect(),
            None => history.iter().rev().cloned().collect(),
        }
    }


    /// 从交易请求中提取资产类型（用于资金检查）
    fn extract_asset_from_request(&self, request: &TransactionRequest) -> Asset {
        // 检查是否有Swap指令，如果有则使用第一个Swap的from_token
        for instruction in &request.instructions {
            if let crate::types::InstructionParams::Swap(swap_params) = &instruction.params {
                // 检查是否为SOL (使用system program ID表示)
                if swap_params.from_token == solana_sdk::system_program::ID {
                    return Asset::NativeSol;
                } else {
                    return Asset::SplToken(swap_params.from_token);
                }
            }
        }

        // 如果没有Swap指令，默认假设使用SOL
        // 这对于其他类型的交易（如质押）是合理的默认值
        Asset::NativeSol
    }
}
