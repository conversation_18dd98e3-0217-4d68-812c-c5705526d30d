//! 错误类型定义

use thiserror::Error;

/// 交易执行器错误类型
#[derive(Debu<PERSON>, Error)]
pub enum ExecutorError {
    #[error("RPC错误: {0}")]
    RpcError(String),

    #[error("资金不足: {0}")]
    InsufficientFunds(String),

    #[error("交易模拟失败: {0}")]
    SimulationFailed(String),

    #[error("交易执行失败: {0}")]
    TransactionFailed(String),

    #[error("计算单元超限: {0}")]
    ExcessiveComputeUnits(u64),

    #[error("风险评估失败: {0}")]
    RiskAssessmentFailed(String),

    #[error("余额检查失败: {0}")]
    BalanceCheckFailed(String),

    #[error("ATA缓存错误: {0}")]
    CacheError(String),

    #[error("不支持的DEX协议: {0:?}")]
    UnsupportedDex(String),

    #[error("不支持的池类型: {0:?}")]
    UnsupportedPoolType(String),

    #[error("序列化错误: {0}")]
    SerializationError(String),

    #[error("IO错误: {0}")]
    IoError(String),

    #[error("滑点过高")]
    SlippageTooHigh,

    #[error("执行超时")]
    ExecutionTimeout,

    #[error("解析结果失败: {0}")]
    ResultParseFailed(String),

    #[error("不支持的交易类型: {0}")]
    UnsupportedTransactionType(String),

    #[error("解析错误: {0}")]
    ParseError(String),
}

/// 执行器结果类型
pub type ExecutorResult<T> = Result<T, ExecutorError>;

impl From<serde_json::Error> for ExecutorError {
    fn from(err: serde_json::Error) -> Self {
        ExecutorError::SerializationError(err.to_string())
    }
}

impl From<std::io::Error> for ExecutorError {
    fn from(err: std::io::Error) -> Self {
        ExecutorError::IoError(err.to_string())
    }
}

impl From<solana_client::client_error::ClientError> for ExecutorError {
    fn from(err: solana_client::client_error::ClientError) -> Self {
        ExecutorError::RpcError(err.to_string())
    }
}