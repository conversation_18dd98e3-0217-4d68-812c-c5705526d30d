use solana_sdk::{
    compute_budget::ComputeBudgetInstruction,
    pubkey::Pubkey,
    signature::{Keypair, Signature, Signer},
    system_instruction,
    transaction::{Transaction},
    commitment_config::CommitmentConfig,
    instruction::Instruction,
};
use serde_json::{json, Value};
use std::time::Duration;
use solana_client::rpc_client::RpcClient;
use thiserror::Error;
use std::sync::Arc;

#[derive(Error, Debug)]
pub enum JitoError {
    #[error("Jito bundle tip has not been set")]
    NoTipSet,
    #[error("Transaction size too big: {size} bytes, max keys: {keys}")]
    TransactionTooLarge { size: usize, keys: usize },
    #[error("Failed to send transaction to Jito: {0}")]
    SendFailed(String),
    #[error("Transaction {0} was not confirmed")]
    NotConfirmed(String),
    #[error("RPC error: {0}")]
    RpcError(String),
    #[error("Serialization error: {0}")]
    SerializationError(String),
}

pub struct JitoSender {
    rpc_client: Arc<RpcClient>,
    jito_url: String,
    commitment: CommitmentConfig,
}

impl JitoSender {
    /// 从RPC URL创建新的JitoSender（创建新的RpcClient实例）
    pub fn new(rpc_url: &str, jito_url: Option<String>) -> Self {
        Self {
            rpc_client: Arc::new(RpcClient::new(rpc_url.to_string())),
            jito_url: jito_url.unwrap_or_else(|| {
                "https://mainnet.block-engine.jito.wtf/api/v1/transactions".to_string()
            }),
            commitment: CommitmentConfig::processed(),
        }
    }

    /// 从现有的RpcClient创建JitoSender
    pub fn with_rpc_client(rpc_client: Arc<RpcClient>, jito_url: Option<String>) -> Self {
        Self {
            rpc_client,
            jito_url: jito_url.unwrap_or_else(|| {
                "https://mainnet.block-engine.jito.wtf/api/v1/transactions".to_string()
            }),
            commitment: CommitmentConfig::processed(),
        }
    }

    /// 从现有的RpcClient实例创建JitoSender（会包装成Arc）
    pub fn from_rpc_client(rpc_client: RpcClient, jito_url: Option<String>) -> Self {
        Self {
            rpc_client: Arc::new(rpc_client),
            jito_url: jito_url.unwrap_or_else(|| {
                "https://mainnet.block-engine.jito.wtf/api/v1/transactions".to_string()
            }),
            commitment: CommitmentConfig::processed(),
        }
    }

    /// 设置commitment级别
    pub fn with_commitment(mut self, commitment: CommitmentConfig) -> Self {
        self.commitment = commitment;
        self
    }

    pub async fn send_tx_jito(
        &self,
        jito_tip: f64, // 以SOL为单位的小费
        transaction: Transaction,
        priority_fee: Option<f64>, // 以SOL为单位的优先费
        signer: &Keypair,
    ) -> Result<String, JitoError> {
        // 验证小费是否设置
        if jito_tip <= 0.0 {
            return Err(JitoError::NoTipSet);
        }

        let jito_tip_lamports = (jito_tip * solana_sdk::native_token::LAMPORTS_PER_SOL as f64) as u64;

        // 获取交易的指令并复制一份以便修改
        let mut instructions = transaction.message.instructions
            .iter()
            .map(|compiled_ix| {
                let program_id = transaction.message.account_keys[compiled_ix.program_id_index as usize];
                let accounts = compiled_ix.accounts
                    .iter()
                    .map(|&account_index| {
                        solana_sdk::instruction::AccountMeta {
                            pubkey: transaction.message.account_keys[account_index as usize],
                            is_signer: account_index < transaction.message.header.num_required_signatures as u8,
                            is_writable: account_index < (transaction.message.header.num_required_signatures + transaction.message.header.num_readonly_signed_accounts) as u8,
                        }
                    })
                    .collect();

                Instruction {
                    program_id,
                    accounts,
                    data: compiled_ix.data.clone(),
                }
            })
            .collect::<Vec<_>>();

        // 添加优先费指令
        if let Some(priority_fee) = priority_fee {
            let priority_fee_microlamports = (priority_fee * solana_sdk::native_token::LAMPORTS_PER_SOL as f64 * 1_000_000.0) as u64;
            tracing::info!("Priority fee micro lamports: {}", priority_fee_microlamports);

            instructions.insert(0,
                ComputeBudgetInstruction::set_compute_unit_price(priority_fee_microlamports)
            );
        }

        // 添加Jito小费指令
        let jito_tip_account = Pubkey::try_from("DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL")
            .map_err(|e| JitoError::SerializationError(e.to_string()))?;

        instructions.push(
            system_instruction::transfer(
                &signer.pubkey(),
                &jito_tip_account,
                jito_tip_lamports,
            )
        );

        // 获取最新区块哈希
        let recent_blockhash = self.rpc_client
            .get_latest_blockhash()
            .map_err(|e| JitoError::RpcError(e.to_string()))?;

        // 创建标准交易
        let mut tx = Transaction::new_with_payer(&instructions, Some(&signer.pubkey()));
        tx.sign(&[signer], recent_blockhash);

        // 检查交易大小限制 - 使用bincode序列化
        let serialized_tx = bincode::serde::encode_to_vec(&tx, bincode::config::standard())
            .map_err(|e| JitoError::SerializationError(e.to_string()))?;
        let total_size = serialized_tx.len();

        if total_size > 1232 {
            return Err(JitoError::TransactionTooLarge {
                size: total_size,
                keys: tx.message.account_keys.len(),
            });
        }

        // 编码交易用于Jito
        let encoded_tx = bs58::encode(&serialized_tx).into_string();

        // 发送到Jito
        let tx_sig = self.send_to_jito(&encoded_tx).await?;
        tracing::info!("Transaction signature: {}", tx_sig);

        // 确认交易 - 简化版本，仅检查几次
        self.confirm_transaction_simple(&tx_sig).await
    }

    async fn send_to_jito(&self, encoded_tx: &str) -> Result<String, JitoError> {
        let payload = json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "sendTransaction",
            "params": [
                encoded_tx,
                {
                    "maxRetries": 0,
                    "skipPreflight": true,
                    "preflightCommitment": "processed"
                }
            ]
        });

        let client = reqwest::Client::new();
        let response = client
            .post(&self.jito_url)
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await
            .map_err(|e| JitoError::SendFailed(e.to_string()))?;

        let response_text = response
            .text()
            .await
            .map_err(|e| JitoError::SendFailed(e.to_string()))?;

        tracing::info!("Jito response: {}", response_text);

        let response_json: Value = serde_json::from_str(&response_text)
            .map_err(|e| JitoError::SendFailed(e.to_string()))?;

        if let Some(result) = response_json.get("result") {
            if let Some(tx_sig) = result.as_str() {
                return Ok(tx_sig.to_string());
            }
        }

        if let Some(error) = response_json.get("error") {
            return Err(JitoError::SendFailed(format!("Jito error: {}", error)));
        }

        Err(JitoError::SendFailed("Unknown Jito response format".to_string()))
    }

    async fn confirm_transaction_simple(&self, tx_sig: &str) -> Result<String, JitoError> {
        let signature = tx_sig.parse::<Signature>()
            .map_err(|e| JitoError::SerializationError(e.to_string()))?;

        // 简单的确认逻辑：等待几秒钟并检查状态
        for _ in 0..10 {
            tokio::time::sleep(Duration::from_millis(500)).await;

            // 检查签名状态
            match self.rpc_client.get_signature_statuses(&[signature]) {
                Ok(statuses) => {
                    if let Some(Some(status)) = statuses.value.first() {
                        tracing::info!("Signature status: {:?}", status);

                        // 如果状态不为空（即已被处理），返回成功
                        if status.err.is_none() {
                            return Ok(tx_sig.to_string());
                        }

                        // 如果有错误，返回失败
                        if status.err.is_some() {
                            return Err(JitoError::NotConfirmed(format!("Transaction failed: {:?}", status.err)));
                        }
                    }
                }
                Err(e) => {
                    tracing::warn!("Error checking signature status: {}", e);
                }
            }
        }

        // 如果10次检查后仍未确认，返回未确认错误
        tracing::warn!("Transaction not confirmed after 10 attempts");
        Err(JitoError::NotConfirmed(tx_sig.to_string()))
    }
}

// Jito小费账户常量
pub const JITO_TIP_ACCOUNTS: &[&str] = &[
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT",
];

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_jito_tip_accounts() {
        for account in JITO_TIP_ACCOUNTS {
            assert!(Pubkey::try_from(*account).is_ok(), "Invalid Jito tip account: {}", account);
        }
    }

    #[tokio::test]
    async fn test_jito_sender_creation() {
        let sender = JitoSender::new("https://api.mainnet-beta.solana.com", None);
        assert_eq!(sender.jito_url, "https://mainnet.block-engine.jito.wtf/api/v1/transactions");
    }
}
