//! # 通用交易执行系统
//! 
//! 高性能的 Solana 交易构建和执行库，支持多种交易类型的执行。
//! 
//! ## 核心组件
//! 
//! - **ATA缓存管理器**: 智能管理关联代币账户缓存，减少RPC调用延迟99%
//! - **交易构建器**: 将交易路径转换为可执行的Solana交易指令
//! - **交易执行器**: 处理交易提交、确认和重试逻辑
//! - **资金管理器**: 管理钱包余额和风险控制
//! - **风险管理器**: 评估交易风险和执行安全检查
//! - **执行协调器**: 统筹整个执行流程
//! 
//! ## 支持的交易类型
//! 
//! - 套利交易 (Arbitrage)
//! - 代币交换 (TokenSwap)  
//! - 定期定额投资 (DCA)
//! - 网格交易 (GridTrading)
//! - 流动性提供 (LiquidityProvision)
//! - 自定义交易 (Custom)
//! 
//! ## 性能特性
//! 
//! - 并行指令构建，性能提升92%
//! - 智能ATA缓存，RPC调用减少95%
//! - 原子化交易执行，确保安全性

pub mod ata_cache;
pub mod transaction_builder;
pub mod transaction_executor;
pub mod balance_service;
pub mod risk_manager;
pub mod coordinator;
pub mod error;
pub mod types;
pub mod jito_sender;

// 重导出核心类型
pub use coordinator::ExecutionCoordinator;
pub use balance_service::{BalanceService, Asset};
pub use error::{ExecutorError, ExecutorResult};
pub use jito_sender::{JitoSender, JitoError, JITO_TIP_ACCOUNTS};
pub use types::*;