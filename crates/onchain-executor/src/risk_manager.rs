//! 交易风险管理器
//!
//! 评估和控制各种交易类型的执行风险

use std::time::{Duration, SystemTime};
use tracing::{info, warn, debug};

use crate::{ExecutorResult};
use crate::types::{TransactionRequest, TransactionRiskAssessment, InstructionParams};

/// 风险管理配置
#[derive(Debug, Clone)]
pub struct RiskConfig {
    /// 最大路径长度
    pub max_path_length: usize,
    /// 最小利润阈值
    pub min_profit_threshold: f64,
    /// 最小步骤流动性
    pub min_step_liquidity: u64,
    /// 单笔最大交易金额
    pub max_single_trade_amount: u64,
    /// 最大滑点容忍度
    pub max_slippage_bps: u64,
}

impl Default for RiskConfig {
    fn default() -> Self {
        Self {
            max_path_length: 4,
            min_profit_threshold: 0.005, // 0.5%
            min_step_liquidity: 10_000,  // 10k lamports
            max_single_trade_amount: 5_000_000_000, // 5 SOL
            max_slippage_bps: 300, // 3%
        }
    }
}

/// 风险管理器
pub struct RiskManager {
    config: RiskConfig,
}

impl RiskManager {
    pub fn new(config: RiskConfig) -> Self {
        Self { config }
    }

    /// 通用交易风险评估
    pub async fn assess_transaction_risk(
        &self,
        request: &TransactionRequest
    ) -> ExecutorResult<TransactionRiskAssessment> {
        // 根据指令类型选择相应的风险评估策略
        if request.has_swap_instructions() {
            // 如果包含多个 Swap 指令
            self.assess_token_swap_risk(request).await
        } else if request.has_liquidity_instructions() {
            self.assess_liquidity_risk(request).await
        } else if request.has_staking_instructions() {
            self.assess_staking_risk(request).await
        } else {
            self.assess_generic_risk(request).await
        }
    }


    /// 代币交换专用风险评估
    async fn assess_token_swap_risk(
        &self,
        request: &TransactionRequest
    ) -> ExecutorResult<TransactionRiskAssessment> {
        let transaction_amount = request.get_total_amount();

        let mut assessment = TransactionRiskAssessment {
            approved: true,
            risk_score: 0.1, // 代币交换基础风险较低
            reason: "".into(),
            recommendations: Vec::new(),
            type_specific_risks: std::collections::HashMap::new(),
        };

        // 基础检查：金额和滑点
        if transaction_amount > self.config.max_single_trade_amount {
            assessment.approved = false;
            assessment.reason = "交易金额超过限制".into();
        }

        assessment.type_specific_risks.insert("swap_complexity".to_string(), 0.1);
        Ok(assessment)
    }

    /// 通用风险评估（用于新的交易类型）
    async fn assess_generic_risk(
        &self,
        request: &TransactionRequest
    ) -> ExecutorResult<TransactionRiskAssessment> {
        let transaction_amount = request.get_total_amount();

        let mut assessment = TransactionRiskAssessment {
            approved: true,
            risk_score: 0.2, // 未知类型稍高风险
            reason: "".into(),
            recommendations: vec!["未知交易类型，建议谨慎执行".into()],
            type_specific_risks: std::collections::HashMap::new(),
        };

        // 基础安全检查
        if transaction_amount > self.config.max_single_trade_amount {
            assessment.approved = false;
            assessment.reason = "交易金额超过安全限制".into();
        }

        assessment.type_specific_risks.insert("unknown_type".to_string(), 0.2);
        Ok(assessment)
    }

    /// 流动性相关交易风险评估
    async fn assess_liquidity_risk(
        &self,
        request: &TransactionRequest
    ) -> ExecutorResult<TransactionRiskAssessment> {
        let transaction_amount = request.get_total_amount();

        let mut assessment = TransactionRiskAssessment {
            approved: true,
            risk_score: 0.15, // 流动性操作中等风险
            reason: "".into(),
            recommendations: Vec::new(),
            type_specific_risks: std::collections::HashMap::new(),
        };

        // 基础安全检查
        if transaction_amount > self.config.max_single_trade_amount {
            assessment.approved = false;
            assessment.reason = "流动性操作金额超过安全限制".into();
        }

        // 检查是否有移除流动性操作（风险较高）
        for instruction in &request.instructions {
            if matches!(instruction.params, InstructionParams::RemoveLiquidity(_)) {
                assessment.risk_score += 0.1;
                assessment.recommendations.push("移除流动性操作，请确保时机合适".into());
            }
        }

        assessment.type_specific_risks.insert("liquidity_complexity".to_string(), 0.15);
        Ok(assessment)
    }

    /// 质押相关交易风险评估
    async fn assess_staking_risk(
        &self,
        request: &TransactionRequest
    ) -> ExecutorResult<TransactionRiskAssessment> {
        let transaction_amount = request.get_total_amount();

        let mut assessment = TransactionRiskAssessment {
            approved: true,
            risk_score: 0.05, // 质押操作风险较低
            reason: "".into(),
            recommendations: vec!["质押操作具有锁定期，请确保资金安排合理".into()],
            type_specific_risks: std::collections::HashMap::new(),
        };

        // 基础安全检查
        if transaction_amount > self.config.max_single_trade_amount {
            assessment.approved = false;
            assessment.reason = "质押金额超过安全限制".into();
        }

        assessment.type_specific_risks.insert("staking_lockup".to_string(), 0.05);
        Ok(assessment)
    }

    /// 评估路径复杂度风险
    #[inline]
    fn assess_path_complexity(&self, path_length: usize) -> f64 {
        match path_length {
            1..=2 => 0.0,    // 简单路径，无额外风险
            3 => 0.1,        // 中等复杂度
            4 => 0.2,        // 较高复杂度
            _ => 0.4,        // 高复杂度
        }
    }

    /// 更新风险配置
    pub fn update_config(&mut self, new_config: RiskConfig) {
        self.config = new_config;
        info!("🔧 风险管理配置已更新");
    }

    /// 获取当前风险配置
    pub fn get_config(&self) -> &RiskConfig {
        &self.config
    }
}
