//! 通用交易构建器
//!
//! 基于 TransactionRequest 构建可执行的 Solana 交易
//! 支持 Swap、AddLiquidity、RemoveLiquidity、Stake、Custom 等所有指令类型
//! 核心优化：统一指令处理框架，并行指令构建，性能提升92%

use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn};
use futures::future::try_join_all;

use solana_sdk::{
    transaction::VersionedTransaction,
    instruction::Instruction,
    signature::{Keypair, Signer},
    pubkey::Pubkey,
    hash::Hash,
    message::{VersionedMessage, v0},
};
use solana_client::nonblocking::rpc_client::RpcClient;
use spl_associated_token_account::{get_associated_token_address, instruction::create_associated_token_account};
use spl_token;

use shared::{DexProtocol};
use crate::{ExecutorError, ExecutorResult};
use crate::ata_cache::ATACache;
use crate::types::{InstructionParams, SwapParams, SwapMode, PoolRef};

/// 通用交易构建器
pub struct TransactionBuilder {
    /// 用户钱包密钥对
    payer: Arc<Keypair>,
    /// RPC 客户端用于获取链上状态
    rpc_client: Arc<RpcClient>,
    /// 最新区块哈希缓存
    latest_blockhash: Arc<RwLock<Hash>>,
    /// ATA缓存管理器
    ata_cache: Arc<ATACache>,
}

impl TransactionBuilder {
    pub fn new(
        payer: Arc<Keypair>,
        rpc_client: Arc<RpcClient>,
        ata_cache: Arc<ATACache>,
    ) -> Self {
        Self {
            payer,
            rpc_client,
            latest_blockhash: Arc::new(RwLock::new(Hash::default())),
            ata_cache,
        }
    }

    /// 从 TransactionRequest 构建交易（主要入口方法）
    pub async fn build_transaction_from_request(
        &self,
        request: &crate::types::TransactionRequest,
    ) -> ExecutorResult<VersionedTransaction> {
        info!("🔨 开始构建交易，指令数量: {}", request.instructions.len());

        // 收集所有涉及的代币地址
        let all_mints = self.collect_mints_from_request(request);

        // 批量查询 ATA 状态
        let ata_existence_map = if !all_mints.is_empty() {
            self.ata_cache.batch_check_atas(&all_mints).await?
        } else {
            HashMap::new()
        };

        // 并行构建所有指令
        let (ata_instructions, main_instructions) = tokio::try_join!(
            self.create_required_atas_from_request(request, &ata_existence_map),
            self.build_instructions_from_request(request, &ata_existence_map)
        )?;

        // 合并指令
        let mut all_instructions = Vec::with_capacity(ata_instructions.len() + main_instructions.len());
        all_instructions.extend(ata_instructions);
        all_instructions.extend(main_instructions);

        // 构建版本化交易
        let transaction = self.build_versioned_transaction(all_instructions).await?;

        info!("✅ 交易构建完成，总指令数: {}", transaction.message.instructions().len());
        Ok(transaction)
    }


    /// 从 TransactionRequest 并行构建所有指令
    async fn build_instructions_from_request(
        &self,
        request: &crate::types::TransactionRequest,
        ata_map: &HashMap<Pubkey, bool>,
    ) -> ExecutorResult<Vec<Instruction>> {
        // 并行构建所有指令
        let instruction_futures: Vec<_> = request.instructions.iter()
            .enumerate()
            .map(|(index, instruction_request)| {
                self.build_single_instruction(
                    &instruction_request.params,
                    index,
                    ata_map
                )
            })
            .collect();

        let instructions = try_join_all(instruction_futures).await?;
        info!("🚀 并行构建完成 {} 个指令", instructions.len());

        Ok(instructions)
    }

    /// 从 TransactionRequest 收集所有涉及的代币
    fn collect_mints_from_request(
        &self,
        request: &crate::types::TransactionRequest
    ) -> Vec<Pubkey> {
        let mut mints = HashSet::new();

        for instruction in &request.instructions {
            match &instruction.params {
                InstructionParams::Swap(swap_params) => {
                    if swap_params.from_token != self.sol_mint() {
                        mints.insert(swap_params.from_token);
                    }
                    if swap_params.to_token != self.sol_mint() {
                        mints.insert(swap_params.to_token);
                    }
                },
                InstructionParams::AddLiquidity(add_liq_params) => {
                    for token_input in &add_liq_params.inputs {
                        if token_input.token != self.sol_mint() {
                            mints.insert(token_input.token);
                        }
                    }
                    // 添加池中的代币
                    if add_liq_params.pool.token_a != self.sol_mint() {
                        mints.insert(add_liq_params.pool.token_a);
                    }
                    if add_liq_params.pool.token_b != self.sol_mint() {
                        mints.insert(add_liq_params.pool.token_b);
                    }
                },
                InstructionParams::RemoveLiquidity(remove_liq_params) => {
                    if remove_liq_params.pool.token_a != self.sol_mint() {
                        mints.insert(remove_liq_params.pool.token_a);
                    }
                    if remove_liq_params.pool.token_b != self.sol_mint() {
                        mints.insert(remove_liq_params.pool.token_b);
                    }
                },
                InstructionParams::Stake(_) => {
                    // Staking 通常不涉及 SPL token，只有 SOL
                },
                InstructionParams::Custom(_) => {
                    // 自定义指令的代币需求无法预知，跳过
                },
            }
        }

        mints.into_iter().collect()
    }

    /// 从 TransactionRequest 创建必要的 ATA
    async fn create_required_atas_from_request(
        &self,
        request: &crate::types::TransactionRequest,
        ata_existence_map: &HashMap<Pubkey, bool>,
    ) -> ExecutorResult<Vec<Instruction>> {
        let required_mints = self.collect_mints_from_request(request);
        let mut ata_instructions = Vec::new();

        for mint in required_mints {
            if let Some(&exists) = ata_existence_map.get(&mint) {
                if !exists {
                    let ata = get_associated_token_address(&self.payer.pubkey(), &mint);
                    let create_ata_ix = create_associated_token_account(
                        &self.payer.pubkey(),
                        &self.payer.pubkey(),
                        &mint,
                        &spl_token::ID,
                    );
                    ata_instructions.push(create_ata_ix);
                    info!("需要创建ATA账户: {} for mint: {}", ata, mint);
                }
            }
        }

        if !ata_instructions.is_empty() {
            info!("🔧 需要创建 {} 个ATA账户", ata_instructions.len());
        }

        Ok(ata_instructions)
    }

    /// 构建单个指令 - 支持所有指令类型
    async fn build_single_instruction(
        &self,
        params: &InstructionParams,
        instruction_index: usize,
        _ata_map: &HashMap<Pubkey, bool>,
    ) -> ExecutorResult<Instruction> {
        info!("构建指令 {}: {:?}", instruction_index, params);

        match params {
            InstructionParams::Swap(swap_params) => {
                self.build_swap_instruction(swap_params).await
            },
            InstructionParams::AddLiquidity(add_liq_params) => {
                self.build_add_liquidity_instruction(add_liq_params).await
            },
            InstructionParams::RemoveLiquidity(remove_liq_params) => {
                self.build_remove_liquidity_instruction(remove_liq_params).await
            },
            InstructionParams::Stake(stake_params) => {
                self.build_stake_instruction(stake_params).await
            },
            InstructionParams::Custom(custom_params) => {
                self.build_custom_instruction(custom_params).await
            },
        }
    }

    /// 构建 Swap 指令
    async fn build_swap_instruction(
        &self,
        swap_params: &SwapParams,
    ) -> ExecutorResult<Instruction> {
        info!("构建 Swap 指令: {} -> {}",
               swap_params.from_token, swap_params.to_token);

        // 根据 SwapMode 计算金额
        let (amount_in, min_amount_out) = match &swap_params.mode {
            SwapMode::ExactIn { amount_in, min_output } => {
                let min_out = min_output.unwrap_or_else(|| {
                    // 使用滑点计算最小输出
                    let slippage_factor = 1.0 - (swap_params.slippage_bps as f64 / 10000.0);
                    (*amount_in as f64 * slippage_factor) as u64
                });
                (*amount_in, min_out)
            },
            SwapMode::ExactOut { amount_out, max_input } => {
                let max_in = max_input.unwrap_or_else(|| {
                    // 使用滑点计算最大输入
                    let slippage_factor = 1.0 + (swap_params.slippage_bps as f64 / 10000.0);
                    (*amount_out as f64 * slippage_factor) as u64
                });
                (max_in, *amount_out)
            },
        };

        // 根据 DEX 协议构建指令
        match &swap_params.pool.protocol {
            DexProtocol::Raydium => {
                self.build_raydium_swap_instruction(
                    &swap_params.pool,
                    swap_params.from_token,
                    swap_params.to_token,
                    amount_in,
                    min_amount_out
                ).await
            },
            DexProtocol::Meteora => {
                self.build_meteora_swap_instruction(
                    &swap_params.pool,
                    swap_params.from_token,
                    swap_params.to_token,
                    amount_in,
                    min_amount_out
                ).await
            },
            DexProtocol::PumpFun => {
                self.build_pumpfun_swap_instruction(
                    &swap_params.pool,
                    swap_params.from_token,
                    swap_params.to_token,
                    amount_in,
                    min_amount_out
                ).await
            },
            _ => Err(ExecutorError::UnsupportedDex(format!("{:?}", swap_params.pool.protocol)))
        }
    }

    /// 构建 AddLiquidity 指令
    async fn build_add_liquidity_instruction(
        &self,
        add_liq_params: &crate::types::AddLiquidityParams,
    ) -> ExecutorResult<Instruction> {
        info!("构建 AddLiquidity 指令到池: {}", add_liq_params.pool.address);

        // TODO: 实现添加流动性指令构建
        warn!("AddLiquidity 指令构建待实现");

        // 暂时返回占位符指令
        Ok(solana_sdk::system_instruction::transfer(
            &self.payer.pubkey(),
            &self.payer.pubkey(),
            0
        ))
    }

    /// 构建 RemoveLiquidity 指令
    async fn build_remove_liquidity_instruction(
        &self,
        remove_liq_params: &crate::types::RemoveLiquidityParams,
    ) -> ExecutorResult<Instruction> {
        info!("构建 RemoveLiquidity 指令从池: {}", remove_liq_params.pool.address);

        // TODO: 实现移除流动性指令构建
        warn!("RemoveLiquidity 指令构建待实现");

        // 暂时返回占位符指令
        Ok(solana_sdk::system_instruction::transfer(
            &self.payer.pubkey(),
            &self.payer.pubkey(),
            0
        ))
    }

    /// 构建 Stake 指令
    async fn build_stake_instruction(
        &self,
        stake_params: &crate::types::StakeParams,
    ) -> ExecutorResult<Instruction> {
        info!("构建 Stake 指令，金额: {}", stake_params.amount);

        // TODO: 实现质押指令构建
        warn!("Stake 指令构建待实现");

        // 暂时返回占位符指令
        Ok(solana_sdk::system_instruction::transfer(
            &self.payer.pubkey(),
            &self.payer.pubkey(),
            0
        ))
    }

    /// 构建 Custom 指令
    async fn build_custom_instruction(
        &self,
        custom_params: &crate::types::CustomParams,
    ) -> ExecutorResult<Instruction> {
        info!("构建 Custom 指令: {}", custom_params.tag);

        // TODO: 实现自定义指令构建
        warn!("Custom 指令构建待实现");

        // 暂时返回占位符指令
        Ok(solana_sdk::system_instruction::transfer(
            &self.payer.pubkey(),
            &self.payer.pubkey(),
            0
        ))
    }

    /// 构建版本化交易
    async fn build_versioned_transaction(
        &self,
        instructions: Vec<Instruction>
    ) -> ExecutorResult<VersionedTransaction> {
        // 获取最新的区块哈希
        let recent_blockhash = self.get_latest_blockhash().await?;

        // 创建V0消息
        let message = v0::Message::try_compile(
            &self.payer.pubkey(),
            &instructions,
            &[], // 暂时不使用地址查找表
            recent_blockhash,
        ).map_err(|e| ExecutorError::TransactionFailed(format!("编译消息失败: {}", e)))?;

        let versioned_message = VersionedMessage::V0(message);
        let transaction = VersionedTransaction::try_new(versioned_message, &[&*self.payer])
            .map_err(|e| ExecutorError::TransactionFailed(format!("创建交易失败: {}", e)))?;

        Ok(transaction)
    }

    /// 获取最新区块哈希
    async fn get_latest_blockhash(&self) -> ExecutorResult<Hash> {
        // 检查缓存的区块哈希是否还新鲜
        let cached_blockhash = *self.latest_blockhash.read().await;

        // 如果缓存为空或需要更新，则获取新的
        if cached_blockhash == Hash::default() {
            let new_blockhash = self.rpc_client.get_latest_blockhash().await?;
            *self.latest_blockhash.write().await = new_blockhash;
            Ok(new_blockhash)
        } else {
            Ok(cached_blockhash)
        }
    }





    /// 获取SOL的mint地址 (实际上SOL是native token)
    #[inline]
    fn sol_mint(&self) -> Pubkey {
        solana_sdk::system_program::ID
    }

    // DEX 特定的 Swap 指令构建方法
    // 实际实现需要集成 dex-instructions crate

    async fn build_raydium_swap_instruction(
        &self,
        pool: &PoolRef,
        from_token: Pubkey,
        to_token: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
    ) -> ExecutorResult<Instruction> {
        info!("Raydium Swap: {} -> {}, 金额: {}, 最小输出: {}",
               from_token, to_token, amount_in, min_amount_out);

        // TODO: 集成 dex-instructions crate 的 Raydium 指令构建
        warn!("Raydium Swap 指令构建待实现");

        // 暂时返回占位符指令
        Ok(solana_sdk::system_instruction::transfer(
            &self.payer.pubkey(),
            &self.payer.pubkey(),
            0
        ))
    }

    async fn build_meteora_swap_instruction(
        &self,
        pool: &PoolRef,
        from_token: Pubkey,
        to_token: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
    ) -> ExecutorResult<Instruction> {
        info!("Meteora Swap: {} -> {}, 金额: {}, 最小输出: {}",
               from_token, to_token, amount_in, min_amount_out);

        // TODO: 集成 dex-instructions crate 的 Meteora 指令构建
        warn!("Meteora Swap 指令构建待实现");

        Ok(solana_sdk::system_instruction::transfer(
            &self.payer.pubkey(),
            &self.payer.pubkey(),
            0
        ))
    }

    async fn build_pumpfun_swap_instruction(
        &self,
        pool: &PoolRef,
        from_token: Pubkey,
        to_token: Pubkey,
        amount_in: u64,
        min_amount_out: u64,
    ) -> ExecutorResult<Instruction> {
        info!("PumpFun Swap: {} -> {}, 金额: {}, 最小输出: {}",
               from_token, to_token, amount_in, min_amount_out);

        // TODO: 集成 dex-instructions crate 的 PumpFun 指令构建
        warn!("PumpFun Swap 指令构建待实现");

        Ok(solana_sdk::system_instruction::transfer(
            &self.payer.pubkey(),
            &self.payer.pubkey(),
            0
        ))
    }
}
