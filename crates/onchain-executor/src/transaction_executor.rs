//! 交易执行器
//!
//! 处理交易提交、确认和失败恢复

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{info, warn, debug};

use solana_client::{
    nonblocking::rpc_client::RpcClient,
    rpc_config::{RpcSendTransactionConfig, RpcSimulateTransactionConfig, RpcTransactionConfig},
};
use solana_sdk::{
    transaction::VersionedTransaction,
    signature::Signature,
    commitment_config::CommitmentConfig,
};
use solana_transaction_status::UiTransactionEncoding;

use crate::{ExecutionPerformanceStats, ExecutorError, ExecutorResult, SimulationResult, TransactionResult};

/// 交易执行器配置
#[derive(Debug, Clone)]
pub struct ExecutorConfig {
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试间隔(毫秒)
    pub retry_delay_ms: u64,
    /// 交易确认级别
    pub commitment: CommitmentConfig,
    /// 是否启用预检
    pub enable_preflight: bool,
    /// 最大计算单元限制
    pub max_compute_units: u32,
}

impl Default for ExecutorConfig {
    fn default() -> Self {
        Self {
            max_retries: 3,
            retry_delay_ms: 1000,
            commitment: CommitmentConfig::confirmed(),
            enable_preflight: true,
            max_compute_units: 1_400_000,
        }
    }
}

/// 交易执行器
pub struct TransactionExecutor {
    rpc_client: Arc<RpcClient>,
    config: ExecutorConfig,
    /// 执行统计
    stats: Arc<RwLock<ExecutionPerformanceStats>>,
}

impl TransactionExecutor {
    pub fn new(rpc_client: Arc<RpcClient>, config: ExecutorConfig) -> Self {
        Self {
            rpc_client,
            config,
            stats: Arc::new(RwLock::new(ExecutionPerformanceStats::default())),
        }
    }

    /// 纯粹的交易执行方法 - 统一的执行流程
    ///
    /// # 参数
    /// - `transaction`: 已构建好的交易
    /// - `description`: 可选的交易描述（用于日志）
    /// - `should_simulate`: 是否在执行前模拟
    ///
    /// # 执行步骤
    /// 1. 可选的预执行模拟验证
    /// 2. 发送交易到链上
    /// 3. 等待交易确认
    /// 4. 解析执行结果
    pub async fn execute_transaction(
        &self,
        transaction: VersionedTransaction,
        description: Option<String>,
        should_simulate: bool,
    ) -> ExecutorResult<TransactionResult> {
        let start_time = Instant::now();
        let desc = description.as_deref().unwrap_or("Transaction");

        info!("🚀 开始执行交易: {}", desc);

        // 1. 可选的预执行模拟
        if should_simulate {
            self.simulate_transaction(&transaction).await?;
            debug!("交易模拟通过，准备提交到链上");
        }

        // 2. 发送交易并获取签名
        let signature = self.send_and_confirm_transaction(transaction).await?;

        // 3. 解析交易结果
        let result = self.parse_transaction_result(&signature, start_time).await?;

        info!("✅ 交易执行完成: {}, 签名: {}, 成功: {}", desc, signature, result.success);
        
        // 4. 更新统计信息
        self.update_stats(&result).await;

        Ok(result)
    }
    


    /// 模拟交易执行
    async fn simulate_transaction(
        &self,
        transaction: &VersionedTransaction,
    ) -> ExecutorResult<SimulationResult> {
        debug!("🔍 开始交易模拟");

        let config = RpcSimulateTransactionConfig {
            sig_verify: false,
            replace_recent_blockhash: true,
            commitment: Some(self.config.commitment),
            encoding: None,
            accounts: None,
            min_context_slot: None,
            inner_instructions: false,
        };

        let simulation = self.rpc_client
            .simulate_transaction_with_config(transaction, config)
            .await?;

        // 检查模拟结果
        if let Some(err) = simulation.value.err {
            return Err(ExecutorError::SimulationFailed(
                format!("模拟执行失败: {:?}", err)
            ));
        }

        // 检查计算单元消耗
        let units_consumed = simulation.value.units_consumed.unwrap_or(0);
        if units_consumed > self.config.max_compute_units as u64 {
            return Err(ExecutorError::ExcessiveComputeUnits(units_consumed));
        }

        info!("✅ 交易模拟成功，消耗 {} 计算单元", units_consumed);

        Ok(SimulationResult {
            success: true,
            units_consumed,
            logs: simulation.value.logs.unwrap_or_default()
                .into_iter()
                .map(|s| s.into())
                .collect(),
        })
    }

    /// 发送并确认交易
    async fn send_and_confirm_transaction(
        &self,
        transaction: VersionedTransaction,
    ) -> ExecutorResult<Signature> {
        let config = RpcSendTransactionConfig {
            skip_preflight: !self.config.enable_preflight,
            preflight_commitment: Some(self.config.commitment.commitment),
            encoding: None,
            max_retries: Some(0), // 我们自己处理重试
            min_context_slot: None,
        };

        for attempt in 1..=self.config.max_retries {
            debug!("尝试发送交易，第 {} 次", attempt);

            // 发送交易
            match self.rpc_client.send_transaction_with_config(&transaction, config).await {
                Ok(signature) => {
                    info!("✅ 交易发送成功，签名: {}", signature);

                    // 等待确认
                    match self.wait_for_confirmation(&signature).await {
                        Ok(_) => {
                            info!("✅ 套利交易确认成功");
                            return Ok(signature);
                        }
                        Err(e) => {
                            warn!("❌ 交易确认失败: {}", e);
                            if attempt < self.config.max_retries {
                                tokio::time::sleep(Duration::from_millis(self.config.retry_delay_ms)).await;
                                continue;
                            } else {
                                return Err(e);
                            }
                        }
                    }
                }
                Err(e) => {
                    warn!("❌ 交易发送失败 (尝试 {}/{}): {}", attempt, self.config.max_retries, e);

                    // 分析错误类型，决定是否重试
                    if attempt < self.config.max_retries && self.should_retry(&e) {
                        tokio::time::sleep(Duration::from_millis(self.config.retry_delay_ms)).await;
                        continue;
                    } else {
                        return Err(ExecutorError::TransactionFailed(e.to_string()));
                    }
                }
            }
        }

        unreachable!()
    }

    /// 等待交易确认
    async fn wait_for_confirmation(&self, signature: &Signature) -> ExecutorResult<()> {
        let timeout = Duration::from_secs(30); // 30秒超时
        let start = Instant::now();

        while start.elapsed() < timeout {
            match self.rpc_client
                .get_signature_status_with_commitment(signature, self.config.commitment)
                .await?
            {
                Some(Ok(_)) => {
                    return Ok(());
                }
                Some(Err(e)) => {
                    return Err(ExecutorError::TransactionFailed(format!("交易执行失败: {:?}", e)));
                }
                None => {
                    // 交易还未确认，继续等待
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }
        }

        Err(ExecutorError::ExecutionTimeout)
    }

    /// 判断错误是否应该重试
    fn should_retry(&self, error: &solana_client::client_error::ClientError) -> bool {
        use solana_client::client_error::ClientErrorKind;

        match error.kind() {
            // 网络相关错误，可以重试
            ClientErrorKind::Io(_) => true,
            ClientErrorKind::Reqwest(_) => true,

            // RPC相关错误，可以重试
            ClientErrorKind::RpcError(_) => true,

            // 交易相关错误，通常不应重试
            ClientErrorKind::TransactionError(_) => false,

            // 其他错误，不重试
            _ => false,
        }
    }

    /// 统一的交易结果解析
    async fn parse_transaction_result(
        &self,
        signature: &Signature,
        start_time: Instant,
    ) -> ExecutorResult<TransactionResult> {
        debug!("🔍 解析交易结果: {}", signature);

        let config = RpcTransactionConfig {
            encoding: Some(UiTransactionEncoding::Json),
            commitment: Some(self.config.commitment),
            max_supported_transaction_version: Some(0),
        };

        match self.rpc_client.get_transaction_with_config(signature, config).await {
            Ok(confirmed_tx) => {
                let meta = confirmed_tx.transaction.meta.as_ref();
                
                // 优先使用 compute_units_consumed，回退到 fee  
                let gas_used = meta
                    .and_then(|m| m.compute_units_consumed.clone().into())
                    .or_else(|| meta.map(|m| m.fee))
                    .unwrap_or(5000);

                Ok(TransactionResult {
                    signature: *signature,
                    slot: confirmed_tx.slot,
                    block_time: confirmed_tx.block_time,
                    gas_used,
                    execution_time: start_time.elapsed(),
                    success: meta.map_or(false, |m| m.err.is_none()),
                    error_message: meta
                        .and_then(|m| m.err.as_ref())
                        .map(|e| format!("{:?}", e).into()),
                })
            },
            Err(e) => {
                warn!("无法获取交易详情: {}", e);
                
                Ok(TransactionResult {
                    signature: *signature,
                    slot: 0,
                    block_time: None,
                    gas_used: 5000,
                    execution_time: start_time.elapsed(),
                    success: false,
                    error_message: Some(format!("无法获取交易详情: {}", e).into()),
                })
            }
        }
    }


    /// 更新执行统计信息
    async fn update_stats(&self, result: &TransactionResult) {
        let mut stats = self.stats.write().await;
        
        stats.total_executions += 1;
        stats.total_gas_used += result.gas_used;
        
        if result.success {
            stats.successful_executions += 1;
        } else {
            stats.failed_executions += 1;
        }
        
        // 重新计算成功率和平均值
        stats.success_rate = if stats.total_executions > 0 {
            stats.successful_executions as f64 / stats.total_executions as f64
        } else {
            0.0
        };
        
        stats.average_gas_used = if stats.total_executions > 0 {
            stats.total_gas_used / stats.total_executions
        } else {
            0
        };
        
        stats.last_updated = std::time::SystemTime::now();
    }
    
    /// 获取执行统计
    pub async fn get_stats(&self) -> ExecutionPerformanceStats {
        self.stats.read().await.clone()
    }



}
