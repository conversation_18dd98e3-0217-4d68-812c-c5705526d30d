//! 交易执行系统核心数据类型定义
//!
//! 定义了通用的交易执行请求、结果和相关数据结构，
//! 支持各种类型的交易执行（套利、DCA、网格交易等）

use std::sync::Arc;
use std::time::{Duration, SystemTime};
use std::collections::HashMap;
use serde::{Serialize, Deserialize};
use solana_sdk::{signature::Signature, pubkey::Pubkey};
use shared::{DexProtocol, PoolType};


/// 通用交易执行请求
#[derive(Debug, Clone)]
pub struct TransactionRequest {
    /// 结构版本（便于后续演进/反序列化分支）
    pub schema_version: u16,
    /// 指令序列（顺序执行，可引用前面步骤输出）
    pub instructions: Vec<InstructionRequest>,
    /// 创建时间
    pub created_at: SystemTime,
    /// 全局执行优先级
    pub priority: ExecutionPriority,
    /// 元数据（策略ID、来源标签等）
    pub metadata: HashMap<String, String>,
}

/// 单条指令请求（变体化参数 + 可选局部上下文）
#[derive(Debug, Clone)]
pub struct InstructionRequest {
    pub params: InstructionParams,
}

/// 指令参数枚举
#[derive(Debug, Clone)]
pub enum InstructionParams {
    Swap(SwapParams),
    AddLiquidity(AddLiquidityParams),
    RemoveLiquidity(RemoveLiquidityParams),
    Stake(StakeParams),
    Custom(CustomParams),
}

/// Swap 模式：精确输入 或 精确输出
#[derive(Debug, Clone)]
pub enum SwapMode {
    /// 精确输入：给定输入数量，可选最小期望输出（None 时由滑点与报价推导）
    ExactIn {
        amount_in: u64,
        min_output: Option<u64>,
    },
    /// 精确输出：给定想得到的输出数量，可选最大可接受输入（None 时由滑点与报价推导）
    ExactOut {
        amount_out: u64,
        max_input: Option<u64>,
    },
}

/// Swap 参数
#[derive(Debug, Clone)]
pub struct SwapParams {
    pub from_token: Pubkey,
    pub to_token: Pubkey,
    /// 交换模式（精确输入 / 精确输出）
    pub mode: SwapMode,
    /// 滑点（bps），用于当 min_output / max_input 未显式给出时推导保护阈值
    pub slippage_bps: u16,
    pub pool: PoolRef,
}

/// 添加流动性（支持多 token）
#[derive(Debug, Clone)]
pub struct AddLiquidityParams {
    pub pool: PoolRef,
    pub inputs: Vec<TokenInput>,        // token + 数量来源
    pub min_lp: Option<u64>,
}

/// 移除流动性（支持多 token 最小输出约束）
#[derive(Debug, Clone)]
pub struct RemoveLiquidityParams {
    pub pool: PoolRef,
    pub lp_amount: u64,
}

/// 质押 / Stake（示例，可扩展）
#[derive(Debug, Clone)]
pub struct StakeParams {
    pub stake_program: Pubkey,
    pub validator_vote: Pubkey,
    pub amount: u64,
}

/// 自定义扩展指令
#[derive(Debug, Clone)]
pub struct CustomParams {
    pub tag: String,
    pub version: u16,
    pub data: Arc<[u8]>,
}

/// 流动池引用（最小必要信息）
#[derive(Debug, Clone)]
pub struct PoolRef {
    pub address: Pubkey,
    pub token_a: Pubkey,
    pub token_b: Pubkey,
    pub protocol: DexProtocol,
    pub pool_type: PoolType,
}

/// 多 token 输入
#[derive(Debug, Clone)]
pub struct TokenInput {
    pub token: Pubkey,
    pub amount: u64,
}

/// 交易执行优先级
#[derive(Debug, Clone, PartialEq, PartialOrd)]
pub enum ExecutionPriority {
    Low,
    Medium,
    High,
    Critical,
}


/// 通用交易执行结果
#[derive(Debug, Clone)]
pub struct TransactionResult {
    /// 交易签名
    pub signature: Signature,
    /// 确认的区块槽位
    pub slot: u64,
    /// 区块时间
    pub block_time: Option<i64>,
    /// 消耗的gas费用
    pub gas_used: u64,
    /// 执行耗时
    pub execution_time: Duration,
    /// 执行是否成功
    pub success: bool,
    /// 错误信息（如果失败）
    pub error_message: Option<Arc<str>>,
}



/// 交易执行记录
#[derive(Debug, Clone)]
pub struct TransactionRecord {
    /// 唯一执行ID
    pub id: uuid::Uuid,
    /// 执行时间戳
    pub timestamp: SystemTime,
    /// 交易描述（替代复杂的路径结构）
    pub transaction_description: String,
    /// 输入金额
    pub input_amount: u64,
    /// 消耗gas
    pub gas_used: u64,
    /// 是否成功
    pub success: bool,
    /// 错误信息
    pub error_message: Option<Arc<str>>,
    /// 执行时间
    pub execution_time: Duration,
    /// 交易签名
    pub signature: Option<Signature>,
}

/// ATA缓存条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ATACacheEntry {
    pub ata_address: Pubkey,
    pub exists: bool,
    pub last_checked: SystemTime,
    pub created_by_us: bool,
}

/// 简化的余额状态（移除策略相关字段）
#[derive(Debug, Clone)]
pub struct BalanceStatus {
    /// 当前余额
    pub balance: u64,
    /// 是否足够
    pub sufficient: bool,
}

/// 交易风险评估结果
#[derive(Debug, Clone)]
pub struct TransactionRiskAssessment {
    /// 是否通过风险评估
    pub approved: bool,
    /// 风险评分 (0.0-1.0，越高越危险)
    pub risk_score: f64,
    /// 评估原因或拒绝理由
    pub reason: Arc<str>,
    /// 风险建议
    pub recommendations: Vec<Arc<str>>,
    /// 交易类型相关的风险因子
    pub type_specific_risks: HashMap<String, f64>,
}

/// 模拟结果
#[derive(Debug)]
pub struct SimulationResult {
    pub success: bool,
    pub units_consumed: u64,
    pub logs: Vec<Arc<str>>,
}

/// 交易执行系统性能统计
#[derive(Debug, Clone)]
pub struct ExecutionPerformanceStats {
    /// 总执行次数
    pub total_executions: u64,
    /// 成功执行次数
    pub successful_executions: u64,
    /// 失败执行次数
    pub failed_executions: u64,
    /// 成功率
    pub success_rate: f64,
    /// 总收益/输出值（根据交易类型而定）
    pub total_outcome_value: i64,
    /// 平均收益/输出值
    pub average_outcome_value: i64,
    /// 总gas消耗
    pub total_gas_used: u64,
    /// 平均gas消耗
    pub average_gas_used: u64,
    /// 最后更新时间
    pub last_updated: SystemTime,
}


/// 旧版兼容类型别名
pub type ExecutionRecord = TransactionRecord;

impl Default for ExecutionPerformanceStats {
    fn default() -> Self {
        Self {
            total_executions: 0,
            successful_executions: 0,
            failed_executions: 0,
            success_rate: 0.0,
            total_outcome_value: 0,
            average_outcome_value: 0,
            total_gas_used: 0,
            average_gas_used: 0,
            last_updated: SystemTime::now(),
        }
    }
}

impl TransactionRequest {
    // new
    pub fn new(instructions: Vec<InstructionRequest>, priority: ExecutionPriority, metadata: HashMap<String, String>) -> Self {
        Self {
            schema_version: 1,
            instructions,
            created_at: SystemTime::now(),
            priority,
            metadata,
        }
    }


    /// 判断交易是否包含 Swap 指令
    pub fn has_swap_instructions(&self) -> bool {
        self.instructions.iter().any(|instruction| {
            matches!(instruction.params, InstructionParams::Swap(_))
        })
    }

    /// 判断交易是否包含流动性相关指令
    pub fn has_liquidity_instructions(&self) -> bool {
        self.instructions.iter().any(|instruction| {
            matches!(instruction.params,
                InstructionParams::AddLiquidity(_) |
                InstructionParams::RemoveLiquidity(_)
            )
        })
    }

    /// 判断交易是否包含质押指令
    pub fn has_staking_instructions(&self) -> bool {
        self.instructions.iter().any(|instruction| {
            matches!(instruction.params, InstructionParams::Stake(_))
        })
    }

    /// 获取主要交易类型描述（用于日志）
    pub fn get_transaction_description(&self) -> String {
        let mut descriptions = Vec::new();

        for instruction in &self.instructions {
            match &instruction.params {
                InstructionParams::Swap(_) => {
                    if !descriptions.contains(&"Swap") {
                        descriptions.push("Swap");
                    }
                },
                InstructionParams::AddLiquidity(_) => descriptions.push("AddLiquidity"),
                InstructionParams::RemoveLiquidity(_) => descriptions.push("RemoveLiquidity"),
                InstructionParams::Stake(_) => descriptions.push("Stake"),
                InstructionParams::Custom(custom) => descriptions.push(&custom.tag),
            }
        }

        if descriptions.is_empty() {
            "Unknown".to_string()
        } else {
            descriptions.join("+")
        }
    }

    /// 从指令中提取总的交易金额
    pub fn get_total_amount(&self) -> u64 {
        // 尝试从 metadata 中获取金额
        self.metadata.get("amount")
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or_else(|| {
                // 如果 metadata 中没有，尝试从第一个 Swap 指令中提取
                for instruction in &self.instructions {
                    match &instruction.params {
                        InstructionParams::Swap(swap_params) => {
                            match &swap_params.mode {
                                SwapMode::ExactIn { amount_in, .. } => return *amount_in,
                                SwapMode::ExactOut { amount_out, .. } => return *amount_out,
                            }
                        },
                        InstructionParams::AddLiquidity(add_liq_params) => {
                            // 返回第一个输入代币的数量
                            if let Some(first_input) = add_liq_params.inputs.first() {
                                return first_input.amount;
                            }
                        },
                        InstructionParams::RemoveLiquidity(remove_liq_params) => {
                            return remove_liq_params.lp_amount;
                        },
                        InstructionParams::Stake(stake_params) => {
                            return stake_params.amount;
                        },
                        InstructionParams::Custom(_) => {
                            // 对于自定义指令，无法确定金额
                        }
                    }
                }
                0 // 如果都找不到，返回默认值
            })
    }

}

/// ATA缓存性能指标
#[derive(Debug, Clone)]
pub struct ATACacheMetrics {
    pub cache_hit_rate: f64,
    pub avg_query_time_ms: f64,
    pub rpc_calls_saved: u64,
    pub preheated_tokens: u64,
}

