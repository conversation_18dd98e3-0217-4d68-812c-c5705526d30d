[package]
name = "shared"
version.workspace = true
edition.workspace = true

[dependencies]
anchor-lang.workspace = true
solana-sdk.workspace = true
solana-transaction-status.workspace = true
thiserror.workspace = true
yellowstone-grpc-proto.workspace = true
serde = { workspace = true, features = ["derive"] }

# 密钥保险库相关依赖
argon2.workspace = true
aes-gcm.workspace = true
zeroize = { workspace = true, features = ["derive"] }
rand.workspace = true


[dev-dependencies]
tempfile = "3.12"
