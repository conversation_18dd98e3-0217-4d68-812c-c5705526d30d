//! Meteora DAMM V2 Anchor 类型定义
//!
//! 基于 meteora_damm_v2.json IDL 文件生成的类型定义

use anchor_lang::prelude::*;

/// Meteora DAMM V2 程序ID
pub const METEORA_DAMM_V2_PROGRAM_ID: Pubkey =
    pubkey!("cpamdpZCGKUy5JxQXB4dcpGPiikHawvSWAd6mEn1sGG");

/// 基础费用结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct BaseFeeStruct {
    /// 悬崖费用分子
    pub cliff_fee_numerator: u64,
    /// 费用调度模式
    pub fee_scheduler_mode: u8,
    /// 填充 0
    pub padding_0: [u8; 5],
    /// 周期数量
    pub number_of_period: u16,
    /// 周期频率
    pub period_frequency: u64,
    /// 减少因子
    pub reduction_factor: u64,
    /// 填充 1
    pub padding_1: u64,
}

/// 动态费用结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct DynamicFeeStruct {
    /// 是否已初始化
    pub initialized: u8,
    /// 填充
    pub padding: [u8; 7],
    /// 最大波动率累积器
    pub max_volatility_accumulator: u32,
    /// 变量费用控制
    pub variable_fee_control: u32,
    /// bin step
    pub bin_step: u16,
    /// 过滤周期
    pub filter_period: u16,
    /// 衰减周期
    pub decay_period: u16,
    /// 减少因子
    pub reduction_factor: u16,
    /// 最后更新时间戳
    pub last_update_timestamp: u64,
    /// bin step u128
    pub bin_step_u128: u128,
    /// sqrt 价格参考
    pub sqrt_price_reference: u128,
    /// 波动率累积器
    pub volatility_accumulator: u128,
    /// 波动率参考
    pub volatility_reference: u128,
}

/// 池费用结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PoolFeesStruct {
    /// 基础费用
    pub base_fee: BaseFeeStruct,
    /// 协议费用百分比
    pub protocol_fee_percent: u8,
    /// 伙伴费用百分比
    pub partner_fee_percent: u8,
    /// 推荐费用百分比
    pub referral_fee_percent: u8,
    /// 填充 0
    pub padding_0: [u8; 5],
    /// 动态费用
    pub dynamic_fee: DynamicFeeStruct,
    /// 填充 1
    pub padding_1: [u64; 2],
}

/// 池指标
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PoolMetrics {
    /// 总 LP A 费用
    pub total_lp_a_fee: u128,
    /// 总 LP B 费用
    pub total_lp_b_fee: u128,
    /// 总协议 A 费用
    pub total_protocol_a_fee: u64,
    /// 总协议 B 费用
    pub total_protocol_b_fee: u64,
    /// 总伙伴 A 费用
    pub total_partner_a_fee: u64,
    /// 总伙伴 B 费用
    pub total_partner_b_fee: u64,
    /// 总仓位数
    pub total_position: u64,
    /// 填充
    pub padding: u64,
}

/// 奖励信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RewardInfo {
    /// 是否已初始化
    pub initialized: u8,
    /// 奖励代币标志
    pub reward_token_flag: u8,
    /// 填充 0
    pub _padding_0: [u8; 6],
    /// 填充 1
    pub _padding_1: [u8; 8],
    /// 奖励代币 mint
    pub mint: Pubkey,
    /// 奖励金库代币账户
    pub vault: Pubkey,
    /// 资助者权威账户
    pub funder: Pubkey,
    /// 奖励持续时间
    pub reward_duration: u64,
    /// 奖励持续时间结束
    pub reward_duration_end: u64,
    /// 奖励率
    pub reward_rate: u128,
    /// 每代币存储的奖励
    pub reward_per_token_stored: [u8; 32],
    /// 最后更新时间
    pub last_update_time: u64,
    /// 空流动性奖励累积秒数
    pub cumulative_seconds_with_empty_liquidity_reward: u64,
}

/// Meteora DAMM V2 池状态
/// 基于 meteora_damm_v2.json IDL 中的 Pool 结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Pool {
    /// 池费用信息
    pub pool_fees: PoolFeesStruct,
    /// 代币 A mint
    pub token_a_mint: Pubkey,
    /// 代币 B mint
    pub token_b_mint: Pubkey,
    /// 代币 A 金库
    pub token_a_vault: Pubkey,
    /// 代币 B 金库
    pub token_b_vault: Pubkey,
    /// 激活点之前能够购买池的白名单金库
    pub whitelisted_vault: Pubkey,
    /// 伙伴
    pub partner: Pubkey,
    /// 流动性份额
    pub liquidity: u128,
    /// 填充，之前的储备金额，请小心使用该字段
    pub _padding: u128,
    /// 协议 A 费用
    pub protocol_a_fee: u64,
    /// 协议 B 费用
    pub protocol_b_fee: u64,
    /// 伙伴 A 费用
    pub partner_a_fee: u64,
    /// 伙伴 B 费用
    pub partner_b_fee: u64,
    /// 最小价格的平方根
    pub sqrt_min_price: u128,
    /// 最大价格的平方根
    pub sqrt_max_price: u128,
    /// 当前价格的平方根
    pub sqrt_price: u128,
    /// 激活点，可以是 slot 或时间戳
    pub activation_point: u64,
    /// 激活类型，0 表示按 slot，1 表示按时间戳
    pub activation_type: u8,
    /// 池状态，0: 启用，1 禁用
    pub pool_status: u8,
    /// 代币 A 标志
    pub token_a_flag: u8,
    /// 代币 B 标志
    pub token_b_flag: u8,
    /// 收费模式，0 是两种代币都收费，1 只在代币 A 收费，2 只在代币 B 收费
    pub collect_fee_mode: u8,
    /// 池类型
    pub pool_type: u8,
    /// 填充 0
    pub _padding_0: [u8; 2],
    /// 每流动性的累积费用 A
    pub fee_a_per_liquidity: [u8; 32],
    /// 每流动性的累积费用 B
    pub fee_b_per_liquidity: [u8; 32],
    /// 永久锁定流动性
    pub permanent_lock_liquidity: u128,
    /// 指标
    pub metrics: PoolMetrics,
    pub creator: Pubkey,
    /// 进一步使用的填充 1
    pub _padding_1: [u64; 6],
    /// 农场奖励信息
    pub reward_infos: [RewardInfo; 2],
}

/// 位置指标
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PositionMetrics {
    /// 总已领取 A 费用
    pub total_claimed_a_fee: u64,
    /// 总已领取 B 费用
    pub total_claimed_b_fee: u64,
}

/// 用户奖励信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct UserRewardInfo {
    /// 最新更新的奖励检查点
    pub reward_per_token_checkpoint: [u8; 32],
    /// 当前待领取奖励
    pub reward_pendings: u64,
    /// 总已领取奖励
    pub total_claimed_rewards: u64,
}

/// 位置状态
/// 基于 meteora_damm_v2.json IDL 中的 Position 结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Position {
    /// 池地址
    pub pool: Pubkey,
    /// NFT mint
    pub nft_mint: Pubkey,
    /// 费用 A 检查点
    pub fee_a_per_token_checkpoint: [u8; 32],
    /// 费用 B 检查点
    pub fee_b_per_token_checkpoint: [u8; 32],
    /// 待领取费用 A
    pub fee_a_pending: u64,
    /// 待领取费用 B
    pub fee_b_pending: u64,
    /// 已解锁流动性
    pub unlocked_liquidity: u128,
    /// 已归属流动性
    pub vested_liquidity: u128,
    /// 永久锁定流动性
    pub permanent_locked_liquidity: u128,
    /// 指标
    pub metrics: PositionMetrics,
    /// 农场奖励信息
    pub reward_infos: [UserRewardInfo; 2],
    /// 填充
    pub padding: [u128; 6],
}

/// 池费用配置基础结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct BaseFeeConfig {
    /// 悬崖费用分子
    pub cliff_fee_numerator: u64,
    /// 费用调度模式
    pub fee_scheduler_mode: u8,
    /// 填充 0
    pub padding_0: [u8; 5],
    /// 周期数量
    pub number_of_period: u16,
    /// 周期频率
    pub period_frequency: u64,
    /// 减少因子
    pub reduction_factor: u64,
    /// 填充 1
    pub padding_1: u64,
}

/// 动态费用配置
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct DynamicFeeConfig {
    /// 是否已初始化
    pub initialized: u8,
    /// 填充
    pub padding: [u8; 7],
    /// 最大波动率累积器
    pub max_volatility_accumulator: u32,
    /// 变量费用控制
    pub variable_fee_control: u32,
    /// bin step
    pub bin_step: u16,
    /// 过滤周期
    pub filter_period: u16,
    /// 衰减周期
    pub decay_period: u16,
    /// 减少因子
    pub reduction_factor: u16,
    /// 填充
    pub padding_2: [u64; 25],
}

/// 池费用配置
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PoolFeesConfig {
    /// 基础费用
    pub base_fee: BaseFeeConfig,
    /// 动态费用
    pub dynamic_fee: DynamicFeeConfig,
    /// 填充
    pub padding: [u64; 25],
}

/// 配置状态
/// 基于 meteora_damm_v2.json IDL 中的 Config 结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Config {
    /// 金库配置密钥
    pub vault_config_key: Pubkey,
    /// 池创建者权威
    pub pool_creator_authority: Pubkey,
    /// 池费用配置
    pub pool_fees: PoolFeesConfig,
    /// 激活类型
    pub activation_type: u8,
    /// 收费模式
    pub collect_fee_mode: u8,
    /// 配置类型模式，0 为静态，1 为动态
    pub config_type: u8,
    /// 填充 0
    pub _padding_0: [u8; 5],
    /// 配置索引
    pub index: u64,
    /// 最小价格的平方根
    pub sqrt_min_price: u128,
    /// 最大价格的平方根
    pub sqrt_max_price: u128,
    /// 填充
    pub padding: [u64; 13],
}

/// 费用操作员
/// 基于 meteora_damm_v2.json IDL 中的 ClaimFeeOperator 结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ClaimFeeOperator {
    /// 操作员
    pub operator: Pubkey,
    /// 保留字段
    pub _padding: [u8; 128],
}

/// 代币徽章
/// 基于 meteora_damm_v2.json IDL 中的 TokenBadge 结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct TokenBadge {
    /// 代币 mint
    pub token_mint: Pubkey,
    /// 保留字段
    pub _padding: [u8; 128],
}

/// 归属状态
/// 基于 meteora_damm_v2.json IDL 中的 Vesting 结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Vesting {
    /// 位置
    pub position: Pubkey,
    /// 悬崖点
    pub cliff_point: u64,
    /// 周期频率
    pub period_frequency: u64,
    /// 悬崖解锁流动性
    pub cliff_unlock_liquidity: u128,
    /// 每周期流动性
    pub liquidity_per_period: u128,
    /// 总已释放流动性
    pub total_released_liquidity: u128,
    /// 周期数量
    pub number_of_period: u16,
    /// 填充
    pub padding: [u8; 14],
    /// 填充 2
    pub padding2: [u128; 6],
}
