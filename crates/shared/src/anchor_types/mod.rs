//! Anchor 类型定义模块
//!
//! 基于 IDL 文件生成的 Anchor 类型定义，用于替换手动解析

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

pub mod raydium;
pub mod raydium_cpmm;
pub mod meteora;
pub mod meteora_damm;
pub mod pump_swap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenMintInfo {
    pub mint: Pubkey,
    pub decimals: u8,
    pub token_vault: Pubkey,
    pub token_balance: u128,
}

impl TokenMintInfo {
    pub fn new(mint: Pubkey, decimals: u8, token_vault: Pubkey, token_balance: u128) -> Self {
        Self {
            mint,
            decimals,
            token_vault,
            token_balance,
        }
    }
}


// 重新导出，避免名称冲突
pub use raydium::{
    RAYDIUM_CLMM_PROGRAM_ID, PoolState, TickArrayState, TickArrayBitmapExtension,
    ObservationState, PersonalPositionState, ProtocolPositionState, TickState, Observation,
    RewardInfo as RaydiumRewardInfo
};
pub use raydium_cpmm::{
    RAYDIUM_CPMM_PROGRAM_ID,
    PoolState as RaydiumCpmmPoolState,
    AmmConfig as RaydiumCpmmAmmConfig,
    ObservationState as RaydiumCpmmObservationState,
    Observation as RaydiumCpmmObservation,
    RaydiumCpmmAccountType,
    identify_account_type as identify_raydium_cpmm_account_type,
    AMM_CONFIG_DISCRIMINATOR, POOL_STATE_DISCRIMINATOR, OBSERVATION_STATE_DISCRIMINATOR
};
pub use meteora::{
    METEORA_DLMM_PROGRAM_ID, LbPair, BinArray, BinArrayBitmapExtension,
    Oracle, Position, PositionV2, StaticParameters, VariableParameters, Bin,
    RewardInfo as MeteoraRewardInfo, UserRewardInfo, FeeInfo
};
pub use meteora_damm::{
    METEORA_DAMM_V2_PROGRAM_ID, Pool as MeteoraDammPool,
    PoolFeesStruct as MeteoraDammPoolFeesStruct, BaseFeeStruct as MeteoraDammBaseFeeStruct,
    DynamicFeeStruct as MeteoraDammDynamicFeeStruct, PoolMetrics as MeteoraDammPoolMetrics,
    RewardInfo as MeteoraDammRewardInfo
};
pub use pump_swap::{
    PUMP_SWAP_PROGRAM_ID, GlobalConfig as PumpSwapGlobalConfig, Pool as PumpSwapPool,
    PumpSwapAccountType, GLOBAL_CONFIG_DISCRIMINATOR, POOL_DISCRIMINATOR,
    identify_account_type as identify_pump_swap_account_type
};
