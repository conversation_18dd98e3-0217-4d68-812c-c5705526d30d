//! Pump Swap Anchor 类型定义
//!
//! 基于 pump_swap.json IDL 定义的账号结构

use anchor_lang::prelude::*;

/// Pump Swap 程序 ID
pub const PUMP_SWAP_PROGRAM_ID: Pubkey = pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");

/// GlobalConfig 账号 discriminator
pub const GLOBAL_CONFIG_DISCRIMINATOR: [u8; 8] = [149, 8, 156, 202, 160, 252, 176, 217];

/// Pool 账号 discriminator
pub const POOL_DISCRIMINATOR: [u8; 8] = [241, 154, 109, 4, 17, 177, 109, 188];

/// GlobalConfig 账号结构
///
/// 全局配置信息，包含管理员、费用配置等信息
#[derive(Debug, Clone, PartialEq, AnchorSerialize, AnchorDeserialize)]
pub struct GlobalConfig {
    /// 管理员公钥
    pub admin: Pubkey,
    /// LP 费用基点（0.01%）
    pub lp_fee_basis_points: u64,
    /// 协议费用基点（0.01%）
    pub protocol_fee_basis_points: u64,
    /// 禁用标志位
    /// bit 0 - 禁用创建池
    /// bit 1 - 禁用存款
    /// bit 2 - 禁用提款
    /// bit 3 - 禁用买入
    /// bit 4 - 禁用卖出
    pub disable_flags: u8,
    /// 协议费用接收者地址（最多8个）
    pub protocol_fee_recipients: [Pubkey; 8],
}

impl Default for GlobalConfig {
    fn default() -> Self {
        Self {
            admin: Pubkey::default(),
            lp_fee_basis_points: 0,
            protocol_fee_basis_points: 0,
            disable_flags: 0,
            protocol_fee_recipients: [Pubkey::default(); 8],
        }
    }
}

impl GlobalConfig {
    /// 检查创建池是否被禁用
    pub fn is_create_pool_disabled(&self) -> bool {
        (self.disable_flags & 0x01) != 0
    }

    /// 检查存款是否被禁用
    pub fn is_deposit_disabled(&self) -> bool {
        (self.disable_flags & 0x02) != 0
    }

    /// 检查提款是否被禁用
    pub fn is_withdraw_disabled(&self) -> bool {
        (self.disable_flags & 0x04) != 0
    }

    /// 检查买入是否被禁用
    pub fn is_buy_disabled(&self) -> bool {
        (self.disable_flags & 0x08) != 0
    }

    /// 检查卖出是否被禁用
    pub fn is_sell_disabled(&self) -> bool {
        (self.disable_flags & 0x10) != 0
    }

    /// 获取活跃的协议费用接收者（非零地址）
    pub fn active_protocol_fee_recipients(&self) -> Vec<Pubkey> {
        self.protocol_fee_recipients
            .iter()
            .filter(|&&addr| addr != Pubkey::default())
            .copied()
            .collect()
    }
}

/// Pool 账号结构
///
/// 流动性池信息，包含基础和报价代币、LP 代币、储备量等
#[derive(Debug, Clone, PartialEq, AnchorSerialize, AnchorDeserialize)]
pub struct Pool {
    /// 池子的 bump seed
    pub pool_bump: u8,
    /// 池子索引
    pub index: u16,
    /// 创建者公钥
    pub creator: Pubkey,
    /// 基础代币 mint 地址
    pub base_mint: Pubkey,
    /// 报价代币 mint 地址
    pub quote_mint: Pubkey,
    /// LP 代币 mint 地址
    pub lp_mint: Pubkey,
    /// 池子基础代币账户地址
    pub pool_base_token_account: Pubkey,
    /// 池子报价代币账户地址
    pub pool_quote_token_account: Pubkey,
    /// LP 代币真实流通供应量（不包括销毁和锁定）
    pub lp_supply: u64,
    /// 币创建者地址
    pub coin_creator: Pubkey,
    /// 预留填充字段（用于未来扩展）
    pub padding: [u8; 57],
}

impl Pool {
    /// 获取基础和报价代币的 mint 地址对
    pub fn mint_pair(&self) -> (Pubkey, Pubkey) {
        (self.base_mint, self.quote_mint)
    }

    /// 检查是否是指定的基础代币池
    pub fn is_base_mint(&self, mint: &Pubkey) -> bool {
        self.base_mint == *mint
    }

    /// 检查是否是指定的报价代币池
    pub fn is_quote_mint(&self, mint: &Pubkey) -> bool {
        self.quote_mint == *mint
    }

    /// 检查池子是否包含指定的代币
    pub fn contains_mint(&self, mint: &Pubkey) -> bool {
        self.base_mint == *mint || self.quote_mint == *mint
    }

    /// 获取池子的唯一标识符（基于关键字段的哈希）
    pub fn pool_id(&self) -> String {
        format!("{}-{}-{}-{}",
            self.index,
            self.creator,
            self.base_mint,
            self.quote_mint
        )
    }

    /// 获取池子的简要信息
    pub fn summary(&self) -> String {
        format!(
            "Pool #{}: {}/{} (Creator: {}, LP Supply: {})",
            self.index,
            self.base_mint,
            self.quote_mint,
            self.creator,
            self.lp_supply
        )
    }
}

/// 账号类型识别辅助函数
pub fn identify_account_type(data: &[u8]) -> Option<PumpSwapAccountType> {
    if data.len() < 8 {
        return None;
    }

    let discriminator = &data[0..8];

    if discriminator == GLOBAL_CONFIG_DISCRIMINATOR {
        Some(PumpSwapAccountType::GlobalConfig)
    } else if discriminator == POOL_DISCRIMINATOR {
        Some(PumpSwapAccountType::Pool)
    } else {
        None
    }
}

/// Pump Swap 账号类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum PumpSwapAccountType {
    GlobalConfig,
    Pool,
}

impl PumpSwapAccountType {
    /// 获取账号类型的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            PumpSwapAccountType::GlobalConfig => "GlobalConfig",
            PumpSwapAccountType::Pool => "Pool",
        }
    }

    /// 获取账号类型的 discriminator
    pub fn discriminator(&self) -> [u8; 8] {
        match self {
            PumpSwapAccountType::GlobalConfig => GLOBAL_CONFIG_DISCRIMINATOR,
            PumpSwapAccountType::Pool => POOL_DISCRIMINATOR,
        }
    }
}
