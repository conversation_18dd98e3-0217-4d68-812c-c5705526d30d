//! Raydium CPMM Anchor 类型定义
//!
//! 基于 raydium_cpmm.json IDL 文件生成的类型定义

use anchor_lang::prelude::*;

/// Raydium CPMM 程序ID
pub const RAYDIUM_CPMM_PROGRAM_ID: Pubkey =
    pubkey!("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C");

/// 观察数据元素
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Observation {
    /// 观察的区块时间戳
    pub block_timestamp: u64,
    /// token0价格在持续时间内的累积，Q32.32格式，剩余64位用于溢出
    pub cumulative_token0_price_x32: u128,
    /// token1价格在持续时间内的累积，Q32.32格式，剩余64位用于溢出
    pub cumulative_token1_price_x32: u128,
}

/// AMM配置账户
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct AmmConfig {
    /// 用于识别PDA的Bump
    pub bump: u8,
    /// 控制是否可以创建新池的状态
    pub disable_create_pool: bool,
    /// 配置索引
    pub index: u16,
    /// 交易费率，以万分之一（10^-6）计价
    pub trade_fee_rate: u64,
    /// 协议费率，以万分之一（10^-6）计价
    pub protocol_fee_rate: u64,
    /// 基金费率，以万分之一（10^-6）计价
    pub fund_fee_rate: u64,
    /// 创建新池的费用
    pub create_pool_fee: u64,
    /// 协议费用所有者地址
    pub protocol_owner: Pubkey,
    /// 基金费用所有者地址
    pub fund_owner: Pubkey,
    /// 填充字段
    pub padding: [u64; 16],
}

// AMM config 实现 default
impl Default for AmmConfig {
    fn default() -> Self {
        Self {
            bump: 0,
            disable_create_pool: false,
            index: 0,
            trade_fee_rate: 0,
            protocol_fee_rate: 0,
            fund_fee_rate: 0,
            create_pool_fee: 0,
            protocol_owner: Pubkey::default(),
            fund_owner: Pubkey::default(),
            padding: [0; 16],
        }
    }
}


/// 观察状态账户
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ObservationState {
    /// 是否已初始化ObservationState
    pub initialized: bool,
    /// 观察数组的最近更新索引
    pub observation_index: u16,
    /// 池ID
    pub pool_id: Pubkey,
    /// 观察数组（100个元素）
    pub observations: [Observation; 100],
    /// 为将来功能更新预留的填充
    pub padding: [u64; 4],
}

/// Raydium CPMM 池状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PoolState {
    /// 池所属的配置
    pub amm_config: Pubkey,
    /// 池创建者
    pub pool_creator: Pubkey,
    /// Token A 金库
    pub token0_vault: Pubkey,
    /// Token B 金库
    pub token1_vault: Pubkey,
    /// LP代币铸造账户
    /// 当存入A或B代币时发行池代币。
    /// 池代币可以提取回原始的A或B代币。
    pub lp_mint: Pubkey,
    /// Token A 的铸造信息
    pub token0_mint: Pubkey,
    /// Token B 的铸造信息
    pub token1_mint: Pubkey,
    /// token_0 程序
    pub token0_program: Pubkey,
    /// token_1 程序
    pub token1_program: Pubkey,
    /// 存储预言机数据的观察账户
    pub observation_key: Pubkey,
    /// 授权Bump
    pub auth_bump: u8,
    /// 池状态的位标识
    /// bit0, 1: 禁用存款(值为1), 0: 正常
    /// bit1, 1: 禁用提取(值为2), 0: 正常
    /// bit2, 1: 禁用交换(值为4), 0: 正常
    pub status: u8,
    /// LP代币精度
    pub lp_mint_decimals: u8,
    /// mint0和mint1的精度
    pub mint0_decimals: u8,
    /// mint1精度
    pub mint1_decimals: u8,
    /// LP代币供应量
    pub lp_supply: u64,
    /// 欠流动性提供者的token_0和token_1数量
    pub protocol_fees_token0: u64,
    /// 协议费用token1
    pub protocol_fees_token1: u64,
    /// 基金费用token0
    pub fund_fees_token0: u64,
    /// 基金费用token1
    pub fund_fees_token1: u64,
    /// 池中允许交换的时间戳
    pub open_time: u64,
    /// 为将来更新预留的填充
    pub padding: [u64; 32],
}

/// Raydium CPMM 账号类型枚举
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RaydiumCpmmAccountType {
    AmmConfig,
    PoolState,
    ObservationState,
    Unknown,
}

/// Discriminator 常量
///
/// 注意：这些 discriminator 值需要从实际的 Raydium CPMM 程序中获取。
/// 由于 IDL 文件中没有提供，目前使用占位符值。
///
/// 要获取正确的 discriminator 值，可以：
/// 1. 查看 Raydium CPMM 程序的源代码
/// 2. 从链上实际账户数据中提取前8字节
/// 3. 使用 Anchor 的 discriminator 计算方法：sha256("account:<AccountName>")[..8]
///
/// 例如：
/// - AmmConfig: sha256("account:AmmConfig")[..8]
/// - PoolState: sha256("account:PoolState")[..8]
/// - ObservationState: sha256("account:ObservationState")[..8]
pub const AMM_CONFIG_DISCRIMINATOR: [u8; 8] = [0, 0, 0, 0, 0, 0, 0, 0]; // 占位符 - 需要替换
pub const POOL_STATE_DISCRIMINATOR: [u8; 8] = [247, 237, 227, 245, 215, 195, 222, 70];
pub const OBSERVATION_STATE_DISCRIMINATOR: [u8; 8] = [0, 0, 0, 0, 0, 0, 0, 2]; // 占位符 - 需要替换

/// 根据账号数据识别 Raydium CPMM 账号类型
pub fn identify_account_type(data: &[u8]) -> RaydiumCpmmAccountType {
    if data.len() < 8 {
        return RaydiumCpmmAccountType::Unknown;
    }

    let discriminator: [u8; 8] = match data[0..8].try_into() {
        Ok(d) => d,
        Err(_) => return RaydiumCpmmAccountType::Unknown,
    };

    match discriminator {
        AMM_CONFIG_DISCRIMINATOR => RaydiumCpmmAccountType::AmmConfig,
        POOL_STATE_DISCRIMINATOR => RaydiumCpmmAccountType::PoolState,
        OBSERVATION_STATE_DISCRIMINATOR => RaydiumCpmmAccountType::ObservationState,
        _ => RaydiumCpmmAccountType::Unknown,
    }
}
