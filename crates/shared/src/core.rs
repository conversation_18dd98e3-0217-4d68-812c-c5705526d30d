//! 套利检测核心模块
//!
//! 包含所有必要的数据结构和简化的套利检测逻辑

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

/// 标准化代币对
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TokenPair {
    /// 字典序较小的代币地址
    pub token_a: Pubkey,
    /// 字典序较大的代币地址
    pub token_b: Pubkey,
}

impl TokenPair {
    /// 创建标准化代币对，自动排序
    pub fn new(token_x: Pubkey, token_y: Pubkey) -> Self {
        if token_x < token_y {
            Self { token_a: token_x, token_b: token_y }
        } else {
            Self { token_a: token_y, token_b: token_x }
        }
    }

    /// 检查是否包含指定代币
    pub fn contains(&self, token: &Pubkey) -> bool {
        &self.token_a == token || &self.token_b == token
    }
}

impl std::fmt::Display for TokenPair {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}/{}", self.token_a, self.token_b)
    }
}

/// DEX 协议类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DexProtocol {
    /// Raydium DEX
    Raydium,
    /// Meteora DEX
    Meteora,
    /// Orca DEX
    Orca,
    /// PumpFun DEX
    PumpFun,
    /// Jupiter DEX
    Jupiter,
    /// Serum DEX
    Serum,
    /// 其他 DEX
    Other(u8),
}

/// 池类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PoolType {
    /// 恒定乘积做市商 (Constant Product Market Maker)
    Cpmm,
    /// 集中流动性做市商 (Concentrated Liquidity Market Maker)
    Clmm,
    /// 动态流动性做市商 (Dynamic Liquidity Market Maker)
    Dlmm,
    Damm,
    PumpSwap,
    /// 稳定币池 (Stable Pool)
    Stable,
    /// 加权池 (Weighted Pool)
    Weighted,
}

impl std::fmt::Display for DexProtocol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DexProtocol::Raydium => write!(f, "Raydium"),
            DexProtocol::Meteora => write!(f, "Meteora"),
            DexProtocol::Orca => write!(f, "Orca"),
            DexProtocol::PumpFun => write!(f, "PumpFun"),
            DexProtocol::Jupiter => write!(f, "Jupiter"),
            DexProtocol::Serum => write!(f, "Serum"),
            DexProtocol::Other(id) => write!(f, "Other({})", id),
        }
    }
}

impl std::fmt::Display for PoolType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PoolType::Cpmm => write!(f, "CPMM"),
            PoolType::Clmm => write!(f, "CLMM"),
            PoolType::Dlmm => write!(f, "DLMM"),
            PoolType::Damm => write!(f, "Damm"),
            PoolType::PumpSwap => write!(f, "PumpSwap"),
            PoolType::Stable => write!(f, "Stable"),
            PoolType::Weighted => write!(f, "Weighted"),
        }
    }
}
