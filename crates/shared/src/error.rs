use thiserror::Error;

/// 简化的套利检测错误类型
#[derive(Error, Debug)]
pub enum ArbitrageError {
    #[error("配置错误: {0}")]
    Config(String),

    #[error("数据错误: {0}")]
    Data(String),

    #[error("内部错误: {0}")]
    Internal(String),

    #[error("序列化错误: {0}")]
    Serialization(String),

    #[error("不支持的操作: {0}")]
    UnsupportedOperation(String),
}

/// 套利检测结果类型
pub type ArbitrageResult<T> = std::result::Result<T, ArbitrageError>;

/// 保持向后兼容的旧错误类型
#[derive(Error, Debug)]
pub enum EchoesError {
    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Network error: {0}")]
    Network(String),

    #[error("Parsing error: {0}")]
    Parsing(String),

    #[error("Parse error: {0}")]
    Parse(String),

    #[error("Storage error: {0}")]
    Storage(String),

    #[error("Internal error: {0}")]
    Internal(String),

    #[error("Invalid state: {0}")]
    InvalidState(String),

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("IO error: {0}")]
    Io(String),
}

/// 通用结果类型
pub type Result<T> = std::result::Result<T, EchoesError>;
