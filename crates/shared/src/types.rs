use yellowstone_grpc_proto::prelude::SubscribeUpdateTransaction;
use yellowstone_grpc_proto::convert_from::create_tx_with_meta;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::Signature;
use solana_sdk::transaction::VersionedTransaction;
use solana_transaction_status::{EncodedTransactionWithStatusMeta, TransactionStatusMeta, UiTransactionEncoding};

/// 系统中流动的数据类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataType {
    Transaction(TransactionData),
    Account(AccountData),
    Block(BlockData),
    Slot(SlotData),
}

/// 账户数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountData {
    pub pubkey: String,
    pub slot: u64,
    pub lamports: u64,
    pub owner: String,
    pub data: Vec<u8>,
    pub executable: bool,
    pub rent_epoch: u64,
}

/// 区块数据结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BlockData {
    pub slot: u64,
    pub hash: String,
    pub parent_slot: u64,
    pub parent_hash: String,
    pub timestamp: Option<i64>,
}

/// 插槽数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlotData {
    pub slot: u64,
    pub parent: Option<u64>,
    pub status: String,
}

/// 交易数据结构（注意：某些字段无法序列化，需要特殊处理）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionData {
    pub slot: u64,
    #[serde(with = "signature_serde")]
    pub signature: Signature,
    pub index: u64,
    #[serde(skip)]
    pub meta: Option<TransactionStatusMeta>,
    #[serde(skip)]
    pub transaction: VersionedTransaction,
    #[serde(with = "pubkey_vec_serde")]
    pub account_keys: Vec<Pubkey>,
    pub tx: EncodedTransactionWithStatusMeta
}

// 自定义序列化模块for Signature
mod signature_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use solana_sdk::signature::Signature;
    use std::str::FromStr;

    pub fn serialize<S>(signature: &Signature, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        signature.to_string().serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Signature, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        Signature::from_str(&s).map_err(serde::de::Error::custom)
    }
}

// 自定义序列化模块for Vec<Pubkey>
mod pubkey_vec_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    pub fn serialize<S>(pubkeys: &[Pubkey], serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let strings: Vec<String> = pubkeys.iter().map(|k| k.to_string()).collect();
        strings.serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Vec<Pubkey>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let strings = Vec::<String>::deserialize(deserializer)?;
        strings
            .into_iter()
            .map(|s| Pubkey::from_str(&s).map_err(serde::de::Error::custom))
            .collect()
    }
}

/// 可序列化的交易数据结构（用于消息传递）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableTransactionData {
    pub slot: u64,
    pub signature: String,
    pub index: u64,
    pub account_keys: Vec<String>,
    // 注意：这里省略了meta和transaction字段，因为它们包含复杂的内部类型
    // 实际项目中可能需要更完整的序列化策略
}

impl From<TransactionData> for SerializableTransactionData {
    fn from(tx: TransactionData) -> Self {
        Self {
            slot: tx.slot,
            signature: tx.signature.to_string(),
            index: tx.index,
            account_keys: tx.account_keys.iter().map(|k| k.to_string()).collect(),
        }
    }
}

impl From<SubscribeUpdateTransaction> for TransactionData {
    fn from(SubscribeUpdateTransaction { transaction, slot }: SubscribeUpdateTransaction) -> Self {
        let raw = transaction.expect("should be defined");
        let index = raw.index;
        let tx = create_tx_with_meta(raw).expect("valid tx with meta");
        Self {
            slot,
            index,
            signature: *tx.transaction_signature(),
            meta: tx.get_status_meta(),
            transaction: tx.get_transaction(),
            account_keys: tx.account_keys().iter().copied().collect(),
            tx: tx.encode(UiTransactionEncoding::Base64, Some(u8::MAX), true)
                .expect("failed to encode"),
        }
    }
}

// 为了向后兼容，重新导出一些类型
pub use crate::core::{DexProtocol, PoolType};

/// 标准化代币对（向后兼容）
pub type NormalizedTokenPair = crate::core::TokenPair;

/// 标准化池价格信息（向后兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NormalizedPoolPrice {
    /// 池地址
    pub pool_address: Pubkey,
    /// 标准化代币对
    pub token_pair: NormalizedTokenPair,
    /// 标准化价格（始终是 token_max/token_min 的价格）
    pub normalized_price: f64,
    /// 池子的原始价格
    pub original_price: f64,
    /// 是否进行了价格反转
    pub is_reversed: bool,
    /// 流动性
    pub liquidity: u128,
    /// DEX 协议
    pub protocol: DexProtocol,
    /// 池类型
    pub pool_type: PoolType,
    /// 最后更新时间戳（毫秒）
    pub last_updated: u64,
}

impl NormalizedPoolPrice {
    /// 创建标准化池价格
    pub fn new(
        pool_address: Pubkey,
        token_a: Pubkey,
        token_b: Pubkey,
        original_price: f64,
        liquidity: u128,
        protocol: DexProtocol,
        pool_type: PoolType,
    ) -> Self {
        let token_pair = NormalizedTokenPair::new(token_a, token_b);
        let is_reversed = token_a > token_b; // 简化的反转检查

        // 标准化价格：始终是 token_max/token_min
        let normalized_price = if is_reversed {
            if original_price != 0.0 {
                1.0 / original_price // 需要取倒数
            } else {
                0.0 // 处理除零情况
            }
        } else {
            original_price // 保持原样
        };

        Self {
            pool_address,
            token_pair,
            normalized_price,
            original_price,
            is_reversed,
            liquidity,
            protocol,
            pool_type,
            last_updated: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_millis() as u64,
        }
    }

    /// 更新价格
    pub fn update_price(&mut self, new_original_price: f64) {
        self.original_price = new_original_price;
        self.normalized_price = if self.is_reversed {
            if new_original_price != 0.0 {
                1.0 / new_original_price
            } else {
                0.0
            }
        } else {
            new_original_price
        };
        self.last_updated = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64;
    }
}
