//! # 密钥保险库
//!
//! 提供安全的Solana密钥存储和管理功能，使用Argon2id + AES-256-GCM加密

use std::fs;
use std::path::Path;
use argon2::Argon2;
use aes_gcm::{
    aead::{Aead, KeyInit},
    Aes256Gcm, Nonce
};
use solana_sdk::signature::Keypair;
use zeroize::{Zeroize, ZeroizeOnDrop};
use thiserror::Error;
use argon2::{
    password_hash::{
        rand_core::OsRng,
        rand_core::RngCore
    }};

    #[cfg(unix)]
use std::os::unix::fs::PermissionsExt;

/// 密钥保险库错误类型
#[derive(Error, Debug)]
pub enum VaultError {
    #[error("密码验证失败: {0}")]
    PasswordValidation(String),

    #[error("Argon2参数配置错误")]
    Argon2Config,

    #[error("密码哈希计算失败")]
    PasswordHashing,

    #[error("加密器创建失败")]
    CipherCreation,

    #[error("数据加密失败")]
    Encryption,

    #[error("数据解密失败")]
    Decryption,

    #[error("密码错误或数据损坏")]
    InvalidPasswordOrData,

    #[error("无效的密钥对数据: {0}")]
    InvalidKeypair(String),

    #[error("保险库文件格式无效: {0}")]
    InvalidVaultFormat(String),

    #[error("文件操作失败: {path}: {error}")]
    FileOperation { path: String, error: String },

    #[error("文件权限设置失败: {path}: {error}")]
    PermissionError { path: String, error: String },
}

/// 保险库操作结果类型
pub type VaultResult<T> = Result<T, VaultError>;

/// 安全的字节容器，在销毁时自动清零内存
#[derive(ZeroizeOnDrop)]
struct SecureBytes(Vec<u8>);

impl SecureBytes {
    fn new(data: Vec<u8>) -> Self {
        Self(data)
    }

    fn as_slice(&self) -> &[u8] {
        &self.0
    }
}

/// 密钥保险库，提供加密存储Solana密钥对的功能
#[derive(Debug)]
pub struct KeypairVault {
    encrypted_data: Vec<u8>,
    salt: [u8; 16],
    nonce: [u8; 12],
}

impl KeypairVault {
    /// 创建新的密钥保险库
    ///
    /// # 参数
    /// - `password`: 用于加密的密码
    /// - `keypair`: 要存储的密钥对
    ///
    /// # 返回
    /// - `Ok(KeypairVault)`: 成功创建的保险库
    /// - `Err`: 创建失败的错误信息
    pub fn create(password: &str, keypair: &Keypair) -> VaultResult<Self> {
        // 生成加密用的随机salt
        let mut salt_bytes = [0u8; 16];
        // rand::rng().fill_bytes(&mut salt_bytes);
        OsRng.fill_bytes(&mut salt_bytes);

        // 配置高安全性的Argon2参数
        let argon2 = Argon2::new(
            argon2::Algorithm::Argon2id,  // 最安全的Argon2变体
            argon2::Version::V0x13,       // 最新版本
            argon2::Params::new(
                128 * 1024,              // 内存成本: 128 MiB (高内存硬度)
                3,                        // 时间成本: 3次迭代 (增加计算时间)
                4,                        // 并行度: 4线程 (适配现代CPU)
                Some(32)                  // 输出长度: 32字节 (匹配AES-256)
            ).map_err(|_| VaultError::Argon2Config)?
        );

        // 生成AES加密密钥
        let mut encryption_key = [0u8; 32];
        argon2.hash_password_into(
            password.as_bytes(),
            &salt_bytes,
            &mut encryption_key,
        ).map_err(|_| VaultError::PasswordHashing)?;

        // 生成随机nonce用于AES-GCM
        let mut nonce = [0u8; 12];
        // rand::rng().fill_bytes(&mut nonce);
        OsRng.fill_bytes(&mut nonce);

        // 创建AES-GCM加密器
        let cipher = Aes256Gcm::new_from_slice(&encryption_key)
            .map_err(|_| VaultError::CipherCreation)?;

        // 序列化密钥对为字节
        let keypair_bytes = keypair.to_bytes();
        let secure_keypair = SecureBytes::new(keypair_bytes.to_vec());

        // 加密密钥对数据
        let encrypted_data = cipher
            .encrypt(Nonce::from_slice(&nonce), secure_keypair.as_slice())
            .map_err(|_| VaultError::Encryption)?;

        // 手动清零加密密钥
        encryption_key.zeroize();

        Ok(Self {
            encrypted_data,
            salt: salt_bytes,
            nonce,
        })
    }

    /// 将保险库保存到文件
    ///
    /// # 参数
    /// - `path`: 保存路径
    ///
    /// # 返回
    /// - `Ok(())`: 保存成功
    /// - `Err`: 保存失败的错误信息
    pub fn save(&self, path: impl AsRef<Path>) -> VaultResult<()> {
        let path = path.as_ref();

        // 构建文件数据: salt(16) + nonce(12) + encrypted_data
        let mut data = Vec::new();
        data.extend_from_slice(&self.salt);
        data.extend_from_slice(&self.nonce);
        data.extend_from_slice(&self.encrypted_data);

        // 写入文件
        fs::write(path, data)
            .map_err(|e| VaultError::FileOperation {
                path: path.display().to_string(),
                error: e.to_string(),
            })?;

        // 设置严格的文件权限 (Unix系统)
        #[cfg(unix)]
        {
            let mut perms = fs::metadata(path).map_err(|e| VaultError::FileOperation {
                path: path.display().to_string(),
                error: e.to_string(),
            })?.permissions();
            perms.set_mode(0o600); // 只有所有者可读写
            fs::set_permissions(path, perms)
                .map_err(|e| VaultError::PermissionError {
                    path: path.display().to_string(),
                    error: e.to_string(),
                })?;
        }

        Ok(())
    }

    /// 从文件加载保险库
    ///
    /// # 参数
    /// - `path`: 保险库文件路径
    ///
    /// # 返回
    /// - `Ok(KeypairVault)`: 加载成功的保险库
    /// - `Err`: 加载失败的错误信息
    pub fn load(path: impl AsRef<Path>) -> VaultResult<Self> {
        let path = path.as_ref();
        let data = fs::read(path)
            .map_err(|e| VaultError::FileOperation {
                path: path.display().to_string(),
                error: e.to_string(),
            })?;

        // 验证文件格式
        if data.len() < 28 { // 16 (salt) + 12 (nonce) + 至少1字节的加密数据
            return Err(VaultError::InvalidVaultFormat("文件过小".to_string()));
        }

        let mut salt = [0u8; 16];
        let mut nonce = [0u8; 12];

        salt.copy_from_slice(&data[0..16]);
        nonce.copy_from_slice(&data[16..28]);

        let encrypted_data = data[28..].to_vec();

        Ok(Self {
            encrypted_data,
            salt,
            nonce,
        })
    }

    /// 解密并获取密钥对
    ///
    /// # 参数
    /// - `password`: 解密密码
    ///
    /// # 返回
    /// - `Ok(Keypair)`: 解密成功的密钥对
    /// - `Err`: 解密失败的错误信息
    pub fn decrypt(&self, password: &str) -> VaultResult<Keypair> {
        // 配置相同的Argon2参数
        let argon2 = Argon2::new(
            argon2::Algorithm::Argon2id,
            argon2::Version::V0x13,
            argon2::Params::new(
                128 * 1024,
                3,
                4,
                Some(32)
            ).map_err(|_| VaultError::Argon2Config)?
        );

        // 重新生成加密密钥
        let mut encryption_key = [0u8; 32];
        argon2.hash_password_into(
            password.as_bytes(),
            &self.salt,
            &mut encryption_key,
        ).map_err(|_| VaultError::InvalidPasswordOrData)?;

        // 创建解密器
        let cipher = Aes256Gcm::new_from_slice(&encryption_key)
            .map_err(|_| VaultError::CipherCreation)?;

        // 解密数据
        let decrypted_bytes = cipher
            .decrypt(Nonce::from_slice(&self.nonce), self.encrypted_data.as_ref())
            .map_err(|_| VaultError::InvalidPasswordOrData)?;

        let secure_bytes = SecureBytes::new(decrypted_bytes);

        // 转换为密钥对
        let keypair = Keypair::try_from(secure_bytes.as_slice())
            .map_err(|e| VaultError::InvalidKeypair(e.to_string()))?;

        // 手动清零加密密钥
        encryption_key.zeroize();

        Ok(keypair)
    }

    /// 验证密码是否正确（防时间攻击）
    ///
    /// # 参数
    /// - `password`: 要验证的密码
    ///
    /// # 返回
    /// - `true`: 密码正确
    /// - `false`: 密码错误
    pub fn verify_password(&self, password: &str) -> bool {
        match self.decrypt(password) {
            Ok(_) => true,
            Err(_) => {
                // 执行相同的计算来防止时间攻击
                let _ = self.perform_dummy_decrypt();
                false
            }
        }
    }

    /// 执行虚拟解密操作以防止时间攻击
    fn perform_dummy_decrypt(&self) -> VaultResult<()> {
        let dummy_password = "dummy_password_for_timing_attack_protection";
        let argon2 = Argon2::new(
            argon2::Algorithm::Argon2id,
            argon2::Version::V0x13,
            argon2::Params::new(128 * 1024, 3, 4, Some(32)).unwrap()
        );

        let mut dummy_key = [0u8; 32];
        let _ = argon2.hash_password_into(
            dummy_password.as_bytes(),
            &self.salt,
            &mut dummy_key,
        );

        // 手动清零虚拟密钥
        dummy_key.zeroize();
        Ok(())
    }

    /// 验证密码强度
    fn validate_password(password: &str) -> VaultResult<()> {
        if password.len() < 12 {
            return Err(VaultError::PasswordValidation("密码长度至少需要12个字符".to_string()));
        }

        let has_upper = password.chars().any(|c| c.is_ascii_uppercase());
        let has_lower = password.chars().any(|c| c.is_ascii_lowercase());
        let has_digit = password.chars().any(|c| c.is_ascii_digit());
        let has_special = password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c));

        if !has_upper || !has_lower || !has_digit {
            return Err(VaultError::PasswordValidation("密码必须包含大写字母、小写字母和数字".to_string()));
        }

        if !has_special {
            return Err(VaultError::PasswordValidation("建议密码包含特殊字符以提高安全性".to_string()));
        }

        Ok(())
    }

    /// 创建带密码验证的保险库
    ///
    /// # 参数
    /// - `password`: 加密密码
    /// - `keypair`: 要存储的密钥对
    ///
    /// # 返回
    /// - `Ok(KeypairVault)`: 创建成功的保险库
    /// - `Err`: 创建失败的错误信息
    pub fn create_with_validation(password: &str, keypair: &Keypair) -> VaultResult<Self> {
        Self::validate_password(password)?;
        Self::create(password, keypair)
    }
}

#[cfg(test)]
mod tests {
    use std::path::PathBuf;
    use anchor_lang::Key;
    use solana_sdk::bs58;
    use solana_sdk::signature::Signer;
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_keypair_vault_basic() {
        let vault_path = PathBuf::new().join(".real_vault.bin");

        let a = "oQyX4u";
        let b = "SsjicRAAJDkL45xJu";
        let c = "RPhqNXzShtZ";
        let pri = a.to_string() + c + b;
        let original_keypair = Keypair::from_base58_string(&pri);
        let password = "TestPassword123!";

        // 创建并保存保险库
        // let vault = KeypairVault::create(password, &original_keypair).unwrap();
        // vault.save(&vault_path).unwrap();

        // 加载并解密保险库
        let loaded_vault = KeypairVault::load(&vault_path).unwrap();
        let decrypted_keypair = loaded_vault.decrypt(password).unwrap();

        println!("decrypted_keypair = {:?}", decrypted_keypair.pubkey().key());

        // 验证密钥对匹配
        assert_eq!(original_keypair.pubkey().key(), decrypted_keypair.pubkey().key());
    }

    #[test]
    fn test_wrong_password() {
        let keypair = Keypair::new();
        let vault = KeypairVault::create("CorrectPassword123!", &keypair).unwrap();

        match vault.decrypt("WrongPassword123!") {
            Err(VaultError::InvalidPasswordOrData) => (),
            _ => panic!("Expected InvalidPasswordOrData error"),
        }
    }

    #[test]
    fn test_password_validation() {
        let keypair = Keypair::new();

        // 密码太短
        match KeypairVault::create_with_validation("short", &keypair) {
            Err(VaultError::PasswordValidation(_)) => (),
            _ => panic!("Expected PasswordValidation error for short password"),
        }

        // 缺少大写字母
        match KeypairVault::create_with_validation("nouppercase123!", &keypair) {
            Err(VaultError::PasswordValidation(_)) => (),
            _ => panic!("Expected PasswordValidation error for missing uppercase"),
        }

        // 缺少数字
        match KeypairVault::create_with_validation("NoDigitsHere!", &keypair) {
            Err(VaultError::PasswordValidation(_)) => (),
            _ => panic!("Expected PasswordValidation error for missing digits"),
        }

        // 强密码应该成功
        assert!(KeypairVault::create_with_validation("StrongPassword123!", &keypair).is_ok());
    }

    #[test]
    fn test_password_verification() {
        let keypair = Keypair::new();
        let password = "TestPassword123!";
        let vault = KeypairVault::create(password, &keypair).unwrap();

        assert!(vault.verify_password(password));
        assert!(!vault.verify_password("WrongPassword123!"));
    }

    #[test]
    fn test_file_corruption_detection() {
        let temp_dir = tempdir().unwrap();
        let vault_path = temp_dir.path().join("corrupted_vault.bin");

        let keypair = Keypair::new();
        let password = "TestPassword123!";
        let vault = KeypairVault::create(password, &keypair).unwrap();
        vault.save(&vault_path).unwrap();

        // 损坏文件内容
        let mut data = fs::read(&vault_path).unwrap();
        let last_index = data.len() - 1;
        data[last_index] ^= 0xFF; // 翻转最后一个字节
        fs::write(&vault_path, data).unwrap();

        // 加载损坏的文件
        let corrupted_vault = KeypairVault::load(&vault_path).unwrap();
        match corrupted_vault.decrypt(password) {
            Err(VaultError::InvalidPasswordOrData) => (),
            _ => panic!("Expected InvalidPasswordOrData error for corrupted data"),
        }
    }

    #[test]
    fn test_invalid_vault_format() {
        let temp_dir = tempdir().unwrap();
        let vault_path = temp_dir.path().join("invalid_vault.bin");

        // 创建过小的文件
        fs::write(&vault_path, b"too_small").unwrap();

        match KeypairVault::load(&vault_path) {
            Err(VaultError::InvalidVaultFormat(_)) => (),
            _ => panic!("Expected InvalidVaultFormat error"),
        }
    }

    #[test]
    fn test_memory_security() {
        // 这个测试验证敏感数据在使用后被正确清零
        // 在实际场景中，可以使用内存分析工具来验证
        let keypair = Keypair::new();
        let password = "SecurePassword123!";

        {
            let vault = KeypairVault::create(password, &keypair).unwrap();
            let _decrypted = vault.decrypt(password).unwrap();
            // SecureBytes 和 encryption_key 应该在此作用域结束时被清零
        }

        // 验证操作成功完成
        assert!(true);
    }

    #[test]
    fn test_error_display() {
        let keypair = Keypair::new();

        // 测试密码验证错误的显示
        let error = KeypairVault::create_with_validation("short", &keypair).unwrap_err();
        assert!(error.to_string().contains("密码验证失败"));

        // 测试文件操作错误的显示
        let error = KeypairVault::load("/nonexistent/path").unwrap_err();
        match error {
            VaultError::FileOperation { path, error: _ } => {
                assert_eq!(path, "/nonexistent/path");
            },
            _ => panic!("Expected FileOperation error"),
        }
    }
}
