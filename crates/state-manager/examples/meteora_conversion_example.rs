//! MeteoraLbPairState 转换示例
//! 演示如何从 shared::anchor_types::meteora::LbPair 转换为 MeteoraLbPairState

use shared::anchor_types::meteora::{
    LbPair, ProtocolFee, RewardInfo, StaticParameters, VariableParameters,
};
use solana_sdk::pubkey::Pubkey;
use state_manager::dex::meteora::dlmm::pool::MeteoraLbPairState;

fn main() {
    println!("Meteora LbPair 到 MeteoraLbPairState 转换示例");
    println!("================================================");

    // 创建示例 LbPair 数据
    let lb_pair = create_sample_lb_pair();

    // 方法 1: 使用 From trait 进行基本转换
    println!("\n1. 基本转换 (使用 From trait):");
    let meteora_state: MeteoraLbPairState = lb_pair.clone().into();
    print_basic_info(&meteora_state);

    // 方法 2: 使用 from_lb_pair_with_info 方法提供完整信息
    println!("\n2. 完整信息转换 (使用 from_lb_pair_with_info):");
    let pool_address = solana_sdk::pubkey!("5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1");
    let meteora_state_full = MeteoraLbPairState::from_lb_pair_with_info(
        lb_pair.clone(),
        pool_address,
        9, // SOL 有 9 位小数
        6, // USDC 有 6 位小数
    );
    print_full_info(&meteora_state_full);

    // 方法 3: 从引用转换
    println!("\n3. 引用转换 (使用 From<&LbPair>):");
    let meteora_state_ref: MeteoraLbPairState = (&lb_pair).into();
    print_basic_info(&meteora_state_ref);

    // 方法 4: 从引用转换包含完整信息
    println!("\n4. 引用完整信息转换:");
    let meteora_state_ref_full =
        MeteoraLbPairState::from_lb_pair_ref_with_info(&lb_pair, pool_address, 9, 6);
    print_full_info(&meteora_state_ref_full);
}

fn create_sample_lb_pair() -> LbPair {
    LbPair {
        parameters: StaticParameters {
            base_factor: 100,
            filter_period: 30,
            decay_period: 600,
            reduction_factor: 5000,
            variable_fee_control: 120000,
            max_volatility_accumulator: 350000,
            min_bin_id: -443636,
            max_bin_id: 443636,
            protocol_share: 1000,
            base_fee_power_factor: 0,
            padding: [0; 5],
        },
        v_parameters: VariableParameters {
            volatility_accumulator: 6921,
            volatility_reference: 6921,
            index_reference: -4354,
            padding: [0; 4],
            last_update_timestamp: 1754643303,
            padding1: [0; 8],
        },
        bump_seed: [1],
        bin_step_seed: [0, 25],
        pair_type: 1,
        active_id: 8388608,
        bin_step: 25,
        status: 1,
        require_base_factor_seed: 0,
        base_factor_seed: [0, 100],
        activation_type: 0,
        creator_pool_on_off_control: 0,
        token_x_mint: solana_sdk::pubkey!("So11111111111111111111111111111111111111112"), // SOL
        token_y_mint: solana_sdk::pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), // USDC
        reserve_x: Pubkey::default(),
        reserve_y: Pubkey::default(),
        padding1: [0; 32],
        protocol_fee: ProtocolFee {
            amount_x: 150000, // 0.15 SOL
            amount_y: 250000, // 0.25 USDC
        },
        reward_infos: [
            RewardInfo {
                mint: Pubkey::default(),
                vault: Pubkey::default(),
                funder: Pubkey::default(),
                reward_duration: 0,
                reward_duration_end: 0,
                reward_rate: 0,
                last_update_time: 0,
                cumulative_seconds_with_empty_liquidity_reward: 0,
            },
            RewardInfo {
                mint: Pubkey::default(),
                vault: Pubkey::default(),
                funder: Pubkey::default(),
                reward_duration: 0,
                reward_duration_end: 0,
                reward_rate: 0,
                last_update_time: 0,
                cumulative_seconds_with_empty_liquidity_reward: 0,
            },
        ],
        oracle: Pubkey::default(),
        bin_array_bitmap: [0; 16],
        last_updated_at: 1754643303,
        padding2: [0; 32],
        pre_activation_swap_address: Pubkey::default(),
        base_key: Pubkey::default(),
        activation_point: 0,
        pre_activation_duration: 0,
        padding3: [0; 8],
        padding4: 0,
        creator: Pubkey::default(),
        token_mint_x_program_flag: 0,
        token_mint_y_program_flag: 0,
        reserved: [0; 22],
    }
}

fn print_basic_info(state: &MeteoraLbPairState) {
    println!("  池地址: {}", state.address);
    println!("  Token X: {}", state.token_x_mint);
    println!("  Token Y: {}", state.token_y_mint);
    println!("  活跃 Bin ID: {}", state.active_id);
    println!("  Bin 步长: {}", state.bin_step);
    println!("  基础因子: {}", state.base_factor);
    println!("  协议份额: {}%", state.protocol_share as f64 / 100.0);
    println!("  波动性累加器: {}", state.volatility_accumulator);
}

fn print_full_info(state: &MeteoraLbPairState) {
    print_basic_info(state);
    println!("  Token X 小数位: {}", state.token_x_decimals);
    println!("  Token Y 小数位: {}", state.token_y_decimals);
    println!("  协议费用 X: {}", state.protocol_fee_x);
    println!("  协议费用 Y: {}", state.protocol_fee_y);
    println!("  奖励信息数量: {}", state.reward_infos.len());
}
