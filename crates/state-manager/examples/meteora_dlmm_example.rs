//! Meteora DLMM 使用示例
//!
//! 演示如何使用 Meteora DLMM 状态管理和报价计算功能

use solana_sdk::pubkey::Pubkey;
use state_manager::{ MeteoraLbPairState, MeteoraLbPoolManager, utils};
use std::fs;
use std::str::FromStr;
use state_manager::dlmm::{MeteoraLbBinArray, MeteoraLbPoolCache};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Meteora DLMM 示例 ===\n");

    // 1. 创建示例池状态
    let pool_address = Pubkey::from_str("5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6")?;
    let token_x_mint = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?; // USDC
    let token_y_mint = Pubkey::from_str("So11111111111111111111111111111111111111112")?; // SOL

    println!("1. 创建 Meteora DLMM 池状态");
    let mut pool_state = MeteoraLbPairState::new(
        pool_address,
        token_x_mint,
        token_y_mint,
        6,     // USDC decimals
        9,     // SOL decimals
        -4353, // active_id (在我们加载的 bin array 范围内，对应有 X 和 Y 流动性的 bin)
        4,     // bin_step (0.25%)
    );

    // 设置费率
    pool_state.max_fee_rate = 100; // 1%

    println!("   池地址: {}", pool_address);
    println!("   Token X (USDC): {}", token_x_mint);
    println!("   Token Y (SOL): {}", token_y_mint);
    println!("   活跃 Bin ID: {}", pool_state.active_id);
    println!("   Bin 步长: {}", pool_state.bin_step);

    // 2. 创建示例 bin 数据
    println!("2. 添加流动性 Bin 数据");
    // 创建围绕活跃价格的多个bin
    let tick_state = fs::read_to_string(
        "/Users/<USER>/code/rust_project/echoes/crates/state-manager/src/data/meteora/dlmm/meteora-bin-array.json",
    )?;
    let tick_array_json: serde_json::Value = serde_json::from_str(&tick_state)?;
    let array_state = MeteoraLbBinArray::from_json_value(&tick_array_json)?;
    pool_state.update_bin_array(-63, array_state);

    println!(
        "   总流动性: {}\n",
        utils::NumberFormatter::format_liquidity(pool_state.get_total_liquidity())
    );

    // 3. 创建池管理器
    println!("3. 创建池管理器");
    let manager = MeteoraLbPoolManager::new(pool_state);

    let current_price = manager.get_current_price()?;
    println!("   当前价格: {:.6} SOL/USDC", current_price);

    let liquidity_info = manager.get_liquidity_info();
    println!("   活跃 Bin 数量: {}", liquidity_info.active_ranges);
    println!(
        "   价格范围: {:.6} - {:.6}",
        liquidity_info.price_range.0, liquidity_info.price_range.1
    );

    // 4. 交换估算示例
    println!("\n4. 交换估算示例");

    // USDC -> SOL 交换（使用较小的输入量以便在一个 bin 内完成）
    let usdc_input = 10_000_000_000; // 100 USDC (6 decimals)
    println!("   输入: {} USDC", usdc_input as f64 / 1_000_000.0);

    match manager.estimate_swap_output(usdc_input, false, Some(5.0)) {
        Ok(estimation) => {
            println!(
                "   预期输出: {} SOL",
                estimation.output_amount as f64 / 1_000_000_000.0
            );
            println!(
                "   费用: {} USDC",
                estimation.fee_amount as f64 / 1_000_000.0
            );
            println!("   价格影响: {:.2}%", estimation.price_impact);

            // 检查是否是 Meteora 特有的 SwapEstimation 类型
            if let Ok(meteora_estimation) = serde_json::to_string(&estimation).and_then(|s| {
                serde_json::from_str::<state_manager::dex::meteora::dlmm::types::SwapEstimation>(&s)
            }) {
                println!("   使用的 Bin 数量: {}", meteora_estimation.bins_used.len());

                if meteora_estimation.remaining_input > 0 {
                    println!(
                        "   未处理输入: {} USDC",
                        meteora_estimation.remaining_input as f64 / 1_000_000.0
                    );
                }

                // 显示详细的bin使用情况
                println!("   Bin 使用详情:");
                for bin_usage in &meteora_estimation.bins_used {
                    println!(
                        "     Bin {}: 输入 {:.2} USDC -> 输出 {:.6} SOL (价格: {:.6})",
                        bin_usage.bin_id,
                        bin_usage.input_amount as f64 / 1_000_000.0,
                        bin_usage.output_amount as f64 / 1_000_000_000.0,
                        utils::q64::to_float(bin_usage.price)
                    );
                }
            } else {
                println!("   交换估算详细信息不可用");
            }
        }
        Err(e) => {
            println!("   交换估算失败: {}", e);
        }
    }

    // SOL -> USDC 交换（使用较小的输入量）
    println!("\n   反向交换示例:");
    let sol_input = 1_000_000_000; // 1 SOL (9 decimals)
    println!("   输入: {} SOL", sol_input as f64 / 1_000_000_000.0);

    match manager.estimate_swap_output(sol_input, true, Some(5.0)) {
        Ok(estimation) => {
            println!(
                "   预期输出: {} USDC",
                estimation.output_amount as f64 / 1_000_000.0
            );
            println!(
                "   费用: {} SOL",
                estimation.fee_amount as f64 / 1_000_000_000.0
            );
            println!("   价格影响: {:.2}%", estimation.price_impact);

            if let Ok(meteora_estimation) = serde_json::to_string(&estimation).and_then(|s| {
                serde_json::from_str::<state_manager::dex::meteora::dlmm::types::SwapEstimation>(&s)
            }) {
                println!("   使用的 Bin 数量: {}", meteora_estimation.bins_used.len());
            }
        }
        Err(e) => {
            println!("   交换估算失败: {}", e);
        }
    }

    // 5. 缓存管理示例
    println!("\n5. 缓存管理示例");
    let cache = MeteoraLbPoolCache::new();

    // 添加池到缓存
    cache.upsert_pool(pool_address, manager)?;
    println!("   已添加池到缓存");

    // 从缓存获取池
    if let Some(cached_manager) = cache.get_pool(&pool_address) {
        let cached_price = cached_manager.get_current_price()?;
        println!("   从缓存获取的价格: {:.6}", cached_price);
    }

    // 根据代币对查找池
    let pools = cache.find_pools_by_tokens(&token_x_mint, &token_y_mint);
    println!("   找到 {} 个匹配的池", pools.len());

    // 缓存统计
    let stats = cache.get_stats();
    println!("   缓存统计:");
    println!("     总池数: {}", stats.total_pools);
    println!("     活跃池数: {}", stats.active_pools);
    println!(
        "     总流动性: {}",
        utils::NumberFormatter::format_liquidity(stats.total_liquidity)
    );

    // 6. 流动性分布分析
    println!("\n6. 流动性分布分析");
    if let Some(manager) = cache.get_pool(&pool_address) {
        let distribution = manager.pool_state.get_liquidity_distribution();
        println!("   流动性分布 (前5个bin):");

        for (i, (bin_id, price, liquidity)) in distribution.iter().take(5).enumerate() {
            println!(
                "     {}. Bin {}: 价格 {:.6}, 流动性 {}",
                i + 1,
                bin_id,
                utils::q64::to_float(*price),
                utils::NumberFormatter::format_liquidity(*liquidity)
            );
        }
    }

    println!("\n=== 示例完成 ===");
    Ok(())
}

/// 辅助函数：格式化数字显示
fn format_number(num: f64) -> String {
    if num >= 1_000_000.0 {
        format!("{:.2}M", num / 1_000_000.0)
    } else if num >= 1_000.0 {
        format!("{:.2}K", num / 1_000.0)
    } else {
        format!("{:.2}", num)
    }
}
