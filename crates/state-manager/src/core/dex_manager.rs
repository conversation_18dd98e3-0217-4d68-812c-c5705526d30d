//! 统一池管理器
//!
//! 提供跨 DEX 的统一池管理接口，专注于核心功能：
//! - 统一的池管理接口
//! - 基本查询和交换估算
//! - 良好的扩展性
//! - 增强的价格查询和套利分析支持

use super::manager::PoolManager;
use super::pool::PoolState;
use super::types::*;
use dashmap::DashMap;
use shared::{
    DexProtocol, PoolType, NormalizedPoolPrice, NormalizedTokenPair,
};
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;

/// 池管理器包装器，使用枚举来存储不同类型的池管理器
///
/// 使用 Box 包装大型结构体以减少枚举的内存占用，提高性能
#[derive(Debug)]
pub enum PoolManagerWrapper {
    /// Raydium CLMM 池管理器
    Raydium(Box<crate::dex::raydium::clmm::RaydiumClmmPoolManager>),
    /// Meteora DLMM 池管理器
    Meteora(Box<crate::dex::meteora::dlmm::MeteoraLbPoolManager>),
    /// Pump Swap 池管理器
    PumpSwap(Box<crate::dex::PumpSwapPoolManager>),
    RaydiumCpmm(Box<crate::dex::raydium::cpmm::RaydiumCpmmPoolManager>),
    MeteoraDamm(Box<crate::dex::meteora::damm::MeteoraDammPoolManager>),
}

impl From<crate::dex::raydium::clmm::RaydiumClmmPoolManager> for PoolManagerWrapper {
    fn from(manager: crate::dex::raydium::clmm::RaydiumClmmPoolManager) -> Self {
        PoolManagerWrapper::Raydium(Box::new(manager))
    }
}

impl From<crate::dex::meteora::dlmm::MeteoraLbPoolManager> for PoolManagerWrapper {
    fn from(manager: crate::dex::meteora::dlmm::MeteoraLbPoolManager) -> Self {
        PoolManagerWrapper::Meteora(Box::new(manager))
    }
}

impl TryFrom<PoolManagerWrapper> for crate::dex::raydium::clmm::RaydiumClmmPoolManager {
    type Error = PoolManagerWrapper;

    fn try_from(wrapper: PoolManagerWrapper) -> Result<Self, Self::Error> {
        match wrapper {
            PoolManagerWrapper::Raydium(manager) => Ok(*manager),
            other => Err(other),
        }
    }
}

impl TryFrom<PoolManagerWrapper> for crate::dex::meteora::dlmm::MeteoraLbPoolManager {
    type Error = PoolManagerWrapper;

    fn try_from(wrapper: PoolManagerWrapper) -> Result<Self, Self::Error> {
        match wrapper {
            PoolManagerWrapper::Meteora(manager) => Ok(*manager),
            other => Err(other),
        }
    }
}

impl From<crate::dex::PumpSwapPoolManager> for PoolManagerWrapper {
    fn from(manager: crate::dex::PumpSwapPoolManager) -> Self {
        PoolManagerWrapper::PumpSwap(Box::new(manager))
    }
}

impl TryFrom<PoolManagerWrapper> for crate::dex::PumpSwapPoolManager {
    type Error = PoolManagerWrapper;

    fn try_from(wrapper: PoolManagerWrapper) -> Result<Self, Self::Error> {
        match wrapper {
            PoolManagerWrapper::PumpSwap(manager) => Ok(*manager),
            other => Err(other),
        }
    }
}

impl From<crate::dex::raydium::cpmm::RaydiumCpmmPoolManager> for PoolManagerWrapper {
    fn from(manager: crate::dex::raydium::cpmm::RaydiumCpmmPoolManager) -> Self {
        PoolManagerWrapper::RaydiumCpmm(Box::new(manager))
    }
}

impl TryFrom<PoolManagerWrapper> for crate::dex::raydium::cpmm::RaydiumCpmmPoolManager {
    type Error = PoolManagerWrapper;

    fn try_from(wrapper: PoolManagerWrapper) -> Result<Self, Self::Error> {
        match wrapper {
            PoolManagerWrapper::RaydiumCpmm(manager) => Ok(*manager),
            other => Err(other),
        }
    }
}

impl From<crate::dex::meteora::damm::MeteoraDammPoolManager> for PoolManagerWrapper {
    fn from(manager: crate::dex::meteora::damm::MeteoraDammPoolManager) -> Self {
        PoolManagerWrapper::MeteoraDamm(Box::new(manager))
    }
}

impl TryFrom<PoolManagerWrapper> for crate::dex::meteora::damm::MeteoraDammPoolManager {
    type Error = PoolManagerWrapper;

    fn try_from(wrapper: PoolManagerWrapper) -> Result<Self, Self::Error> {
        match wrapper {
            PoolManagerWrapper::MeteoraDamm(manager) => Ok(*manager),
            other => Err(other),
        }
    }
}


impl PoolManagerWrapper {
    /// 获取池地址的统一接口
    ///
    /// 这个方法提供了统一的方式来获取任何池管理器的地址，
    /// 避免在调用代码中使用 match 语句
    pub fn pool_address(&self) -> Pubkey {
        match self {
            PoolManagerWrapper::Raydium(manager) => manager.get_state().pool_address(),
            PoolManagerWrapper::Meteora(manager) => manager.get_state().pool_address(),
            PoolManagerWrapper::PumpSwap(manager) => manager.get_state().pool_address(),
            PoolManagerWrapper::RaydiumCpmm(manager) => manager.get_state().pool_address(),
            PoolManagerWrapper::MeteoraDamm(manager) => manager.get_state().pool_address(),
        }
    }

    /// 获取池状态信息的统一接口
    ///
    /// 这个方法提供了统一的方式来获取池状态信息，
    /// 返回 (token_a, token_b, protocol, pool_type)
    pub fn get_pool_info(&self) -> (Pubkey, Pubkey, DexProtocol, PoolType) {
        match self {
            PoolManagerWrapper::Raydium(manager) => {
                let state = manager.get_state();
                let (token_a, token_b, _, _) = state.token_pair();
                let protocol = state.protocol();
                let pool_type = state.pool_type();
                (token_a, token_b, protocol, pool_type)
            }
            PoolManagerWrapper::Meteora(manager) => {
                let state = manager.get_state();
                let (token_a, token_b, _, _) = state.token_pair();
                let protocol = state.protocol();
                let pool_type = state.pool_type();
                (token_a, token_b, protocol, pool_type)
            }
            PoolManagerWrapper::PumpSwap(manager) => {
                let state = manager.get_state();
                let (token_a, token_b, _, _) = state.token_pair();
                let protocol = state.protocol();
                let pool_type = state.pool_type();
                (token_a, token_b, protocol, pool_type)
            }
            PoolManagerWrapper::RaydiumCpmm(manager) => {
                let state = manager.get_state();
                let (token_a, token_b, _, _) = state.token_pair();
                let protocol = state.protocol();
                let pool_type = state.pool_type();
                (token_a, token_b, protocol, pool_type)
            }
            PoolManagerWrapper::MeteoraDamm(manager) => {
                let state = manager.get_state();
                let (token_a, token_b, _, _) = state.token_pair();
                let protocol = state.protocol();
                let pool_type = state.pool_type();
                (token_a, token_b, protocol, pool_type)
            }
        }
    }

    /// 获取池流动性的统一接口
    ///
    /// 这个方法提供了统一的方式来获取池的流动性
    pub fn get_liquidity(&self) -> u128 {
        match self {
            PoolManagerWrapper::Raydium(manager) => manager.get_state().liquidity,
            PoolManagerWrapper::Meteora(manager) => manager.pool_state.get_total_liquidity(),
            PoolManagerWrapper::PumpSwap(manager) => {
                manager.get_state().get_liquidity_info().lp_count as u128
            }
            PoolManagerWrapper::RaydiumCpmm(manager) => {
                manager.get_state().get_liquidity_info().lp_count as u128
            }
            PoolManagerWrapper::MeteoraDamm(manager) => {
                manager.get_state().get_liquidity_info().lp_count as u128
            }
        }
    }

    /// 获取当前池子价格的统一接口
    ///
    /// 这个方法提供了统一的方式来获取任何池管理器的当前价格。
    /// 价格通常表示为 Token1/Token0 的比率，经过小数位调整。
    ///
    /// # 返回值
    /// - `Ok(price)` - 当前价格（Token1/Token0）
    /// - `Err(error)` - 价格计算失败时的错误信息
    ///
    /// # 错误处理
    /// 可能的错误包括：
    /// - 池状态无效
    /// - 价格计算溢出
    /// - 池子未初始化
    ///
    /// # 示例
    /// ```rust,ignore
    /// let price = pool_wrapper.get_current_price()?;
    /// println!("当前价格: {}", price);
    /// ```
    pub fn get_current_price(&self) -> shared::Result<f64> {
        match self {
            PoolManagerWrapper::Raydium(manager) => {
                // 使用 PoolState trait 的 current_price 方法
                manager.get_state().current_price()
            }
            PoolManagerWrapper::Meteora(manager) => {
                // 使用 PoolState trait 的 current_price 方法
                manager.pool_state.current_price()
            }
            PoolManagerWrapper::PumpSwap(manager) => {
                manager.get_state().current_price()
            }
            PoolManagerWrapper::RaydiumCpmm(manager) => {
                manager.get_state().current_price()
            }
            PoolManagerWrapper::MeteoraDamm(manager) => {
                manager.get_state().current_price()
            }
        }
    }

    /// 更新池的代币mint信息的统一接口
    ///
    /// 这个方法提供了统一的方式来更新任何池管理器的代币mint信息
    ///
    /// # 参数
    /// - `mint` - 要更新的代币mint地址
    /// - `info` - 新的代币mint信息
    ///
    /// # 返回值
    /// - `Ok(())` - 更新成功
    /// - `Err(error)` - 更新失败时的错误信息
    pub fn update_token_mint_info(&mut self, mint: Pubkey, info: shared::TokenMintInfo) -> shared::Result<()> {
        match self {
            PoolManagerWrapper::PumpSwap(manager) => {
                manager.pool_state.update_token_mint_info(mint, info);
                Ok(())
            }
            PoolManagerWrapper::RaydiumCpmm(manager) => {
                manager.pool_state.update_token_mint_info(mint, info);
                Ok(())
            }
            PoolManagerWrapper::MeteoraDamm(manager) => {
                manager.pool_state.update_token_mint_info(mint, info);
                Ok(())
            }
            PoolManagerWrapper::Raydium(manager) => {
                // Raydium CLMM 目前不支持这个功能
                Ok(())
            }
            PoolManagerWrapper::Meteora(manager) => {
                // Meteora DLMM 目前不支持这个功能
                Ok(())
            }
        }
    }
}

/// 统一池管理器
/// 提供跨 DEX 的统一池管理接口
#[derive(Debug)]
pub struct DexPoolManager {
    /// 池管理器存储：使用 Pubkey 作为唯一标识符
    /// 使用 DashMap 提供线程安全的并发访问
    pools: DashMap<Pubkey, PoolManagerWrapper>,
}

impl DexPoolManager {
    /// 创建新的统一管理器
    pub fn new() -> Self {
        Self {
            pools: DashMap::new(),
        }
    }

    // 转换函数已删除，现在直接使用统一的 shared::core 类型

    /// 获取池的状态信息
    fn get_pool_state_info(
        &self,
        manager: &PoolManagerWrapper,
    ) -> (Pubkey, Pubkey, DexProtocol, PoolType) {
        manager.get_pool_info()
    }

    /// 统一的添加池管理器函数
    ///
    /// 接受任何可以转换为 PoolManagerWrapper 的池管理器，并将其存储
    /// 这个函数是所有添加池操作的核心实现
    ///
    /// # 示例
    /// ```rust,ignore
    /// let manager = DexPoolManager::new();
    ///
    /// // 直接使用统一接口
    /// manager.add_pool(raydium_manager)?;
    /// manager.add_pool(meteora_manager)?;
    ///
    /// ```
    pub fn add_pool<T>(&self, manager: T) -> shared::Result<()>
    where
        T: Into<PoolManagerWrapper>,
    {
        let wrapper = manager.into();
        let pool_address = wrapper.pool_address();

        self.pools.insert(pool_address, wrapper);
        Ok(())
    }

    /// 统一的移除池管理器函数
    ///
    /// 移除指定地址的池管理器，返回包装器
    /// 这个函数是所有移除池操作的核心实现
    pub fn remove_pool(&self, pool_address: &Pubkey) -> Option<PoolManagerWrapper> {
        self.pools.remove(pool_address).map(|(_, manager)| manager)
    }

    /// 移除并获取特定类型的池管理器
    ///
    /// 提供类型安全的移除操作，如果池存在且类型匹配则返回具体类型
    pub fn remove_pool_typed<T>(&self, pool_address: &Pubkey) -> Option<T>
    where
        T: TryFrom<PoolManagerWrapper>,
    {
        self.remove_pool(pool_address)?.try_into().ok()
    }

    /// 估算交换 - 从所有支持的池中获取报价
    pub fn estimate_swap(
        &self,
        input_token: &Pubkey,
        output_token: &Pubkey,
        input_amount: u64,
        max_price_impact: Option<f64>,
    ) -> shared::Result<Vec<SwapEstimation>> {
        let mut estimations: Vec<SwapEstimation> = Vec::new();

        // 遍历所有池，查找支持该代币对的池
        for pool_entry in self.pools.iter() {
            let manager = pool_entry.value();
            let (token_a, token_b, _, _) = self.get_pool_state_info(manager);

            // 检查是否支持该代币对
            if let Ok(direction) =
                self.determine_swap_direction(*input_token, *output_token, token_a, token_b)
            {
                match manager {
                    PoolManagerWrapper::Raydium(manager) => {
                        let zero_for_one = match direction {
                            SwapDirection::AToB => true,
                            SwapDirection::BToA => false,
                        };

                        if let Ok(swap_result) =
                            manager.estimate_swap_output(input_amount, zero_for_one, None)
                        {
                            let estimation = SwapEstimation {
                                input_amount,
                                output_amount: swap_result.amount_out,
                                minimum_output: None,
                                price_impact: swap_result.price_impact_bps as f64 / 100.0,
                                fee_amount: swap_result.fee_amount,
                                price_after: swap_result.sqrt_price_after as f64
                                    / (1u128 << 64) as f64,
                                direction,
                            };
                            estimations.push(estimation);
                        }
                    }
                    PoolManagerWrapper::Meteora(manager) => {
                        let input_is_x = match direction {
                            SwapDirection::AToB => true,
                            SwapDirection::BToA => false,
                        };

                        if let Ok(estimation) =
                            manager.estimate_swap_output(input_amount, input_is_x, max_price_impact)
                        {
                            estimations.push(estimation);
                        }
                    }
                    PoolManagerWrapper::PumpSwap(manager) => {
                        if let Ok(swap_result) =
                            manager.estimate_swap_output(input_amount, direction, max_price_impact)
                        {
                            let estimation = SwapEstimation {
                                input_amount,
                                output_amount: swap_result.output_amount,
                                minimum_output: swap_result.minimum_output,
                                price_impact: swap_result.price_impact,
                                fee_amount: swap_result.fee_amount,
                                price_after: swap_result.price_after,
                                direction,
                            };
                            estimations.push(estimation);
                        }
                    }
                    PoolManagerWrapper::RaydiumCpmm(manager) => {
                        if let Ok(swap_result) =
                            manager.estimate_swap_output(input_amount, direction, max_price_impact)
                        {
                            let estimation = SwapEstimation {
                                input_amount,
                                output_amount: swap_result.output_amount,
                                minimum_output: swap_result.minimum_output,
                                price_impact: swap_result.price_impact,
                                fee_amount: swap_result.fee_amount,
                                price_after: swap_result.price_after,
                                direction,
                            };
                            estimations.push(estimation);
                        }
                    }
                    PoolManagerWrapper::MeteoraDamm(manager) => {
                        if let Ok(swap_result) =
                            manager.estimate_swap_output(input_amount, direction, max_price_impact)
                        {
                            let estimation = SwapEstimation {
                                input_amount,
                                output_amount: swap_result.output_amount,
                                minimum_output: swap_result.minimum_output,
                                price_impact: swap_result.price_impact,
                                fee_amount: swap_result.fee_amount,
                                price_after: swap_result.price_after,
                                direction,
                            };
                            estimations.push(estimation);
                        }
                    }
                }
            }
        }

        Ok(estimations)
    }

    /// 确定交换方向
    fn determine_swap_direction(
        &self,
        input_token: Pubkey,
        output_token: Pubkey,
        token_a: Pubkey,
        token_b: Pubkey,
    ) -> shared::Result<SwapDirection> {
        if token_a == input_token && token_b == output_token {
            Ok(SwapDirection::AToB)
        } else if token_b == input_token && token_a == output_token {
            Ok(SwapDirection::BToA)
        } else {
            Err(shared::EchoesError::InvalidInput(format!(
                "Token pair mismatch: input={}, output={}, pool_tokens=({}, {})",
                input_token, output_token, token_a, token_b
            )))
        }
    }

    /// 获取最佳交换报价
    pub fn get_best_swap_quote(
        &self,
        input_token: &Pubkey,
        output_token: &Pubkey,
        input_amount: u64,
        max_price_impact: Option<f64>,
    ) -> shared::Result<SwapEstimation> {
        let estimations =
            self.estimate_swap(input_token, output_token, input_amount, max_price_impact)?;

        // 选择输出最多的报价
        estimations
            .into_iter()
            .max_by_key(|est| est.output_amount)
            .ok_or_else(|| {
                shared::EchoesError::Internal(format!(
                    "No pools found for token pair {}/{}",
                    input_token, output_token
                ))
            })
    }

    /// 查找支持指定代币对的所有池
    pub fn find_pools_for_tokens(&self, token_a: &Pubkey, token_b: &Pubkey) -> Vec<PoolId> {
        let mut pool_ids = Vec::new();

        for pool_entry in self.pools.iter() {
            let manager = pool_entry.value();
            let pool_address = *pool_entry.key();
            let (pool_token_a, pool_token_b, protocol, pool_type) =
                self.get_pool_state_info(manager);

            // 检查是否支持该代币对（双向检查）
            if (pool_token_a == *token_a && pool_token_b == *token_b)
                || (pool_token_a == *token_b && pool_token_b == *token_a)
            {
                pool_ids.push(PoolId::new(protocol, pool_type, pool_address));
            }
        }

        pool_ids
    }

    /// 按协议获取池
    pub fn get_pools_by_protocol(&self, protocol: DexProtocol) -> Vec<PoolId> {
        let mut pool_ids = Vec::new();

        for pool_entry in self.pools.iter() {
            let manager = pool_entry.value();
            let pool_address = *pool_entry.key();
            let (_, _, pool_protocol, pool_type) = self.get_pool_state_info(manager);

            if pool_protocol == protocol {
                pool_ids.push(PoolId::new(protocol, pool_type, pool_address));
            }
        }

        pool_ids
    }

    /// 按池类型获取池
    pub fn get_pools_by_type(&self, pool_type: PoolType) -> Vec<PoolId> {
        let mut pool_ids = Vec::new();

        for pool_entry in self.pools.iter() {
            let manager = pool_entry.value();
            let pool_address = *pool_entry.key();
            let (_, _, protocol, manager_pool_type) = self.get_pool_state_info(manager);

            if manager_pool_type == pool_type {
                pool_ids.push(PoolId::new(protocol, pool_type, pool_address));
            }
        }

        pool_ids
    }

    /// 获取流动性最高的池
    pub fn get_top_pools_by_liquidity(&self, limit: usize) -> Vec<(PoolId, u128)> {
        let mut pools_with_liquidity = Vec::new();

        for pool_entry in self.pools.iter() {
            let manager = pool_entry.value();
            let pool_address = *pool_entry.key();
            let (_, _, protocol, pool_type) = self.get_pool_state_info(manager);

            // 根据池类型获取流动性
            let liquidity = manager.get_liquidity();

            pools_with_liquidity.push((PoolId::new(protocol, pool_type, pool_address), liquidity));
        }

        // 按流动性排序并返回前 N 个
        pools_with_liquidity.sort_by(|a, b| b.1.cmp(&a.1));
        pools_with_liquidity.into_iter().take(limit).collect()
    }

    /// 获取所有池的地址列表
    pub fn get_all_pool_addresses(&self) -> Vec<Pubkey> {
        self.pools.iter().map(|entry| *entry.key()).collect()
    }

    /// 检查池是否存在
    pub fn contains_pool(&self, pool_address: &Pubkey) -> bool {
        self.pools.contains_key(pool_address)
    }

    /// 获取池数量
    pub fn pool_count(&self) -> usize {
        self.pools.len()
    }

    /// 清空所有池
    pub fn clear_all_pools(&self) {
        self.pools.clear();
    }

    /// 获取指定池的当前价格
    ///
    /// 根据池地址获取该池的当前价格信息
    ///
    /// # 参数
    /// * `pool_address` - 池地址
    ///
    /// # 返回值
    /// * `Ok(price)` - 当前价格（Token1/Token0）
    /// * `Err(error)` - 池不存在或价格获取失败
    ///
    /// # 示例
    /// ```rust,ignore
    /// let price = manager.get_pool_price(&pool_address)?;
    /// println!("池 {} 的当前价格: {}", pool_address, price);
    /// ```
    pub fn get_pool_price(&self, pool_address: &Pubkey) -> shared::Result<f64> {
        if let Some(manager) = self.pools.get(pool_address) {
            manager.get_current_price()
        } else {
            Err(shared::EchoesError::InvalidInput(format!(
                "Pool with address {} not found",
                pool_address
            )))
        }
    }

    /// 获取所有池的价格信息
    ///
    /// 返回一个包含所有池地址和对应价格的向量
    ///
    /// # 返回值
    /// 返回 `(pool_address, price)` 的向量，跳过价格获取失败的池
    ///
    /// # 示例
    /// ```rust,ignore
    /// let prices = manager.get_all_pool_prices();
    /// for (address, price) in prices {
    ///     println!("池 {}: 价格 {}", address, price);
    /// }
    /// ```
    pub fn get_all_pool_prices(&self) -> Vec<(Pubkey, f64)> {
        let mut prices = Vec::new();

        for pool_entry in self.pools.iter() {
            let pool_address = *pool_entry.key();
            let manager = pool_entry.value();

            if let Ok(price) = manager.get_current_price() {
                prices.push((pool_address, price));
            }
            // 静默跳过获取价格失败的池，避免因单个池的问题影响整体查询
        }

        prices
    }

    /// 获取所有池的标准化价格信息
    ///
    /// 返回包含完整价格和代币对信息的标准化数据结构，用于套利分析
    ///
    /// # 返回值
    /// 返回 `NormalizedPoolPrice` 的向量，跳过价格获取失败的池
    pub fn get_all_normalized_pool_prices(&self) -> Vec<NormalizedPoolPrice> {
        let mut normalized_prices = Vec::new();

        for pool_entry in self.pools.iter() {
            let pool_address = *pool_entry.key();
            let manager = pool_entry.value();

            // 获取池的基本信息
            let (token_a, token_b, protocol, pool_type) = manager.get_pool_info();
            let liquidity = manager.get_liquidity();

            // 获取价格
            if let Ok(price) = manager.get_current_price() {
                let normalized_price = NormalizedPoolPrice::new(
                    pool_address,
                    token_a,
                    token_b,
                    price,
                    liquidity,
                    protocol,
                    pool_type,
                );
                normalized_prices.push(normalized_price);
            }
        }

        normalized_prices
    }

    /// 获取指定代币对的所有池价格信息
    ///
    /// 用于查找同一代币对在不同池子中的价格，便于套利分析
    ///
    /// # 参数
    /// * `token_a` - 代币A地址
    /// * `token_b` - 代币B地址
    ///
    /// # 返回值
    /// 返回包含该代币对的所有池的标准化价格信息
    pub fn get_normalized_prices_for_token_pair(
        &self,
        token_a: &Pubkey,
        token_b: &Pubkey,
    ) -> Vec<NormalizedPoolPrice> {
        let target_pair = NormalizedTokenPair::new(*token_a, *token_b);
        let mut matching_prices = Vec::new();

        for pool_entry in self.pools.iter() {
            let pool_address = *pool_entry.key();
            let manager = pool_entry.value();

            let (pool_token_a, pool_token_b, protocol, pool_type) = manager.get_pool_info();
            let pool_pair = NormalizedTokenPair::new(pool_token_a, pool_token_b);

            // 检查是否是目标代币对
            if pool_pair == target_pair {
                let liquidity = manager.get_liquidity();

                if let Ok(price) = manager.get_current_price() {
                    let normalized_price = NormalizedPoolPrice::new(
                        pool_address,
                        pool_token_a,
                        pool_token_b,
                        price,
                        liquidity,
                        protocol,
                        pool_type,
                    );
                    matching_prices.push(normalized_price);
                }
            }
        }

        matching_prices
    }

    /// 按代币对分组池价格信息
    ///
    /// 将所有池按代币对分组，便于批量套利分析
    ///
    /// # 返回值
    /// 返回按 NormalizedTokenPair 分组的池价格信息映射
    pub fn get_pools_grouped_by_token_pair(
        &self,
    ) -> HashMap<NormalizedTokenPair, Vec<NormalizedPoolPrice>> {
        let mut grouped = HashMap::new();

        for pool_entry in self.pools.iter() {
            let pool_address = *pool_entry.key();
            let manager = pool_entry.value();

            let (token_a, token_b, protocol, pool_type) = manager.get_pool_info();
            let token_pair = NormalizedTokenPair::new(token_a, token_b);
            let liquidity = manager.get_liquidity();

            if let Ok(price) = manager.get_current_price() {
                let normalized_price = NormalizedPoolPrice::new(
                    pool_address,
                    token_a,
                    token_b,
                    price,
                    liquidity,
                    protocol,
                    pool_type,
                );

                grouped
                    .entry(token_pair)
                    .or_insert_with(Vec::new)
                    .push(normalized_price);
            }
        }

        grouped
    }

    /// 获取包含指定代币的所有池
    ///
    /// 用于查找某个代币参与的所有流动性池
    ///
    /// # 参数
    /// * `token` - 代币地址
    ///
    /// # 返回值
    /// 返回包含该代币的所有池的标准化价格信息
    pub fn get_pools_containing_token(&self, token: &Pubkey) -> Vec<NormalizedPoolPrice> {
        let mut matching_pools = Vec::new();

        for pool_entry in self.pools.iter() {
            let pool_address = *pool_entry.key();
            let manager = pool_entry.value();

            let (token_a, token_b, protocol, pool_type) = manager.get_pool_info();

            // 检查是否包含目标代币
            if token_a == *token || token_b == *token {
                let liquidity = manager.get_liquidity();

                if let Ok(price) = manager.get_current_price() {
                    let normalized_price = NormalizedPoolPrice::new(
                        pool_address,
                        token_a,
                        token_b,
                        price,
                        liquidity,
                        protocol,
                        pool_type,
                    );
                    matching_pools.push(normalized_price);
                }
            }
        }

        matching_pools
    }

    /// 获取指定池子的代币对信息
    ///
    /// 用于快速查询池子的代币对，避免重复查询
    ///
    /// # 参数
    /// * `pool_address` - 池地址
    ///
    /// # 返回值
    /// * `Ok((token_a, token_b))` - 代币对
    /// * `Err(error)` - 池不存在
    pub fn get_pool_tokens(&self, pool_address: &Pubkey) -> shared::Result<(Pubkey, Pubkey)> {
        if let Some(manager) = self.pools.get(pool_address) {
            let (token_a, token_b, _, _) = manager.get_pool_info();
            Ok((token_a, token_b))
        } else {
            Err(shared::EchoesError::InvalidInput(format!(
                "Pool with address {} not found",
                pool_address
            )))
        }
    }

    /// 获取指定池子的标准化价格信息
    ///
    /// 获取单个池子的完整标准化价格信息
    ///
    /// # 参数
    /// * `pool_address` - 池地址
    ///
    /// # 返回值
    /// * `Ok(NormalizedPoolPrice)` - 标准化价格信息
    /// * `Err(error)` - 池不存在或价格获取失败
    pub fn get_normalized_pool_price(
        &self,
        pool_address: &Pubkey,
    ) -> shared::Result<NormalizedPoolPrice> {
        if let Some(manager) = self.pools.get(pool_address) {
            let (token_a, token_b, protocol, pool_type) = manager.get_pool_info();
            let liquidity = manager.get_liquidity();
            let price = manager.get_current_price()?;

            let normalized_price = NormalizedPoolPrice::new(
                *pool_address,
                token_a,
                token_b,
                price,
                liquidity,
                protocol,
                pool_type,
            );

            Ok(normalized_price)
        } else {
            Err(shared::EchoesError::InvalidInput(format!(
                "Pool with address {} not found",
                pool_address
            )))
        }
    }

    /// 批量获取指定池子的标准化价格信息
    ///
    /// 用于高效获取多个池子的价格信息，跳过获取失败的池子
    ///
    /// # 参数
    /// * `pool_addresses` - 池地址列表
    ///
    /// # 返回值
    /// 返回成功获取价格信息的池子的标准化价格信息列表
    pub fn get_normalized_pool_prices_batch(
        &self,
        pool_addresses: &[Pubkey],
    ) -> Vec<NormalizedPoolPrice> {
        pool_addresses
            .iter()
            .filter_map(|addr| self.get_normalized_pool_price(addr).ok())
            .collect()
    }

    /// 更新现有池管理器的状态
    ///
    /// 提供更新现有池状态的功能，避免重复创建池管理器实例
    /// 这是解决数据丢失问题的核心方法
    ///
    /// # 参数
    /// * `manager` - 新的池管理器实例
    ///
    /// # 返回值
    /// * `Ok(true)` - 成功更新现有池
    /// * `Ok(false)` - 池不存在，添加为新池
    /// * `Err(error)` - 更新失败
    pub fn update_pool<T>(&self, manager: T) -> shared::Result<bool>
    where
        T: Into<PoolManagerWrapper>,
    {
        let wrapper = manager.into();
        let pool_address = wrapper.pool_address();

        // 检查池是否已存在
        let exists = self.pools.contains_key(&pool_address);

        // 无论是否存在都进行更新（DashMap的insert会覆盖现有值）
        self.pools.insert(pool_address, wrapper);

        Ok(exists)
    }

    /// 增量更新池状态（如果存在的话）
    ///
    /// 只有当池已存在时才进行更新，否则忽略
    /// 用于确保只更新已知池的状态
    ///
    /// # 参数
    /// * `manager` - 新的池管理器实例
    ///
    /// # 返回值
    /// * `Ok(true)` - 成功更新现有池
    /// * `Ok(false)` - 池不存在，未进行任何操作
    /// * `Err(error)` - 更新失败
    pub fn update_existing_pool<T>(&self, manager: T) -> shared::Result<bool>
    where
        T: Into<PoolManagerWrapper>,
    {
        let wrapper = manager.into();
        let pool_address = wrapper.pool_address();

        // 只有池已存在时才进行更新
        if self.pools.contains_key(&pool_address) {
            self.pools.insert(pool_address, wrapper);
            Ok(true)
        } else {
            Ok(false)
        }
    }


    /// 更新池的代币mint信息
    ///
    /// # 参数
    /// - `pool_address` - 池地址（字符串格式）
    /// - `token_mint_info` - 代币mint信息
    ///
    /// # 返回值
    /// - `Ok(())` - 更新成功
    /// - `Err(error)` - 更新失败时的错误信息
    pub fn update_pool_token_mint_balance(&self, pool_address: String, token_mint_info: shared::TokenMintInfo) -> shared::Result<()> {
        // 尝试解析池地址字符串为 Pubkey
        let pool_pubkey = pool_address.parse::<Pubkey>()
            .map_err(|e| shared::EchoesError::InvalidInput(format!("Invalid pool address {}: {}", pool_address, e)))?;

        if let Some(mut manager_ref) = self.pools.get_mut(&pool_pubkey) {
            let manager = manager_ref.value_mut();
            manager.update_token_mint_info(token_mint_info.mint, token_mint_info)
        } else {
            Err(shared::EchoesError::InvalidInput(format!(
                "Pool with address {} not found",
                pool_address
            )))
        }
    }

    /// 增量更新Raydium CLMM池状态
    pub fn update_raydium_pool_state(&self, pool_address: Pubkey, new_state: crate::dex::raydium::clmm::pool::RaydiumClmmPoolState) -> shared::Result<bool> {
        if let Some(mut manager_ref) = self.pools.get_mut(&pool_address) {
            if let PoolManagerWrapper::Raydium(raydium_manager) = manager_ref.value_mut() {
                raydium_manager.update_pool_state(new_state);
                Ok(true)
            } else {
                Err(shared::EchoesError::InvalidInput("Pool is not a Raydium CLMM pool".to_string()))
            }
        } else {
            Ok(false)
        }
    }

    /// 增量更新Meteora DLMM池状态
    pub fn update_meteora_dlmm_pool_state(&self, pool_address: Pubkey, new_state: crate::dex::meteora::dlmm::MeteoraLbPairState) -> shared::Result<bool> {
        if let Some(mut manager_ref) = self.pools.get_mut(&pool_address) {
            if let PoolManagerWrapper::Meteora(meteora_manager) = manager_ref.value_mut() {
                meteora_manager.pool_state = new_state;
                Ok(true)
            } else {
                Err(shared::EchoesError::InvalidInput("Pool is not a Meteora DLMM pool".to_string()))
            }
        } else {
            Ok(false)
        }
    }

    /// 增量更新PumpSwap池状态（保留现有的token_mint_info）
    pub fn update_pump_swap_pool_state(&self, pool_address: Pubkey, mut new_state: crate::dex::pump::PumpSwapPoolState) -> shared::Result<bool> {
        if let Some(mut manager_ref) = self.pools.get_mut(&pool_address) {
            if let PoolManagerWrapper::PumpSwap(pump_manager) = manager_ref.value_mut() {
                // 保留现有的 token_mint_info
                let existing_token_mint_info = pump_manager.pool_state.token_mint_info.clone();
                new_state.token_mint_info = existing_token_mint_info;

                pump_manager.update_pool_state(new_state)?;
                Ok(true)
            } else {
                Err(shared::EchoesError::InvalidInput("Pool is not a PumpSwap pool".to_string()))
            }
        } else {
            Ok(false)
        }
    }

    /// 增量更新Raydium CPMM池状态（保留现有的token_mint_info）
    pub fn update_raydium_cpmm_pool_state(&self, pool_address: Pubkey, mut new_state: crate::dex::raydium::cpmm::RaydiumCpmmPool) -> shared::Result<bool> {
        if let Some(mut manager_ref) = self.pools.get_mut(&pool_address) {
            if let PoolManagerWrapper::RaydiumCpmm(cpmm_manager) = manager_ref.value_mut() {
                // 保留现有的 token_mint_info
                let existing_token_mint_info = cpmm_manager.pool_state.token_mint_info.clone();
                new_state.token_mint_info = existing_token_mint_info;

                cpmm_manager.update_pool_state(new_state)?;
                Ok(true)
            } else {
                Err(shared::EchoesError::InvalidInput("Pool is not a Raydium CPMM pool".to_string()))
            }
        } else {
            Ok(false)
        }
    }

    /// 增量更新Meteora DAMM池状态（保留现有的token_mint_info）
    pub fn update_meteora_damm_pool_state(&self, pool_address: Pubkey, mut new_state: crate::dex::meteora::damm::MeteoraDammPool) -> shared::Result<bool> {
        if let Some(mut manager_ref) = self.pools.get_mut(&pool_address) {
            if let PoolManagerWrapper::MeteoraDamm(damm_manager) = manager_ref.value_mut() {
                // 保留现有的 token_mint_info
                let existing_token_mint_info = damm_manager.pool_state.token_mint_info.clone();
                new_state.token_mint_info = existing_token_mint_info;

                damm_manager.update_pool_state(new_state)?;
                Ok(true)
            } else {
                Err(shared::EchoesError::InvalidInput("Pool is not a Meteora DAMM pool".to_string()))
            }
        } else {
            Ok(false)
        }
    }
}
