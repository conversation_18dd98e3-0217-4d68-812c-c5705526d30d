//! 池管理器抽象
//!
//! 定义池管理器的通用接口和基础实现

use super::pool::PoolState;
use super::types::*;

/// 池管理器 trait - 基于实际 DEX 实现的通用接口
///
/// 这个 trait 定义了所有池管理器必须提供的核心功能，
/// 基于 Raydium CLMM 和 Meteora DLMM 的实际实现抽象而来
pub trait PoolManager: Send + Sync {
    /// 池状态类型
    type State: PoolState;

    /// 获取池状态引用
    fn get_state(&self) -> &Self::State;

    /// 获取当前价格
    fn get_current_price(&self) -> shared::Result<f64>;

    /// 估算交换输出
    ///
    /// # 参数
    /// * `input_amount` - 输入金额
    /// * `direction` - 交换方向
    /// * `max_price_impact` - 最大价格影响（可选）
    ///
    /// # 返回
    /// 返回交换估算结果
    fn estimate_swap_output(
        &self,
        input_amount: u64,
        direction: SwapDirection,
        max_price_impact: Option<f64>,
    ) -> shared::Result<SwapEstimation>;

    /// 获取流动性信息
    fn get_liquidity_info(&self) -> LiquidityInfo;

    /// 更新池状态（如果支持）
    fn update_pool_state(&mut self, _new_state: Self::State) -> shared::Result<()> {
        // 默认实现：不支持状态更新
        Err(shared::EchoesError::Internal(
            "Pool state update not supported".to_string(),
        ))
    }
}
