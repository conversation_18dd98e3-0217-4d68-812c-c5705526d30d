//! Meteora DAMM V2 池管理器实现
//!
//! 实现 Meteora DAMM V2 特定的池管理逻辑

use super::pool::MeteoraDammPool;
use super::types::*;
use crate::core::{LiquidityInfo as CoreLiquidityInfo, PoolManager, SwapDirection, SwapEstimation as CoreSwapEstimation};
use shared::anchor_types::meteora_damm::Pool as MeteoraDammPoolData;
use shared::{EchoesError, Result};
use solana_sdk::pubkey::Pubkey;

/// Meteora DAMM V2 池管理器
/// 负责管理 Meteora DAMM V2 池的状态和计算交换
#[derive(Debug, Clone)]
pub struct MeteoraDammPoolManager {
    /// 池状态
    pub pool_state: MeteoraDammPool,
}

impl MeteoraDammPoolManager {
    /// 创建新的池管理器
    pub fn new(pool_state: MeteoraDammPool) -> Self {
        Self { pool_state }
    }

    /// 从原始账户数据创建池管理器
    /// 注意：由于 Borsh 序列化的复杂性和依赖问题，此功能暂时禁用
    /// 建议使用 from_account_data 并手动提供必要的字段
    #[allow(dead_code)]
    pub fn from_raw_account_data(
        _address: Pubkey,
        account_data: &[u8],
    ) -> Result<Self> {
        // 验证数据长度
        if account_data.len() < 8 {
            return Err(EchoesError::InvalidInput(
                format!("Account data too short: {} bytes", account_data.len())
            ));
        }

        // 跳过前 8 字节的 discriminator 
        let pool_data_bytes = &account_data[8..];
        
        // 记录实际长度用于调试
        if pool_data_bytes.len() != 1072 {
            return Err(EchoesError::InvalidInput(
                format!(
                    "Unexpected pool data length: {} bytes. Expected 1072 bytes for Meteora DAMM V2 Pool. \
                    This indicates either the account structure has changed or the data is corrupted. \
                    Consider using from_account_data() with explicit field values instead.", 
                    pool_data_bytes.len()
                )
            ));
        }

        // TODO: 实现自定义的 Borsh 反序列化以避免依赖问题
        // 暂时返回错误，建议使用替代方法
        Err(EchoesError::InvalidInput(
            "Raw account data deserialization is temporarily disabled due to dependency conflicts. \
            Please use from_account_data() with explicit parameters or ensure your data contains exactly 1072 bytes after the discriminator.".to_string()
        ))
    }

    /// 从账号数据创建池管理器
    pub fn from_account_data(
        address: Pubkey,
        pool_data: MeteoraDammPoolData,
        token_a_reserve: Option<u64>,
        token_b_reserve: Option<u64>,
        token_a_decimals: Option<u8>,
        token_b_decimals: Option<u8>,
    ) -> Result<Self> {
        let mut pool_state = MeteoraDammPool::from_pool_data(address, pool_data);
        
        // 如果提供了储备数据，则更新
        if let (Some(token_a), Some(token_b)) = (token_a_reserve, token_b_reserve) {
            pool_state.update_reserves(token_a, token_b);
        }

        // 如果提供了精度信息，则更新 token_mint_info
        if let Some(decimals_a) = token_a_decimals {
            if let Some(info) = pool_state.token_mint_info.get_mut(&pool_state.token_a_mint) {
                info.decimals = decimals_a;
            }
        }
        if let Some(decimals_b) = token_b_decimals {
            if let Some(info) = pool_state.token_mint_info.get_mut(&pool_state.token_b_mint) {
                info.decimals = decimals_b;
            }
        }

        // 验证池状态
        pool_state.validate()?;

        Ok(Self::new(pool_state))
    }

    /// 更新储备量
    pub fn update_reserves(&mut self, token_a_reserve: u64, token_b_reserve: u64) {
        self.pool_state.update_reserves(token_a_reserve, token_b_reserve);
    }

    /// 更新 LP 供应量
    pub fn update_lp_supply(&mut self, lp_supply: u64) {
        self.pool_state.update_lp_supply(lp_supply);
    }

    /// 获取 token A 储备
    pub fn get_token_a_reserve(&self) -> u64 {
        self.pool_state.get_token_a_reserve()
    }

    /// 获取 token B 储备
    pub fn get_token_b_reserve(&self) -> u64 {
        self.pool_state.get_token_b_reserve()
    }

    /// 获取费用信息
    pub fn get_fee_info(&self) -> &FeeInfo {
        &self.pool_state.fee_info
    }

    /// 获取曲线信息
    pub fn get_curve_info(&self) -> &CurveInfo {
        &self.pool_state.curve_info
    }

    /// 检查是否为指定代币对的池
    pub fn is_pool_for_pair(&self, token_a: &Pubkey, token_b: &Pubkey) -> bool {
        self.pool_state.matches_token_pair(token_a, token_b)
    }

    /// 获取最佳路由（对于单一池子，路由就是直接交换）
    pub fn get_best_route(
        &self,
        input_mint: &Pubkey,
        output_mint: &Pubkey,
        input_amount: u64,
    ) -> Result<SwapEstimation> {
        // 验证代币对
        if !self.pool_state.matches_token_pair(input_mint, output_mint) {
            return Err(EchoesError::InvalidInput(
                "Token pair not supported by this pool".to_string(),
            ));
        }

        // 确定交换方向
        let token_a_to_token_b = self.pool_state.token_a_mint == *input_mint;
        
        if token_a_to_token_b {
            self.pool_state.estimate_swap_token_a_to_token_b(input_amount)
        } else {
            self.pool_state.estimate_swap_token_b_to_token_a(input_amount)
        }
    }

    /// 获取流动性深度信息
    pub fn get_liquidity_depth(&self, levels: usize) -> Vec<(f64, u64, u64)> {
        // 简化实现：返回当前价格层级
        let mut depth = Vec::new();
        let current_price = self.pool_state.get_current_price().unwrap_or(0.0);
        
        for i in 0..levels {
            let price_offset = (i as f64) * 0.01; // 1% 间隔
            let bid_price = current_price * (1.0 - price_offset);
            let ask_price = current_price * (1.0 + price_offset);
            
            // 简化的流动性估算
            let liquidity = if i == 0 {
                self.pool_state.get_token_a_reserve().min(self.pool_state.get_token_b_reserve())
            } else {
                (self.pool_state.get_token_a_reserve().min(self.pool_state.get_token_b_reserve())) / (i as u64 + 1)
            };
            
            depth.push((bid_price, liquidity, liquidity));
            if bid_price != ask_price {
                depth.push((ask_price, liquidity, liquidity));
            }
        }
        
        depth.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap());
        depth
    }

    /// 检查池的健康状态
    pub fn check_health(&self) -> Result<()> {
        // 检查基本状态
        self.pool_state.validate()?;

        // 检查流动性
        if self.pool_state.get_token_a_reserve() < math_constants::MIN_LIQUIDITY 
            || self.pool_state.get_token_b_reserve() < math_constants::MIN_LIQUIDITY {
            return Err(EchoesError::InvalidState("Insufficient liquidity".to_string()));
        }

        // 检查价格合理性
        let price = self.pool_state.get_current_price()?;
        if !price.is_finite() || price <= 0.0 {
            return Err(EchoesError::InvalidState("Invalid price".to_string()));
        }

        // 检查池状态标志
        if !self.pool_state.can_swap() {
            return Err(EchoesError::InvalidState("Pool swapping is disabled".to_string()));
        }

        // 对于稳定币池，检查额外的稳定性指标
        if self.pool_state.curve_info.is_stable {
            let virtual_price = self.pool_state.calculate_virtual_price()?;
            if virtual_price <= 0.0 || !virtual_price.is_finite() {
                return Err(EchoesError::InvalidState("Invalid virtual price".to_string()));
            }
        }

        Ok(())
    }

    /// 获取池的摘要信息
    pub fn get_pool_summary(&self) -> String {
        format!(
            "Meteora DAMM V2 Pool {} ({}): {}/{} (Price: {:.6}, Token A: {}, Token B: {})",
            self.pool_state.address,
            self.pool_state.get_curve_type_description(),
            self.pool_state.token_a_mint,
            self.pool_state.token_b_mint,
            self.pool_state.get_current_price().unwrap_or(0.0),
            utils::format_amount(self.pool_state.get_token_a_reserve(), self.pool_state.get_token_a_decimals()),
            utils::format_amount(self.pool_state.get_token_b_reserve(), self.pool_state.get_token_b_decimals()),
        )
    }

    /// 估算给定输出金额所需的输入金额
    pub fn estimate_input_for_output(
        &self,
        output_amount: u64,
        token_a_to_token_b: bool,
    ) -> Result<u64> {
        self.pool_state.estimate_input_for_output(output_amount, token_a_to_token_b)
    }

    /// 获取池状态信息
    pub fn get_pool_status(&self) -> (bool, bool) {
        (
            self.pool_state.enabled,
            self.pool_state.can_swap(),
        )
    }

    /// 获取虚拟价格（对于稳定币池）
    pub fn get_virtual_price(&self) -> Result<f64> {
        self.pool_state.calculate_virtual_price()
    }

    /// 计算价格范围内的流动性
    pub fn calculate_liquidity_in_range(&self, price_lower: f64, price_upper: f64) -> u64 {
        let current_price = self.pool_state.get_current_price().unwrap_or(0.0);
        
        // 对于 DAMM，所有流动性都在当前价格点
        if current_price >= price_lower && current_price <= price_upper {
            // 返回以较小储备为基准的流动性
            self.pool_state.get_token_a_reserve().min(self.pool_state.get_token_b_reserve())
        } else {
            0
        }
    }

    /// 模拟大额交换的价格影响
    pub fn simulate_large_swap(&self, input_amount: u64, token_a_to_token_b: bool) -> Result<Vec<(u64, f64, f64)>> {
        let mut results = Vec::new();
        let chunk_size = input_amount / 10; // 分成10份模拟
        let mut cumulative_input = 0u64;
        let mut current_token_a_reserve = self.pool_state.get_token_a_reserve();
        let mut current_token_b_reserve = self.pool_state.get_token_b_reserve();

        for i in 1..=10 {
            let chunk_input = if i == 10 {
                input_amount - cumulative_input // 最后一块包含剩余部分
            } else {
                chunk_size
            };

            cumulative_input += chunk_input;

            // 计算这一块的输出
            let (reserve_in, reserve_out) = if token_a_to_token_b {
                (current_token_a_reserve, current_token_b_reserve)
            } else {
                (current_token_b_reserve, current_token_a_reserve)
            };

            let fee_amount = self.pool_state.fee_info.calculate_fee(chunk_input)?;
            let input_after_fee = chunk_input.saturating_sub(fee_amount);
            
            let output = if self.pool_state.curve_info.is_stable {
                let amp = self.pool_state.curve_info.amp.unwrap_or(0);
                let (multiplier_in, multiplier_out) = if token_a_to_token_b {
                    (self.pool_state.curve_info.token_a_multiplier, self.pool_state.curve_info.token_b_multiplier)
                } else {
                    (self.pool_state.curve_info.token_b_multiplier, self.pool_state.curve_info.token_a_multiplier)
                };
                
                utils::calculate_stable_output(
                    input_after_fee,
                    reserve_in,
                    reserve_out,
                    amp,
                    multiplier_in,
                    multiplier_out,
                )?
            } else {
                utils::calculate_constant_product_output(input_after_fee, reserve_in, reserve_out)?
            };

            // 更新储备
            if token_a_to_token_b {
                current_token_a_reserve += input_after_fee;
                current_token_b_reserve = current_token_b_reserve.saturating_sub(output);
            } else {
                current_token_b_reserve += input_after_fee;
                current_token_a_reserve = current_token_a_reserve.saturating_sub(output);
            }

            // 计算当前价格
            let current_price = utils::calculate_price(
                current_token_a_reserve,
                current_token_b_reserve,
                self.pool_state.get_token_a_decimals(),
                self.pool_state.get_token_b_decimals(),
            );

            // 计算价格影响
            let original_price = self.pool_state.get_current_price()?;
            let price_impact = utils::calculate_price_impact(original_price, current_price);

            results.push((cumulative_input, current_price, price_impact * 100.0));
        }

        Ok(results)
    }

    /// 获取池类型描述
    pub fn get_pool_type_description(&self) -> String {
        let curve_desc = self.pool_state.get_curve_type_description();
        let pool_type_desc = match self.pool_state.pool_type {
            super::pool::PoolType::Permissioned => "Permissioned",
            super::pool::PoolType::Permissionless => "Permissionless",
        };
        
        format!("{} {}", pool_type_desc, curve_desc)
    }

    /// 检查是否为稳定币池
    pub fn is_stable_pool(&self) -> bool {
        self.pool_state.curve_info.is_stable
    }

    /// 获取放大系数（如果是稳定币池）
    pub fn get_amplification(&self) -> Option<u64> {
        self.pool_state.curve_info.amp
    }
}

impl PoolManager for MeteoraDammPoolManager {
    type State = MeteoraDammPool;

    fn get_state(&self) -> &Self::State {
        &self.pool_state
    }

    fn get_current_price(&self) -> Result<f64> {
        self.pool_state.get_current_price()
    }

    fn estimate_swap_output(
        &self,
        input_amount: u64,
        direction: SwapDirection,
        max_price_impact: Option<f64>,
    ) -> Result<CoreSwapEstimation> {
        let estimation = match direction {
            SwapDirection::AToB => {
                // Token A -> Token B
                self.pool_state.estimate_swap_token_a_to_token_b(input_amount)?
            }
            SwapDirection::BToA => {
                // Token B -> Token A
                self.pool_state.estimate_swap_token_b_to_token_a(input_amount)?
            }
        };

        // 检查价格影响限制
        if let Some(max_impact) = max_price_impact {
            if estimation.price_impact > max_impact {
                return Err(EchoesError::InvalidInput(
                    format!("Price impact {:.2}% exceeds maximum {:.2}%", 
                           estimation.price_impact, max_impact)
                ));
            }
        }

        // 计算交换后价格
        let price_after = if direction == SwapDirection::AToB {
            // Token A -> Token B，计算新价格
            let fee_amount = self.pool_state.fee_info.calculate_fee(input_amount)?;
            let input_after_fee = input_amount.saturating_sub(fee_amount);
            let new_token_a_reserve = self.pool_state.get_token_a_reserve() + input_after_fee;
            let new_token_b_reserve = self.pool_state.get_token_b_reserve()
                .checked_sub(estimation.output_amount)
                .unwrap_or(self.pool_state.get_token_b_reserve());
            utils::calculate_price(new_token_a_reserve, new_token_b_reserve, 
                                 self.pool_state.get_token_a_decimals(), self.pool_state.get_token_b_decimals())
        } else {
            // Token B -> Token A，计算新价格
            let fee_amount = self.pool_state.fee_info.calculate_fee(input_amount)?;
            let input_after_fee = input_amount.saturating_sub(fee_amount);
            let new_token_b_reserve = self.pool_state.get_token_b_reserve() + input_after_fee;
            let new_token_a_reserve = self.pool_state.get_token_a_reserve()
                .checked_sub(estimation.output_amount)
                .unwrap_or(self.pool_state.get_token_a_reserve());
            utils::calculate_price(new_token_a_reserve, new_token_b_reserve, 
                                 self.pool_state.get_token_a_decimals(), self.pool_state.get_token_b_decimals())
        };

        // 转换为核心类型
        Ok(CoreSwapEstimation {
            input_amount: estimation.input_amount,
            output_amount: estimation.output_amount,
            minimum_output: Some(estimation.output_amount), // 简化处理
            price_impact: estimation.price_impact,
            fee_amount: estimation.fee_amount,
            price_after,
            direction,
        })
    }

    fn get_liquidity_info(&self) -> CoreLiquidityInfo {
        let current_price = self.pool_state.get_current_price().unwrap_or(0.0);
        
        // 计算总流动性（token A + token B 等价值）
        let token_a_value = self.pool_state.get_token_a_reserve() as u128;
        let token_b_value_in_token_a = if current_price > 0.0 {
            (self.pool_state.get_token_b_reserve() as f64 / current_price) as u128
        } else {
            0
        };
        let total_liquidity = token_a_value + token_b_value_in_token_a;
        
        // 创建流动性分布（简化为当前价格点）
        let distribution = vec![(
            current_price,
            self.pool_state.get_token_a_reserve() as u128,
            self.pool_state.get_token_b_reserve() as u128,
        )];
        
        CoreLiquidityInfo {
            total_liquidity,
            active_ranges: 1, // DAMM V2 只有一个价格点
            price_range: (current_price, current_price),
            distribution,
        }
    }

    fn update_pool_state(&mut self, new_state: Self::State) -> Result<()> {
        // 验证新状态
        new_state.validate()?;
        
        // 更新状态
        self.pool_state = new_state;
        
        Ok(())
    }
}