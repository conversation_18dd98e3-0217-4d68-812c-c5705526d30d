//! Meteora DAMM V2 池状态实现
//!
//! 实现 Meteora DAMM V2 特定的池状态逻辑

use std::collections::HashMap;
use super::types::*;
use crate::core::{PoolState};
use serde::{Deserialize, Serialize};
use shared::anchor_types::meteora_damm::{Pool as MeteoraDammPoolData};
use shared::{EchoesError, Result, TokenMintInfo, DexProtocol, PoolType as CorePoolType};
use solana_sdk::pubkey::Pubkey;

/// 本地 PoolType 定义，包含 serde 支持
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PoolType {
    /// 有权限的
    Permissioned,
    /// 无权限的
    Permissionless,
}

// 移除这个 impl，因为 MeteoraDammPoolType 不再存在
// 在代码中直接使用 u8 类型进行匹配

/// Meteora DAMM V2 池状态
/// 表示一个完整的 Meteora DAMM V2 流动性池
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraDammPool {
    /// 池地址
    pub address: Pubkey,
    /// LP 代币铸币地址
    pub lp_mint: Pubkey,
    /// Token A 铸币地址
    pub token_a_mint: Pubkey,
    /// Token B 铸币地址
    pub token_b_mint: Pubkey,
    /// Token A 金库地址
    pub token_a_vault: Pubkey,
    /// Token B 金库地址
    pub token_b_vault: Pubkey,
    /// LP 代币供应量
    pub lp_supply: u64,
    /// 费用信息
    pub fee_info: FeeInfo,
    /// 曲线信息
    pub curve_info: CurveInfo,
    /// 是否启用
    pub enabled: bool,
    /// 池类型（有权限/无权限）
    pub pool_type: PoolType,
    /// 锁定的 LP 代币数量
    pub total_locked_lp: u64,
    /// 费用最后更新时间戳
    pub fee_last_updated_at: u64,
    /// 最后更新时间戳
    pub last_updated_slot: u64,
    pub token_mint_info: HashMap<Pubkey, TokenMintInfo>
}

impl MeteoraDammPool {
    /// 创建新的 Meteora DAMM V2 池状态
    pub fn new(
        address: Pubkey,
        pool_data: MeteoraDammPoolData,
    ) -> Self {
        // 从新的费用结构中提取费用信息
        let fee_info = FeeInfo::new(
            pool_data.pool_fees.base_fee.cliff_fee_numerator,
            10000u64, // 默认分母
            pool_data.pool_fees.protocol_fee_percent as u64,
            100u64, // 百分比分母
        );

        // 新结构没有直接的 curve_type 字段，默认使用恒定乘积
        let curve_info = CurveInfo::constant_product();

        Self {
            address,
            lp_mint: Pubkey::default(), // 新结构中没有 lp_mint 字段
            token_a_mint: pool_data.token_a_mint,
            token_b_mint: pool_data.token_b_mint,
            token_a_vault: pool_data.token_a_vault,
            token_b_vault: pool_data.token_b_vault,
            lp_supply: 0, // 需要从 LP mint 获取
            fee_info,
            curve_info,
            enabled: pool_data.pool_status == 0, // 0: 启用，1: 禁用
            pool_type: if pool_data.pool_type == 0 { PoolType::Permissioned } else { PoolType::Permissionless },
            total_locked_lp: pool_data.permanent_lock_liquidity as u64,
            fee_last_updated_at: pool_data.activation_point, // 使用激活点作为时间戳
            last_updated_slot: 0,
            token_mint_info: HashMap::new(),
        }
    }

    /// 从池数据创建状态（简化版）
    pub fn from_pool_data(
        address: Pubkey,
        pool_data: MeteoraDammPoolData,
    ) -> Self {
        // 从新的费用结构中提取费用信息
        // 注意：新结构使用了更复杂的费用系统，这里需要适配
        let fee_info = FeeInfo::new(
            pool_data.pool_fees.base_fee.cliff_fee_numerator,
            10000u64, // 默认分母
            pool_data.pool_fees.protocol_fee_percent as u64,
            100u64, // 百分比分母
        );

        // 新结构没有直接的 curve_type 字段，默认使用恒定乘积
        let curve_info = CurveInfo::constant_product();

        Self {
            address,
            lp_mint: Pubkey::default(), // 新结构中没有 lp_mint 字段
            token_a_mint: pool_data.token_a_mint,
            token_b_mint: pool_data.token_b_mint,
            token_a_vault: pool_data.token_a_vault,
            token_b_vault: pool_data.token_b_vault,
            lp_supply: 0, // 需要从 LP mint 获取
            fee_info,
            curve_info,
            enabled: pool_data.pool_status == 0, // 0: 启用，1: 禁用
            pool_type: if pool_data.pool_type == 0 { PoolType::Permissioned } else { PoolType::Permissionless },
            total_locked_lp: pool_data.permanent_lock_liquidity as u64,
            fee_last_updated_at: pool_data.activation_point, // 使用激活点作为时间戳
            last_updated_slot: 0,
            token_mint_info: HashMap::new(),
        }
    }

    /// 更新代币信息
    pub fn update_token_mint_info(&mut self, mint: Pubkey, info: TokenMintInfo) {
        self.token_mint_info.insert(mint, info);
    }

    /// 批量更新代币信息
    pub fn update_token_mint_infos(&mut self, infos: HashMap<Pubkey, TokenMintInfo>) {
        for (mint, info) in infos {
            self.token_mint_info.insert(mint, info);
        }
    }

    /// 获取代币信息
    pub fn get_token_mint_info(&self, mint: &Pubkey) -> Option<&TokenMintInfo> {
        self.token_mint_info.get(mint)
    }

    /// 获取 Token A 信息
    pub fn get_token_a_info(&self) -> Option<&TokenMintInfo> {
        self.token_mint_info.get(&self.token_a_mint)
    }

    /// 获取 Token B 信息
    pub fn get_token_b_info(&self) -> Option<&TokenMintInfo> {
        self.token_mint_info.get(&self.token_b_mint)
    }

    /// 获取 Token A 储备量
    pub fn get_token_a_reserve(&self) -> u64 {
        self.get_token_a_info()
            .map(|info| info.token_balance as u64)
            .unwrap_or(0)
    }

    /// 获取 Token B 储备量
    pub fn get_token_b_reserve(&self) -> u64 {
        self.get_token_b_info()
            .map(|info| info.token_balance as u64)
            .unwrap_or(0)
    }

    /// 获取 Token A 精度
    pub fn get_token_a_decimals(&self) -> u8 {
        self.get_token_a_info()
            .map(|info| info.decimals)
            .unwrap_or(9) // 默认精度
    }

    /// 获取 Token B 精度
    pub fn get_token_b_decimals(&self) -> u8 {
        self.get_token_b_info()
            .map(|info| info.decimals)
            .unwrap_or(9) // 默认精度
    }

    /// 更新储备量（向后兼容）
    pub fn update_reserves(&mut self, token_a_reserve: u64, token_b_reserve: u64) {
        // 更新 token_a 信息
        if let Some(info) = self.token_mint_info.get_mut(&self.token_a_mint) {
            info.token_balance = token_a_reserve as u128;
        } else {
            let info = TokenMintInfo {
                mint: self.token_a_mint,
                decimals: 9,
                token_vault: self.token_a_vault,
                token_balance: token_a_reserve as u128,
            };
            self.token_mint_info.insert(self.token_a_mint, info);
        }

        // 更新 token_b 信息
        if let Some(info) = self.token_mint_info.get_mut(&self.token_b_mint) {
            info.token_balance = token_b_reserve as u128;
        } else {
            let info = TokenMintInfo {
                mint: self.token_b_mint,
                decimals: 9,
                token_vault: self.token_b_vault,
                token_balance: token_b_reserve as u128,
            };
            self.token_mint_info.insert(self.token_b_mint, info);
        }
    }

    /// 更新 LP 供应量
    pub fn update_lp_supply(&mut self, lp_supply: u64) {
        self.lp_supply = lp_supply;
    }

    /// 获取当前价格（token B/token A）
    pub fn get_current_price(&self) -> Result<f64> {
        let token_a_reserve = self.get_token_a_reserve();
        let token_b_reserve = self.get_token_b_reserve();
        let token_a_decimals = self.get_token_a_decimals();
        let token_b_decimals = self.get_token_b_decimals();

        if token_a_reserve == 0 {
            return Err(EchoesError::InvalidState(
                "Token A reserve is zero".to_string(),
            ));
        }

        Ok(utils::calculate_price(
            token_a_reserve,
            token_b_reserve,
            token_a_decimals,
            token_b_decimals,
        ))
    }

    /// 估算交换输出（token A -> token B）
    pub fn estimate_swap_token_a_to_token_b(&self, input_amount: u64) -> Result<SwapEstimation> {
        self.estimate_swap_internal(input_amount, true)
    }

    /// 估算交换输出（token B -> token A）
    pub fn estimate_swap_token_b_to_token_a(&self, input_amount: u64) -> Result<SwapEstimation> {
        self.estimate_swap_internal(input_amount, false)
    }

    /// 内部交换估算逻辑
    fn estimate_swap_internal(&self, input_amount: u64, token_a_to_token_b: bool) -> Result<SwapEstimation> {
        if input_amount == 0 {
            return Err(EchoesError::InvalidInput("Input amount cannot be zero".to_string()));
        }

        if !self.enabled {
            return Err(EchoesError::InvalidState("Pool is disabled".to_string()));
        }

        // 检查流动性
        let token_a_reserve = self.get_token_a_reserve();
        let token_b_reserve = self.get_token_b_reserve();
        if token_a_reserve == 0 || token_b_reserve == 0 {
            return Err(EchoesError::InvalidState("Insufficient liquidity".to_string()));
        }

        // 计算费用
        let fee_amount = self.fee_info.calculate_fee(input_amount)?;
        let input_after_fee = input_amount
            .checked_sub(fee_amount)
            .ok_or_else(|| EchoesError::InvalidInput("Fee exceeds input amount".to_string()))?;

        // 确定储备量
        let (reserve_in, reserve_out) = if token_a_to_token_b {
            (token_a_reserve, token_b_reserve)
        } else {
            (token_b_reserve, token_a_reserve)
        };

        // 根据曲线类型计算输出金额
        let output_amount = if self.curve_info.is_stable {
            let amp = self.curve_info.amp.unwrap_or(0);
            let (multiplier_in, multiplier_out) = if token_a_to_token_b {
                (self.curve_info.token_a_multiplier, self.curve_info.token_b_multiplier)
            } else {
                (self.curve_info.token_b_multiplier, self.curve_info.token_a_multiplier)
            };

            utils::calculate_stable_output(
                input_after_fee,
                reserve_in,
                reserve_out,
                amp,
                multiplier_in,
                multiplier_out,
            )?
        } else {
            utils::calculate_constant_product_output(input_after_fee, reserve_in, reserve_out)?
        };

        // 计算价格影响
        let old_price = self.get_current_price()?;
        let new_reserve_in = reserve_in + input_after_fee;
        let new_reserve_out = reserve_out
            .checked_sub(output_amount)
            .ok_or_else(|| EchoesError::InvalidState("Output exceeds available liquidity".to_string()))?;

        let (new_token_a_reserve, new_token_b_reserve) = if token_a_to_token_b {
            (new_reserve_in, new_reserve_out)
        } else {
            (new_reserve_out, new_reserve_in)
        };

        let new_price = utils::calculate_price(
            new_token_a_reserve,
            new_token_b_reserve,
            self.get_token_a_decimals(),
            self.get_token_b_decimals(),
        );

        let price_impact = utils::calculate_price_impact(old_price, new_price);

        // 检查价格影响是否过大
        if price_impact > math_constants::MAX_SLIPPAGE {
            return Err(EchoesError::InvalidInput("Excessive price impact".to_string()));
        }

        // 计算协议费用
        let protocol_fee_amount = self.fee_info.calculate_protocol_fee(fee_amount)?;

        Ok(SwapEstimation {
            input_amount,
            output_amount,
            fee_amount,
            protocol_fee_amount,
            price_impact: price_impact * 100.0, // 转换为百分比
            slippage: price_impact * 100.0, // 简化处理，实际应该考虑容忍度
        })
    }

    /// 获取流动性信息
    pub fn get_liquidity_info(&self) -> LiquidityInfo {
        let total_liquidity_token_a = self.get_token_a_reserve();
        let total_liquidity_token_b = self.get_token_b_reserve();
        let current_price = self.get_current_price().unwrap_or(0.0);

        LiquidityInfo {
            total_liquidity_token_a,
            total_liquidity_token_b,
            lp_count: 0, // 简化处理，实际需要跟踪 LP 持有者
            current_price,
        }
    }

    /// 获取价格信息
    pub fn get_price_info(&self) -> Result<PriceInfo> {
        let current_price = self.get_current_price()?;

        Ok(PriceInfo {
            current_price,
            price_change: None, // 需要历史数据
            price_change_percent: None, // 需要历史数据
        })
    }

    /// 获取池统计信息
    pub fn get_stats(&self) -> MeteoraDammStats {
        MeteoraDammStats {
            token_a_reserve: self.get_token_a_reserve(),
            token_b_reserve: self.get_token_b_reserve(),
            lp_supply: self.lp_supply,
            volume_24h: 0, // 需要外部数据源
            fees_24h: 0,   // 需要外部数据源
            protocol_fees_token_a: 0, // 需要从协议费用账户获取
            protocol_fees_token_b: 0, // 需要从协议费用账户获取
        }
    }

    /// 检查代币对是否匹配
    pub fn matches_token_pair(&self, token_a: &Pubkey, token_b: &Pubkey) -> bool {
        (self.token_a_mint == *token_a && self.token_b_mint == *token_b)
            || (self.token_a_mint == *token_b && self.token_b_mint == *token_a)
    }

    /// 检查池是否包含指定代币
    pub fn contains_token(&self, token: &Pubkey) -> bool {
        self.token_a_mint == *token || self.token_b_mint == *token
    }

    /// 获取对应的输出代币
    pub fn get_output_mint(&self, input_mint: &Pubkey) -> Option<Pubkey> {
        if self.token_a_mint == *input_mint {
            Some(self.token_b_mint)
        } else if self.token_b_mint == *input_mint {
            Some(self.token_a_mint)
        } else {
            None
        }
    }

    /// 更新最后更新时间戳
    pub fn update_slot(&mut self, slot: u64) {
        self.last_updated_slot = slot;
    }

    /// 验证池状态
    pub fn validate(&self) -> Result<()> {
        if self.token_a_mint == Pubkey::default() || self.token_b_mint == Pubkey::default() {
            return Err(EchoesError::InvalidState("Invalid mint addresses".to_string()));
        }

        if self.token_a_mint == self.token_b_mint {
            return Err(EchoesError::InvalidState("Token A and token B mints cannot be the same".to_string()));
        }

        if self.fee_info.trade_fee_denominator == 0 {
            return Err(EchoesError::InvalidState("Fee denominator cannot be zero".to_string()));
        }

        if self.curve_info.is_stable {
            if let Some(amp) = self.curve_info.amp {
                if amp > math_constants::MAX_AMP {
                    return Err(EchoesError::InvalidState("Amplification factor too high".to_string()));
                }
            }
        }

        Ok(())
    }

    /// 检查池是否可以进行交换
    pub fn can_swap(&self) -> bool {
        self.enabled && self.get_token_a_reserve() > 0 && self.get_token_b_reserve() > 0
    }

    /// 估算给定输出金额所需的输入金额
    pub fn estimate_input_for_output(
        &self,
        output_amount: u64,
        token_a_to_token_b: bool,
    ) -> Result<u64> {
        let (reserve_in, reserve_out) = if token_a_to_token_b {
            (self.get_token_a_reserve(), self.get_token_b_reserve())
        } else {
            (self.get_token_b_reserve(), self.get_token_a_reserve())
        };

        let input_without_fee = if self.curve_info.is_stable {
            // 对于稳定币曲线，使用反向计算（简化版）
            utils::calculate_input_for_output(output_amount, reserve_in, reserve_out)?
        } else {
            utils::calculate_input_for_output(output_amount, reserve_in, reserve_out)?
        };

        // 考虑费用：input_with_fee = input_without_fee / (1 - fee_rate)
        let fee_rate = self.fee_info.trade_fee_rate();
        let input_with_fee = if fee_rate > 0.0 && fee_rate < 1.0 {
            (input_without_fee as f64 / (1.0 - fee_rate)) as u64
        } else {
            input_without_fee
        };

        Ok(input_with_fee)
    }

    /// 获取曲线类型描述
    pub fn get_curve_type_description(&self) -> String {
        if self.curve_info.is_stable {
            format!("Stable (amp: {})", self.curve_info.amp.unwrap_or(0))
        } else {
            "Constant Product".to_string()
        }
    }

    /// 计算虚拟价格（对于稳定币池）
    pub fn calculate_virtual_price(&self) -> Result<f64> {
        if !self.curve_info.is_stable {
            return Ok(1.0); // 恒定乘积池的虚拟价格始终为1
        }

        if self.lp_supply == 0 {
            return Ok(0.0);
        }

        // 简化的虚拟价格计算
        // virtual_price = (reserve_a + reserve_b) / lp_supply
        let total_reserves = self.get_token_a_reserve() + self.get_token_b_reserve();
        let virtual_price = total_reserves as f64 / self.lp_supply as f64;

        Ok(virtual_price)
    }
}

impl PoolState for MeteoraDammPool {
    fn pool_address(&self) -> Pubkey {
        self.address
    }

    fn token_pair(&self) -> (Pubkey, Pubkey, u8, u8) {
        (self.token_a_mint, self.token_b_mint, self.get_token_a_decimals(), self.get_token_b_decimals())
    }

    fn current_price(&self) -> Result<f64> {
        self.get_current_price()
    }

    fn is_active(&self) -> bool {
        self.enabled && self.can_swap()
    }

    fn protocol(&self) -> DexProtocol {
        DexProtocol::Meteora
    }

    fn pool_type(&self) -> CorePoolType {
        if self.curve_info.is_stable {
            CorePoolType::Stable
        } else {
            CorePoolType::Cpmm
        }
    }
}
