//! Meteora DAMM V2 类型定义
//!
//! 定义 Meteora DAMM V2 特有的数据结构和类型

use serde::{Deserialize, Serialize};
use shared::{EchoesError, Result};

/// Meteora DAMM V2 数学常量
pub mod math_constants {
    /// 基础点精度 (10000 = 100%)
    pub const BASIS_POINT_MAX: u64 = 10000;
    /// 最小流动性
    pub const MIN_LIQUIDITY: u64 = 1000;
    /// 最大滑点 (50%)
    pub const MAX_SLIPPAGE: f64 = 0.5;
    /// 费率精度 (1000000 = 100%)
    pub const FEE_RATE_DENOMINATOR: u64 = 1000000;
    /// 虚拟价格精度
    pub const VIRTUAL_PRICE_PRECISION: u64 = 1_000_000_000;
    /// 放大系数最大值
    pub const MAX_AMP: u64 = 1_000_000;
    /// 迭代计算最大次数
    pub const MAX_ITERATIONS: usize = 32;
    /// 脱锚阈值最大值 (基点)
    pub const MAX_DEPEG_THRESHOLD: u64 = 5000; // 50%
}

/// Meteora DAMM V2 池统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraDammStats {
    /// Token A 金库储备
    pub token_a_reserve: u64,
    /// Token B 金库储备
    pub token_b_reserve: u64,
    /// LP 代币供应量
    pub lp_supply: u64,
    /// 24小时交易量
    pub volume_24h: u64,
    /// 24小时费用
    pub fees_24h: u64,
    /// 协议费用 token A
    pub protocol_fees_token_a: u64,
    /// 协议费用 token B
    pub protocol_fees_token_b: u64,
}

/// 交换估算结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapEstimation {
    /// 输入金额
    pub input_amount: u64,
    /// 输出金额
    pub output_amount: u64,
    /// 费用金额
    pub fee_amount: u64,
    /// 协议费用金额
    pub protocol_fee_amount: u64,
    /// 价格影响（百分比）
    pub price_impact: f64,
    /// 滑点（百分比）
    pub slippage: f64,
}

/// 流动性信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityInfo {
    /// 总流动性（以 token A 计价）
    pub total_liquidity_token_a: u64,
    /// 总流动性（以 token B 计价）
    pub total_liquidity_token_b: u64,
    /// 流动性提供者数量（LP 代币持有者）
    pub lp_count: u32,
    /// 当前价格
    pub current_price: f64,
}

/// 价格信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceInfo {
    /// 当前价格（token B/token A）
    pub current_price: f64,
    /// 价格变化（相对于上次更新）
    pub price_change: Option<f64>,
    /// 价格变化百分比
    pub price_change_percent: Option<f64>,
}

/// 费用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeeInfo {
    /// 交易费率分子
    pub trade_fee_numerator: u64,
    /// 交易费率分母
    pub trade_fee_denominator: u64,
    /// 协议费率分子
    pub protocol_fee_numerator: u64,
    /// 协议费率分母
    pub protocol_fee_denominator: u64,
}

impl FeeInfo {
    /// 创建新的费用信息
    pub fn new(
        trade_fee_numerator: u64,
        trade_fee_denominator: u64,
        protocol_fee_numerator: u64,
        protocol_fee_denominator: u64,
    ) -> Self {
        Self {
            trade_fee_numerator,
            trade_fee_denominator,
            protocol_fee_numerator,
            protocol_fee_denominator,
        }
    }

    /// 获取交易费率（小数）
    pub fn trade_fee_rate(&self) -> f64 {
        if self.trade_fee_denominator == 0 {
            return 0.0;
        }
        self.trade_fee_numerator as f64 / self.trade_fee_denominator as f64
    }

    /// 获取协议费率（小数）
    pub fn protocol_fee_rate(&self) -> f64 {
        if self.protocol_fee_denominator == 0 {
            return 0.0;
        }
        self.protocol_fee_numerator as f64 / self.protocol_fee_denominator as f64
    }

    /// 计算费用金额
    pub fn calculate_fee(&self, amount: u64) -> Result<u64> {
        if self.trade_fee_denominator == 0 {
            return Ok(0);
        }
        
        amount
            .checked_mul(self.trade_fee_numerator)
            .and_then(|fee| fee.checked_div(self.trade_fee_denominator))
            .ok_or_else(|| EchoesError::InvalidInput("Fee calculation overflow".to_string()))
    }

    /// 计算协议费用
    pub fn calculate_protocol_fee(&self, total_fee: u64) -> Result<u64> {
        if self.protocol_fee_denominator == 0 {
            return Ok(0);
        }
        
        total_fee
            .checked_mul(self.protocol_fee_numerator)
            .and_then(|fee| fee.checked_div(self.protocol_fee_denominator))
            .ok_or_else(|| EchoesError::InvalidInput("Protocol fee calculation overflow".to_string()))
    }
}

/// 曲线类型相关的计算
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CurveInfo {
    /// 是否为稳定币曲线
    pub is_stable: bool,
    /// 放大系数（仅稳定币曲线）
    pub amp: Option<u64>,
    /// Token A 倍数
    pub token_a_multiplier: u64,
    /// Token B 倍数
    pub token_b_multiplier: u64,
    /// 精度因子
    pub precision_factor: u8,
}

impl CurveInfo {
    /// 创建恒定乘积曲线信息
    pub fn constant_product() -> Self {
        Self {
            is_stable: false,
            amp: None,
            token_a_multiplier: 1,
            token_b_multiplier: 1,
            precision_factor: 0,
        }
    }

    /// 创建稳定币曲线信息
    pub fn stable(
        amp: u64,
        token_a_multiplier: u64,
        token_b_multiplier: u64,
        precision_factor: u8,
    ) -> Self {
        Self {
            is_stable: true,
            amp: Some(amp),
            token_a_multiplier,
            token_b_multiplier,
            precision_factor,
        }
    }
}

/// 工具函数
pub mod utils {
    use super::*;

    /// 计算恒定乘积输出金额
    /// output = (input * reserve_out) / (reserve_in + input)
    pub fn calculate_constant_product_output(
        input_amount: u64,
        reserve_in: u64,
        reserve_out: u64,
    ) -> Result<u64> {
        if reserve_in == 0 || reserve_out == 0 {
            return Err(EchoesError::InvalidInput(
                "Reserve cannot be zero".to_string(),
            ));
        }

        let numerator = (input_amount as u128)
            .checked_mul(reserve_out as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Numerator overflow".to_string()))?;

        let denominator = (reserve_in as u128)
            .checked_add(input_amount as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Denominator overflow".to_string()))?;

        let output = numerator
            .checked_div(denominator)
            .ok_or_else(|| EchoesError::InvalidInput("Division by zero".to_string()))?;

        Ok(output as u64)
    }

    /// 计算稳定币曲线输出金额（简化版）
    /// 使用简化的 StableSwap 公式
    pub fn calculate_stable_output(
        input_amount: u64,
        reserve_in: u64,
        reserve_out: u64,
        amp: u64,
        multiplier_in: u64,
        multiplier_out: u64,
    ) -> Result<u64> {
        if reserve_in == 0 || reserve_out == 0 {
            return Err(EchoesError::InvalidInput(
                "Reserve cannot be zero".to_string(),
            ));
        }

        // 应用倍数调整
        let adjusted_reserve_in = reserve_in.saturating_mul(multiplier_in);
        let adjusted_reserve_out = reserve_out.saturating_mul(multiplier_out);
        let adjusted_input = input_amount.saturating_mul(multiplier_in);

        // 对于小交换量，使用恒定乘积近似
        if amp == 0 || adjusted_input < adjusted_reserve_in / 1000 {
            let output = calculate_constant_product_output(
                adjusted_input,
                adjusted_reserve_in,
                adjusted_reserve_out,
            )?;
            return Ok(output / multiplier_out);
        }

        // 简化的稳定币曲线计算
        // 实际实现应该使用更精确的 StableSwap 算法
        let d = calculate_d(adjusted_reserve_in, adjusted_reserve_out, amp)?;
        let new_reserve_in = adjusted_reserve_in + adjusted_input;
        let new_reserve_out = calculate_y(new_reserve_in, d, amp)?;
        
        let output = adjusted_reserve_out
            .checked_sub(new_reserve_out)
            .ok_or_else(|| EchoesError::InvalidInput("Output calculation underflow".to_string()))?;

        Ok(output / multiplier_out)
    }

    /// 计算 D 值（简化版）
    fn calculate_d(reserve_a: u64, reserve_b: u64, amp: u64) -> Result<u64> {
        // 简化计算：D ≈ 2 * sqrt(reserve_a * reserve_b)
        let product = (reserve_a as u128)
            .checked_mul(reserve_b as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Product overflow".to_string()))?;
        
        let sqrt_product = (product as f64).sqrt() as u64;
        let d = sqrt_product.saturating_mul(2);
        
        // 应用放大系数调整
        let adjusted_d = if amp > 1 {
            d.saturating_mul(amp) / 100
        } else {
            d
        };
        
        Ok(adjusted_d)
    }

    /// 计算 Y 值（简化版）
    fn calculate_y(x: u64, d: u64, _amp: u64) -> Result<u64> {
        // 简化计算：y = d - x（对于平衡的稳定币池）
        Ok(d.saturating_sub(x))
    }

    /// 计算价格影响
    /// price_impact = abs(new_price - old_price) / old_price
    pub fn calculate_price_impact(old_price: f64, new_price: f64) -> f64 {
        if old_price == 0.0 {
            return 0.0;
        }
        
        ((new_price - old_price) / old_price).abs()
    }

    /// 计算当前价格（基于储备比例）
    /// price = reserve_token_b / reserve_token_a
    pub fn calculate_price(
        reserve_token_a: u64,
        reserve_token_b: u64,
        token_a_decimals: u8,
        token_b_decimals: u8,
    ) -> f64 {
        if reserve_token_a == 0 {
            return 0.0;
        }

        let token_a_adjusted = reserve_token_a as f64 / 10_f64.powi(token_a_decimals as i32);
        let token_b_adjusted = reserve_token_b as f64 / 10_f64.powi(token_b_decimals as i32);

        token_b_adjusted / token_a_adjusted
    }

    /// 安全的百分比计算
    pub fn safe_percentage(value: f64) -> f64 {
        if value.is_finite() {
            value.max(0.0).min(100.0)
        } else {
            0.0
        }
    }

    /// 格式化金额为可读字符串
    pub fn format_amount(amount: u64, decimals: u8) -> String {
        let adjusted = amount as f64 / 10_f64.powi(decimals as i32);
        
        if adjusted >= 1_000_000_000.0 {
            format!("{:.2}B", adjusted / 1_000_000_000.0)
        } else if adjusted >= 1_000_000.0 {
            format!("{:.2}M", adjusted / 1_000_000.0)
        } else if adjusted >= 1_000.0 {
            format!("{:.2}K", adjusted / 1_000.0)
        } else {
            format!("{:.6}", adjusted)
        }
    }

    /// 计算给定输出所需的输入金额（反向恒定乘积公式）
    pub fn calculate_input_for_output(
        output_amount: u64,
        reserve_in: u64,
        reserve_out: u64,
    ) -> Result<u64> {
        if reserve_out <= output_amount {
            return Err(EchoesError::InvalidInput(
                "Output amount exceeds available liquidity".to_string(),
            ));
        }

        // input = (output * reserve_in) / (reserve_out - output)
        let numerator = (output_amount as u128)
            .checked_mul(reserve_in as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Numerator overflow".to_string()))?;

        let denominator = (reserve_out as u128)
            .checked_sub(output_amount as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Denominator underflow".to_string()))?;

        if denominator == 0 {
            return Err(EchoesError::InvalidInput("Division by zero".to_string()));
        }

        let input = numerator
            .checked_div(denominator)
            .ok_or_else(|| EchoesError::InvalidInput("Division overflow".to_string()))?;

        Ok(input as u64)
    }
}

/// Meteora DAMM V2 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MeteoraDammError {
    /// 流动性不足
    InsufficientLiquidity,
    /// 价格影响过大
    ExcessivePriceImpact,
    /// 无效的代币对
    InvalidTokenPair,
    /// 无效的金额
    InvalidAmount,
    /// 池子不存在
    PoolNotFound,
    /// 计算溢出
    CalculationOverflow,
    /// 滑点过大
    ExcessiveSlippage,
    /// 池子状态无效
    InvalidPoolStatus,
    /// 费率配置无效
    InvalidFeeConfig,
    /// 曲线参数无效
    InvalidCurveParams,
    /// 放大系数无效
    InvalidAmplification,
    /// 脱锚阈值超出范围
    DepegThresholdExceeded,
}

impl std::fmt::Display for MeteoraDammError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MeteoraDammError::InsufficientLiquidity => write!(f, "Insufficient liquidity"),
            MeteoraDammError::ExcessivePriceImpact => write!(f, "Excessive price impact"),
            MeteoraDammError::InvalidTokenPair => write!(f, "Invalid token pair"),
            MeteoraDammError::InvalidAmount => write!(f, "Invalid amount"),
            MeteoraDammError::PoolNotFound => write!(f, "Pool not found"),
            MeteoraDammError::CalculationOverflow => write!(f, "Calculation overflow"),
            MeteoraDammError::ExcessiveSlippage => write!(f, "Excessive slippage"),
            MeteoraDammError::InvalidPoolStatus => write!(f, "Invalid pool status"),
            MeteoraDammError::InvalidFeeConfig => write!(f, "Invalid fee configuration"),
            MeteoraDammError::InvalidCurveParams => write!(f, "Invalid curve parameters"),
            MeteoraDammError::InvalidAmplification => write!(f, "Invalid amplification factor"),
            MeteoraDammError::DepegThresholdExceeded => write!(f, "Depeg threshold exceeded"),
        }
    }
}

impl std::error::Error for MeteoraDammError {}

/// Meteora DAMM V2 结果类型
pub type MeteoraDammResult<T> = std::result::Result<T, MeteoraDammError>;