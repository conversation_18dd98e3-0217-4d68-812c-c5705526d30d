//! Meteora DLMM 类型定义
//!
//! 定义 Meteora DLMM 特有的数据结构和类型

use serde::{Deserialize, Serialize};
use shared::{EchoesError, Result};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;

/// Meteora DLMM 数学常量
pub mod math_constants {
    /// 基础点精度 (10000 = 100%)
    pub const BASIS_POINT_MAX: u64 = 10000;
    /// 最大bin数量
    pub const MAX_BIN_PER_ARRAY: usize = 70;
    /// 价格精度位数
    pub const PRICE_PRECISION_BITS: u32 = 64;
    /// 最大bin ID
    pub const MAX_BIN_ID: i32 = 443636;
    /// 最小bin ID
    pub const MIN_BIN_ID: i32 = -443636;
}

/// Meteora DLMM Bin 数据结构
/// 表示单个价格区间的流动性状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbBin {
    /// X代币数量
    pub amount_x: u64,
    /// Y代币数量
    pub amount_y: u64,
    /// 价格 (Q64.64格式)
    pub price: u128,
    /// 流动性供应量
    pub liquidity_supply: u128,
    /// 每代币存储的奖励
    pub reward_per_token_stored: [u128; 2],
    /// X代币每代币存储的费用
    pub fee_amount_x_per_token_stored: u128,
    /// Y代币每代币存储的费用
    pub fee_amount_y_per_token_stored: u128,
    /// X代币输入累计
    pub amount_x_in: u64,
    /// Y代币输入累计
    pub amount_y_in: u64,
}

impl MeteoraLbBin {
    /// 从 JSON 数据解析 MeteoraLbBin
    pub fn from_json(value: &serde_json::Value) -> Result<Self> {
        let amount_x: u64 = value
            .get("amountX")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse().ok())
            .ok_or_else(|| EchoesError::Parse("Failed to parse amountX".to_string()))?;

        let amount_y: u64 = value
            .get("amountY")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse().ok())
            .ok_or_else(|| EchoesError::Parse("Failed to parse amountY".to_string()))?;

        let price: u128 = value
            .get("price")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse().ok())
            .ok_or_else(|| EchoesError::Parse("Failed to parse price".to_string()))?;

        let liquidity_supply: u128 = value
            .get("liquiditySupply")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse().ok())
            .ok_or_else(|| EchoesError::Parse("Failed to parse liquiditySupply".to_string()))?;

        let reward_per_token_stored = {
            let rewards = value
                .get("rewardPerTokenStored")
                .and_then(|v| v.as_array())
                .ok_or_else(|| {
                    EchoesError::Parse("Failed to parse rewardPerTokenStored".to_string())
                })?;

            if rewards.len() != 2 {
                return Err(EchoesError::Parse(
                    "rewardPerTokenStored must have exactly 2 elements".to_string(),
                ));
            }

            [
                rewards[0]
                    .as_str()
                    .and_then(|s| s.parse().ok())
                    .ok_or_else(|| {
                        EchoesError::Parse("Failed to parse rewardPerTokenStored[0]".to_string())
                    })?,
                rewards[1]
                    .as_str()
                    .and_then(|s| s.parse().ok())
                    .ok_or_else(|| {
                        EchoesError::Parse("Failed to parse rewardPerTokenStored[1]".to_string())
                    })?,
            ]
        };

        let fee_amount_x_per_token_stored: u128 = value
            .get("feeAmountXPerTokenStored")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse().ok())
            .ok_or_else(|| {
                EchoesError::Parse("Failed to parse feeAmountXPerTokenStored".to_string())
            })?;

        let fee_amount_y_per_token_stored: u128 = value
            .get("feeAmountYPerTokenStored")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse().ok())
            .ok_or_else(|| {
                EchoesError::Parse("Failed to parse feeAmountYPerTokenStored".to_string())
            })?;

        let amount_x_in: u64 = value
            .get("amountXIn")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse().ok())
            .ok_or_else(|| EchoesError::Parse("Failed to parse amountXIn".to_string()))?;

        let amount_y_in: u64 = value
            .get("amountYIn")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse().ok())
            .ok_or_else(|| EchoesError::Parse("Failed to parse amountYIn".to_string()))?;

        Ok(MeteoraLbBin {
            amount_x,
            amount_y,
            price,
            liquidity_supply,
            reward_per_token_stored,
            fee_amount_x_per_token_stored,
            fee_amount_y_per_token_stored,
            amount_x_in,
            amount_y_in,
        })
    }

    /// 检查bin是否为空
    pub fn is_empty(&self) -> bool {
        self.amount_x == 0 && self.amount_y == 0
    }

    /// 获取bin的总价值（以X代币计价）
    pub fn get_total_value_in_x(&self) -> Result<u64> {
        if self.price == 0 {
            return Ok(self.amount_x);
        }

        // 将Y代币转换为X代币等价值
        let price_float = utils::q64_to_float(self.price);
        let y_value_in_x = (self.amount_y as f64 / price_float) as u64;

        Ok(self.amount_x + y_value_in_x)
    }

    /// 获取bin的总价值（以Y代币计价）
    pub fn get_total_value_in_y(&self) -> Result<u64> {
        if self.price == 0 {
            return Ok(self.amount_y);
        }

        // 将X代币转换为Y代币等价值
        let price_float = utils::q64_to_float(self.price);
        let x_value_in_y = (self.amount_x as f64 * price_float) as u64;

        Ok(self.amount_y + x_value_in_y)
    }

    pub fn get_max_amount_out(&self, swap_for_y: bool) -> u128 {
        if swap_for_y {
            self.amount_y as u128
        } else {
            self.amount_x as u128
        }
    }

    pub fn get_max_amount_in(&self, price: u128, swap_for_y: bool) -> Result<u128> {
        if swap_for_y {
            Ok(self.amount_y_in as u128 * utils::Q64_ONE / price)
        } else {
            Ok((self.amount_x_in as u128 * price) >> 64u128)
        }
    }

    pub fn get_amount_in(&self, amount_out: u128, price: u128, swap_for_y: bool) -> Result<u128> {
        if swap_for_y {
            Ok(amount_out * utils::Q64_ONE / price)
        } else {
            Ok((amount_out * price) >> 64u128)
        }
    }

    pub fn get_amount_out(&self, amount_in: u128, price: u128, swap_for_y: bool) -> Result<u128> {
        Self::get_amount_out_static(amount_in, price, swap_for_y)
    }

    pub fn swap(
        &self,
        amount_in: u128,
        price: u128,
        swap_for_y: bool,
        lb_pair_state: &crate::dex::meteora::dlmm::pool::MeteoraLbPairState,
    ) -> Result<SwapOutput> {
        let max_amount_out = self.get_max_amount_out(swap_for_y);
        let mut max_amount_in = self.get_max_amount_in(price, swap_for_y)?;

        // 计算最大输入金额的费用
        let max_fee = lb_pair_state.compute_fee(max_amount_in as u64)?;
        max_amount_in = (max_amount_in as u64)
            .checked_add(max_fee)
            .ok_or_else(|| EchoesError::InvalidState("Max amount in overflow".to_string()))?
            as u128;

        let (amount_in_with_fees, amount_out, fee, protocol_fee) = if amount_in > max_amount_in {
            // 输入超过最大值，使用最大可用值
            let protocol_fee = lb_pair_state.compute_protocol_fee(max_fee)?;
            (max_amount_in, max_amount_out, max_fee, protocol_fee)
        } else {
            // 正常交换流程
            let fee = lb_pair_state.compute_fee_from_amount(amount_in as u64)?;
            let amount_in_after_fee = (amount_in as u64).checked_sub(fee).ok_or_else(|| {
                EchoesError::InvalidState("Amount after fee underflow".to_string())
            })? as u128;
            let amount_out = Self::get_amount_out_static(amount_in_after_fee, price, swap_for_y)?;
            let amount_out = amount_out.min(max_amount_out);
            let protocol_fee = lb_pair_state.compute_protocol_fee(fee)?;
            (amount_in, amount_out, fee, protocol_fee)
        };

        let lp_fee = fee
            .checked_sub(protocol_fee)
            .ok_or_else(|| EchoesError::InvalidState("LP fee calculation underflow".to_string()))?;

        let amount_in_consumed = (amount_in_with_fees as u64)
            .checked_sub(fee)
            .ok_or_else(|| EchoesError::InvalidState("Amount in consumed underflow".to_string()))?
            as u128;

        Ok(SwapOutput {
            amount_out,
            amount_in_with_fees,
            amount_in_consumed,
            fee,
            protocol_fee,
            lp_fee,
        })
    }

    // 静态版本的 get_amount_out，供内部使用
    fn get_amount_out_static(amount_in: u128, price: u128, swap_for_y: bool) -> Result<u128> {
        if swap_for_y {
            Ok((amount_in * price) >> 64u128)
        } else {
            if price == 0 {
                return Err(EchoesError::InvalidInput(
                    "Price cannot be zero".to_string(),
                ));
            }
            Ok(amount_in * utils::Q64_ONE / price)
        }
    }
}

/// Meteora DLMM Bin Array 数据结构
/// 包含一组连续的价格区间
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbBinArray {
    /// 数组索引
    pub index: i64,
    /// 版本号
    pub version: u8,
    /// 填充字段
    pub padding: [u8; 7],
    /// 关联的LB Pair地址
    pub lb_pair: Pubkey,
    /// Bin数组
    pub bins: Vec<MeteoraLbBin>,
}

impl MeteoraLbBinArray {
    /// 从 JSON 数据解析 MeteoraLbBinArray
    pub fn from_json(json_str: &str) -> Result<Self> {
        let value: serde_json::Value = serde_json::from_str(json_str)
            .map_err(|e| EchoesError::Parse(format!("Failed to parse JSON: {}", e)))?;

        Self::from_json_value(&value)
    }

    /// 从 JSON Value 解析 MeteoraLbBinArray
    pub fn from_json_value(value: &serde_json::Value) -> Result<Self> {
        let parsed_data = value
            .get("parsed")
            .and_then(|p| p.get("data"))
            .ok_or_else(|| EchoesError::Parse("Missing parsed.data in JSON".to_string()))?;

        let index: i64 = parsed_data
            .get("index")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse().ok())
            .or_else(|| parsed_data.get("index").and_then(|v| v.as_i64()))
            .ok_or_else(|| EchoesError::Parse("Failed to parse index".to_string()))?;

        let version: u8 = parsed_data
            .get("version")
            .and_then(|v| v.as_u64())
            .map(|v| v as u8)
            .ok_or_else(|| EchoesError::Parse("Failed to parse version".to_string()))?;

        let padding_array = parsed_data
            .get("padding")
            .and_then(|v| v.as_array())
            .ok_or_else(|| EchoesError::Parse("Failed to parse padding".to_string()))?;

        if padding_array.len() != 7 {
            return Err(EchoesError::Parse(
                "padding must have exactly 7 elements".to_string(),
            ));
        }

        let mut padding = [0u8; 7];
        for (i, val) in padding_array.iter().enumerate() {
            padding[i] = val
                .as_u64()
                .map(|v| v as u8)
                .ok_or_else(|| EchoesError::Parse(format!("Failed to parse padding[{}]", i)))?;
        }

        let lb_pair_str = parsed_data
            .get("lbPair")
            .and_then(|v| v.as_str())
            .ok_or_else(|| EchoesError::Parse("Failed to parse lbPair".to_string()))?;

        let lb_pair = Pubkey::from_str(lb_pair_str)
            .map_err(|e| EchoesError::Parse(format!("Failed to parse lbPair as Pubkey: {}", e)))?;

        let bins_array = parsed_data
            .get("bins")
            .and_then(|v| v.as_array())
            .ok_or_else(|| EchoesError::Parse("Failed to parse bins".to_string()))?;

        let mut bins = Vec::new();
        for (i, bin_value) in bins_array.iter().enumerate() {
            let bin = MeteoraLbBin::from_json(bin_value)
                .map_err(|e| EchoesError::Parse(format!("Failed to parse bin[{}]: {}", i, e)))?;
            bins.push(bin);
        }

        Ok(MeteoraLbBinArray {
            index,
            version,
            padding,
            lb_pair,
            bins,
        })
    }

    /// 获取指定索引的bin
    pub fn get_bin(&self, bin_index: usize) -> Option<&MeteoraLbBin> {
        self.bins.get(bin_index)
    }

    /// 获取所有非空的bin
    pub fn get_active_bins(&self) -> Vec<(usize, &MeteoraLbBin)> {
        self.bins
            .iter()
            .enumerate()
            .filter(|(_, bin)| !bin.is_empty())
            .collect()
    }

    /// 计算数组中的总流动性
    pub fn get_total_liquidity(&self) -> u128 {
        self.bins.iter().map(|bin| bin.liquidity_supply).sum()
    }

    /// 获取价格范围
    pub fn get_price_range(&self) -> Option<(u128, u128)> {
        let active_bins = self.get_active_bins();
        if active_bins.is_empty() {
            return None;
        }

        let min_price = active_bins.iter().map(|(_, bin)| bin.price).min()?;
        let max_price = active_bins.iter().map(|(_, bin)| bin.price).max()?;

        Some((min_price, max_price))
    }
}

/// Meteora DLMM Bin Array Bitmap Extension
/// 用于快速查找活跃的bin数组
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbBinArrayBitmapExtension {
    /// 关联的LB Pair地址
    pub lb_pair: Pubkey,
    /// 正向bin数组位图
    pub positive_bin_array_bitmap: [[u64; 8]; 12],
    /// 负向bin数组位图
    pub negative_bin_array_bitmap: [[u64; 8]; 12],
}

impl MeteoraLbBinArrayBitmapExtension {
    /// 检查指定的bin数组索引是否活跃
    pub fn is_bin_array_active(&self, array_index: i64) -> bool {
        if array_index >= 0 {
            self.check_positive_bitmap(array_index as u64)
        } else {
            self.check_negative_bitmap((-array_index) as u64)
        }
    }

    /// 检查正向位图
    fn check_positive_bitmap(&self, index: u64) -> bool {
        let word_index = (index / 64) as usize;
        let bit_index = index % 64;

        if word_index >= 12 * 8 {
            return false;
        }

        let array_index = word_index / 8;
        let inner_index = word_index % 8;

        if array_index < 12 && inner_index < 8 {
            (self.positive_bin_array_bitmap[array_index][inner_index] & (1u64 << bit_index)) != 0
        } else {
            false
        }
    }

    /// 检查负向位图
    fn check_negative_bitmap(&self, index: u64) -> bool {
        let word_index = (index / 64) as usize;
        let bit_index = index % 64;

        if word_index >= 12 * 8 {
            return false;
        }

        let array_index = word_index / 8;
        let inner_index = word_index % 8;

        if array_index < 12 && inner_index < 8 {
            (self.negative_bin_array_bitmap[array_index][inner_index] & (1u64 << bit_index)) != 0
        } else {
            false
        }
    }

    /// 获取所有活跃的bin数组索引
    pub fn get_active_bin_array_indices(&self) -> Vec<i64> {
        let mut indices = Vec::new();

        // 检查正向位图
        for array_idx in 0..12 {
            for inner_idx in 0..8 {
                let bitmap = self.positive_bin_array_bitmap[array_idx][inner_idx];
                if bitmap != 0 {
                    for bit in 0..64 {
                        if (bitmap & (1u64 << bit)) != 0 {
                            let index = (array_idx * 8 + inner_idx) * 64 + bit;
                            indices.push(index as i64);
                        }
                    }
                }
            }
        }

        // 检查负向位图
        for array_idx in 0..12 {
            for inner_idx in 0..8 {
                let bitmap = self.negative_bin_array_bitmap[array_idx][inner_idx];
                if bitmap != 0 {
                    for bit in 0..64 {
                        if (bitmap & (1u64 << bit)) != 0 {
                            let index = (array_idx * 8 + inner_idx) * 64 + bit;
                            indices.push(-(index as i64));
                        }
                    }
                }
            }
        }

        indices.sort();
        indices
    }
}

/// Meteora 奖励信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraRewardInfo {
    /// 奖励代币铸币地址
    pub mint: Pubkey,
    /// 奖励库地址
    pub vault: Pubkey,
    /// 权限地址
    pub authority: Pubkey,
    /// 每秒发放量
    pub emissions_per_second: u64,
    /// 累计奖励（每流动性）
    pub reward_per_token_complete: u128,
    /// 待处理奖励
    pub reward_pending: u64,
    /// 最后更新时间
    pub last_update_time: u64,
}

/// Meteora Oracle 信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraOracle {
    /// 样本生命周期
    pub sample_lifetime: u64,
    /// 大小
    pub size: u64,
    /// 权重
    pub weight: u64,
    /// 索引
    pub index: u64,
    /// 最后更新时间
    pub last_update_time: u64,
}

/// 单个Bin的交换输出结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapOutput {
    /// 输出金额
    pub amount_out: u128,
    /// 实际消耗的输入金额（包含费用）
    pub amount_in_with_fees: u128,
    /// 实际输入到 bin 的金额（不包含费用）
    pub amount_in_consumed: u128,
    /// 总费用
    pub fee: u64,
    /// 协议费用
    pub protocol_fee: u64,
    /// 流动性提供者费用
    pub lp_fee: u64,
}

/// 交换估算结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapEstimation {
    /// 输入金额
    pub input_amount: u64,
    /// 输出金额
    pub output_amount: u64,
    /// 总费用金额
    pub fee_amount: u64,
    /// 协议费用金额
    pub protocol_fee_amount: u64,
    /// 流动性提供者费用金额
    pub lp_fee_amount: u64,
    /// 价格影响（百分比）
    pub price_impact: f64,
    /// 使用的bin信息
    pub bins_used: Vec<BinUsage>,
    /// 剩余未处理的输入
    pub remaining_input: u64,
}

/// Bin使用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinUsage {
    /// Bin ID
    pub bin_id: i32,
    /// 在此bin中的输入金额
    pub input_amount: u64,
    /// 在此bin中的输出金额
    pub output_amount: u64,
    /// Bin价格
    pub price: u128,
    /// 在此bin中的费用
    pub fee: u64,
}

/// 流动性信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityInfo {
    /// 总流动性
    pub total_liquidity: u128,
    /// 活跃bin数量
    pub active_bin_count: usize,
    /// 价格范围 (最小价格, 最大价格)
    pub price_range: (u128, u128),
    /// 流动性分布 (bin_id, price, liquidity)
    pub distribution: Vec<(i32, u128, u128)>,
}

/// 价格信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceInfo {
    /// 当前价格
    pub current_price: f64,
    /// 活跃bin ID
    pub active_bin_id: i32,
    /// 价格精度调整后的价格
    pub adjusted_price: f64,
    /// 价格变化（相对于上次更新）
    pub price_change: Option<f64>,
}

/// 缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    /// 总池数量
    pub total_pools: usize,
    /// 总流动性
    pub total_liquidity: u128,
    /// 活跃池数量
    pub active_pools: usize,
}

/// 工具函数
pub mod utils {
    use super::*;

    /// Q64.64格式常量：2^64
    pub const Q64_ONE: u128 = 1u128 << 64;

    /// Q64.64格式常量的浮点数形式
    pub const Q64_ONE_F64: f64 = (1u128 << 64) as f64;

    /// 计算两个价格之间的百分比差异
    pub fn calculate_price_difference(price1: u128, price2: u128) -> f64 {
        if price1 == 0 || price2 == 0 {
            return 0.0;
        }

        let diff = if price1 > price2 {
            price1 - price2
        } else {
            price2 - price1
        };

        (diff as f64 / price1 as f64) * 100.0
    }

    /// 将Q64.64格式的价格转换为浮点数
    ///
    /// # 参数
    /// * `q64_price` - Q64.64格式的价格值
    ///
    /// # 返回
    /// 对应的浮点数价格
    pub fn q64_to_float(q64_price: u128) -> f64 {
        q64_price as f64 / Q64_ONE_F64
    }

    /// 将浮点数价格转换为Q64.64格式
    ///
    /// # 参数
    /// * `price` - 浮点数价格
    ///
    /// # 返回
    /// Q64.64格式的价格值
    pub fn float_to_q64(price: f64) -> u128 {
        (price * Q64_ONE_F64) as u128
    }

    /// 安全地将Q64.64格式转换为浮点数，处理溢出情况
    ///
    /// # 参数
    /// * `q64_price` - Q64.64格式的价格值
    ///
    /// # 返回
    /// Result包含浮点数价格或错误
    pub fn q64_to_float_safe(q64_price: u128) -> Result<f64> {
        if q64_price > u128::MAX / 2 {
            return Err(EchoesError::InvalidInput(
                "Q64 value too large for safe conversion".to_string(),
            ));
        }
        Ok(q64_price as f64 / Q64_ONE_F64)
    }

    /// 安全地将浮点数转换为Q64.64格式，处理溢出情况
    ///
    /// # 参数
    /// * `price` - 浮点数价格
    ///
    /// # 返回
    /// Result包含Q64.64格式的价格值或错误
    pub fn float_to_q64_safe(price: f64) -> Result<u128> {
        if price < 0.0 {
            return Err(EchoesError::InvalidInput(
                "Price cannot be negative".to_string(),
            ));
        }

        let q64_value = price * Q64_ONE_F64;
        if q64_value > u128::MAX as f64 {
            return Err(EchoesError::InvalidInput(
                "Price too large for Q64.64 format".to_string(),
            ));
        }

        Ok(q64_value as u128)
    }

    /// 格式化流动性数量为可读字符串
    pub fn format_liquidity(liquidity: u128) -> String {
        if liquidity >= 1_000_000_000_000 {
            format!("{:.2}T", liquidity as f64 / 1_000_000_000_000.0)
        } else if liquidity >= 1_000_000_000 {
            format!("{:.2}B", liquidity as f64 / 1_000_000_000.0)
        } else if liquidity >= 1_000_000 {
            format!("{:.2}M", liquidity as f64 / 1_000_000.0)
        } else if liquidity >= 1_000 {
            format!("{:.2}K", liquidity as f64 / 1_000.0)
        } else {
            liquidity.to_string()
        }
    }
}

/// Meteora DLMM 交换路径
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbSwapPath {
    /// 使用的 Bin
    pub bins_used: Vec<MeteoraLbBinUsage>,
    /// 总输入
    pub total_input: u64,
    /// 总输出
    pub total_output: u64,
    /// 总费用
    pub total_fee: u64,
    /// 价格影响
    pub price_impact: f64,
}

/// Meteora DLMM Bin 使用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbBinUsage {
    /// Bin ID
    pub bin_id: i32,
    /// 输入金额
    pub input_amount: u64,
    /// 输出金额
    pub output_amount: u64,
    /// 费用
    pub fee: u64,
    /// Bin 价格
    pub price: u128,
}

/// Meteora DLMM 位置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbPositionInfo {
    /// 位置 NFT 铸币地址
    pub nft_mint: Pubkey,
    /// LB Pair 地址
    pub lb_pair: Pubkey,
    /// 下边界 Bin ID
    pub lower_bin_id: i32,
    /// 上边界 Bin ID
    pub upper_bin_id: i32,
    /// 最后更新时间
    pub last_updated_at: u64,
    /// 总流动性
    pub total_liquidity: u128,
}

/// Meteora DLMM 统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbStats {
    /// 总流动性
    pub total_liquidity: u128,
    /// 活跃 Bin 数量
    pub active_bin_count: usize,
    /// 价格范围
    pub price_range: (u128, u128),
    /// 24小时交易量
    pub volume_24h: u64,
    /// 24小时费用
    pub fees_24h: u64,
}

/// Meteora DLMM 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MeteoraLbError {
    /// 无效的 Bin ID
    InvalidBinId,
    /// 无效的 Bin 步长
    InvalidBinStep,
    /// 无效的流动性
    InvalidLiquidity,
    /// 无效的价格
    InvalidPrice,
    /// Bin 不存在
    BinNotFound,
    /// Bin 数组不存在
    BinArrayNotFound,
    /// 位置不存在
    PositionNotFound,
    /// 计算溢出
    CalculationOverflow,
    /// 流动性不足
    InsufficientLiquidity,
}

impl std::fmt::Display for MeteoraLbError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MeteoraLbError::InvalidBinId => write!(f, "Invalid bin ID"),
            MeteoraLbError::InvalidBinStep => write!(f, "Invalid bin step"),
            MeteoraLbError::InvalidLiquidity => write!(f, "Invalid liquidity"),
            MeteoraLbError::InvalidPrice => write!(f, "Invalid price"),
            MeteoraLbError::BinNotFound => write!(f, "Bin not found"),
            MeteoraLbError::BinArrayNotFound => write!(f, "Bin array not found"),
            MeteoraLbError::PositionNotFound => write!(f, "Position not found"),
            MeteoraLbError::CalculationOverflow => write!(f, "Calculation overflow"),
            MeteoraLbError::InsufficientLiquidity => write!(f, "Insufficient liquidity"),
        }
    }
}

impl std::error::Error for MeteoraLbError {}

/// Meteora DLMM 结果类型
pub type MeteoraLbResult<T> = std::result::Result<T, MeteoraLbError>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_q64_conversion_consistency() {
        // 测试 Q64 转换工具函数的一致性
        let test_prices = vec![0.0001, 1.0, 1.5, 100.0, 1000000.0];

        for price in test_prices {
            let q64_price = utils::float_to_q64(price);
            let converted_back = utils::q64_to_float(q64_price);

            // 对于合理精度范围内的值，应该可以相互转换
            let precision_tolerance = 1e-10; // 浮点精度容差
            assert!(
                (converted_back - price).abs() < precision_tolerance,
                "Conversion failed for price {}: {} != {}",
                price,
                converted_back,
                price
            );
        }

        // 测试常用常量
        assert_eq!(utils::Q64_ONE, 1u128 << 64);
        assert_eq!(utils::Q64_ONE_F64, (1u128 << 64) as f64);
        assert_eq!(utils::float_to_q64(1.0), utils::Q64_ONE);
        assert_eq!(utils::q64_to_float(utils::Q64_ONE), 1.0);
    }

    #[test]
    fn test_q64_safe_conversions() {
        // 测试安全转换函数
        let valid_price = 123.456;
        let q64_result = utils::float_to_q64_safe(valid_price);
        assert!(q64_result.is_ok());

        let back_result = utils::q64_to_float_safe(q64_result.unwrap());
        assert!(back_result.is_ok());

        // 测试错误情况
        let negative_price = -1.0;
        let negative_result = utils::float_to_q64_safe(negative_price);
        assert!(negative_result.is_err());

        let too_large_price = f64::MAX;
        let large_result = utils::float_to_q64_safe(too_large_price);
        assert!(large_result.is_err());
    }
}
