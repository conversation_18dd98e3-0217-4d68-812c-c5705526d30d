//! Pump Swap 池管理器实现
//!
//! 实现 Pump Swap 特定的池管理逻辑

use super::pool::PumpSwapPoolState;
use super::types::*;
use crate::core::{LiquidityInfo as CoreLiquidityInfo, PoolManager, SwapDirection, SwapEstimation as CoreSwapEstimation};
use shared::anchor_types::pump_swap::{GlobalConfig, Pool};
use shared::{EchoesError, Result};
use solana_sdk::pubkey::Pubkey;

/// Pump Swap 池管理器
/// 负责管理 Pump Swap 池的状态和计算交换
#[derive(Debug, Clone)]
pub struct PumpSwapPoolManager {
    /// 池状态
    pub pool_state: PumpSwapPoolState,
}

impl PumpSwapPoolManager {
    /// 创建新的池管理器
    pub fn new(pool_state: PumpSwapPoolState) -> Self {
        Self { pool_state }
    }

    /// 从账号数据创建池管理器
    pub fn from_account_data(
        address: Pubkey,
        pool_data: Pool,
        global_config: &GlobalConfig,
        base_reserve: Option<u64>,
        quote_reserve: Option<u64>,
    ) -> Result<Self> {
        let mut pool_state = PumpSwapPoolState::from_pool_data(address, pool_data, global_config);

        // 如果提供了储备数据，则更新
        if let (Some(base), Some(quote)) = (base_reserve, quote_reserve) {
            // 使用默认精度，实际应用中应该从代币信息获取
            pool_state.update_reserves(base, quote, 9, 6);
        }

        // 验证池状态
        pool_state.validate()?;

        Ok(Self::new(pool_state))
    }

    /// 更新储备量
    pub fn update_reserves(&mut self, base_reserve: u64, quote_reserve: u64) {
        // 使用默认精度，实际应用中应该从代币信息获取
        self.pool_state.update_reserves(base_reserve, quote_reserve, 9, 6);
    }

    /// 获取基础代币储备
    pub fn get_base_reserve(&self) -> u64 {
        self.pool_state.get_base_reserve()
    }

    /// 获取报价代币储备
    pub fn get_quote_reserve(&self) -> u64 {
        self.pool_state.get_quote_reserve()
    }

    /// 获取费用信息
    pub fn get_fee_info(&self) -> &FeeInfo {
        &self.pool_state.fee_info
    }

    /// 检查是否为指定代币对的池
    pub fn is_pool_for_pair(&self, token_a: &Pubkey, token_b: &Pubkey) -> bool {
        self.pool_state.matches_token_pair(token_a, token_b)
    }

    /// 获取最佳路由（对于单一池子，路由就是直接交换）
    pub fn get_best_route(
        &self,
        input_mint: &Pubkey,
        output_mint: &Pubkey,
        input_amount: u64,
    ) -> Result<SwapEstimation> {
        // 验证代币对
        if !self.pool_state.matches_token_pair(input_mint, output_mint) {
            return Err(EchoesError::InvalidInput(
                "Token pair not supported by this pool".to_string(),
            ));
        }

        // 确定交换方向
        let base_to_quote = self.pool_state.base_mint == *input_mint;

        if base_to_quote {
            self.pool_state.estimate_swap_base_to_quote(input_amount)
        } else {
            self.pool_state.estimate_swap_quote_to_base(input_amount)
        }
    }

    /// 获取流动性深度信息
    pub fn get_liquidity_depth(&self, levels: usize) -> Vec<(f64, u64, u64)> {
        // 简化实现：返回当前价格层级
        // 实际实现可能需要模拟不同输入量的价格影响
        let mut depth = Vec::new();
        let current_price = self.pool_state.get_current_price().unwrap_or(0.0);

        for i in 0..levels {
            let price_offset = (i as f64) * 0.01; // 1% 间隔
            let bid_price = current_price * (1.0 - price_offset);
            let ask_price = current_price * (1.0 + price_offset);

            // 简化的流动性估算
            let liquidity = if i == 0 {
                self.pool_state.get_base_reserve().min(self.pool_state.get_quote_reserve())
            } else {
                (self.pool_state.get_base_reserve().min(self.pool_state.get_quote_reserve())) / (i as u64 + 1)
            };

            depth.push((bid_price, liquidity, liquidity));
            if bid_price != ask_price {
                depth.push((ask_price, liquidity, liquidity));
            }
        }

        depth.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap());
        depth
    }

    /// 检查池的健康状态
    pub fn check_health(&self) -> Result<()> {
        // 检查基本状态
        self.pool_state.validate()?;

        // 检查流动性
        if self.pool_state.get_base_reserve() < math_constants::MIN_LIQUIDITY
            || self.pool_state.get_quote_reserve() < math_constants::MIN_LIQUIDITY {
            return Err(EchoesError::InvalidState("Insufficient liquidity".to_string()));
        }

        // 检查价格合理性
        let price = self.pool_state.get_current_price()?;
        if !price.is_finite() || price <= 0.0 {
            return Err(EchoesError::InvalidState("Invalid price".to_string()));
        }

        Ok(())
    }

    /// 获取池的摘要信息
    pub fn get_pool_summary(&self) -> String {
        format!(
            "Pump Pool {}: {}/{} (Price: {:.6}, Base: {}, Quote: {})",
            self.pool_state.index,
            self.pool_state.base_mint,
            self.pool_state.quote_mint,
            self.pool_state.get_current_price().unwrap_or(0.0),
            utils::format_amount(self.pool_state.get_base_reserve(), self.pool_state.get_base_decimals()),
            utils::format_amount(self.pool_state.get_quote_reserve(), self.pool_state.get_quote_decimals()),
        )
    }

    /// 估算给定输出金额所需的输入金额
    pub fn estimate_input_for_output(
        &self,
        output_amount: u64,
        base_to_quote: bool,
    ) -> Result<u64> {
        let (reserve_in, reserve_out) = if base_to_quote {
            (self.pool_state.get_base_reserve(), self.pool_state.get_quote_reserve())
        } else {
            (self.pool_state.get_quote_reserve(), self.pool_state.get_base_reserve())
        };

        if reserve_out <= output_amount {
            return Err(EchoesError::InvalidInput(
                "Output amount exceeds available liquidity".to_string(),
            ));
        }

        // 使用反向 AMM 公式: input = (output * reserve_in) / (reserve_out - output)
        let numerator = (output_amount as u128)
            .checked_mul(reserve_in as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Numerator overflow".to_string()))?;

        let denominator = (reserve_out as u128)
            .checked_sub(output_amount as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Denominator underflow".to_string()))?;

        if denominator == 0 {
            return Err(EchoesError::InvalidInput("Division by zero".to_string()));
        }

        let input_without_fee = numerator
            .checked_div(denominator)
            .ok_or_else(|| EchoesError::InvalidInput("Division overflow".to_string()))?
            as u64;

        // 考虑费用：input_with_fee = input_without_fee / (1 - fee_rate)
        let fee_rate = self.pool_state.fee_info.total_fee_basis_points as f64 / math_constants::BASIS_POINT_MAX as f64;
        let input_with_fee = (input_without_fee as f64 / (1.0 - fee_rate)) as u64;

        Ok(input_with_fee)
    }
}

impl PoolManager for PumpSwapPoolManager {
    type State = PumpSwapPoolState;

    fn get_state(&self) -> &Self::State {
        &self.pool_state
    }

    fn get_current_price(&self) -> Result<f64> {
        self.pool_state.get_current_price()
    }

    fn estimate_swap_output(
        &self,
        input_amount: u64,
        direction: SwapDirection,
        max_price_impact: Option<f64>,
    ) -> Result<CoreSwapEstimation> {
        let estimation = match direction {
            SwapDirection::AToB => {
                // 基础代币 -> 报价代币
                self.pool_state.estimate_swap_base_to_quote(input_amount)?
            }
            SwapDirection::BToA => {
                // 报价代币 -> 基础代币
                self.pool_state.estimate_swap_quote_to_base(input_amount)?
            }
        };

        // 检查价格影响限制
        if let Some(max_impact) = max_price_impact {
            if estimation.price_impact > max_impact {
                return Err(EchoesError::InvalidInput(
                    format!("Price impact {:.2}% exceeds maximum {:.2}%",
                           estimation.price_impact, max_impact)
                ));
            }
        }

        // 计算交换后价格
        let price_after = if direction == SwapDirection::AToB {
            // 基础代币 -> 报价代币，计算新价格
            let new_base_reserve = self.pool_state.get_base_reserve() + input_amount;
            let new_quote_reserve = self.pool_state.get_quote_reserve()
                .checked_sub(estimation.output_amount)
                .unwrap_or(self.pool_state.get_quote_reserve());
            utils::calculate_price(new_base_reserve, new_quote_reserve,
                                 self.pool_state.get_base_decimals(), self.pool_state.get_quote_decimals())
        } else {
            // 报价代币 -> 基础代币，计算新价格
            let new_quote_reserve = self.pool_state.get_quote_reserve() + input_amount;
            let new_base_reserve = self.pool_state.get_base_reserve()
                .checked_sub(estimation.output_amount)
                .unwrap_or(self.pool_state.get_base_reserve());
            utils::calculate_price(new_base_reserve, new_quote_reserve,
                                 self.pool_state.get_base_decimals(), self.pool_state.get_quote_decimals())
        };

        // 转换为核心类型
        Ok(CoreSwapEstimation {
            input_amount: estimation.input_amount,
            output_amount: estimation.output_amount,
            minimum_output: Some(estimation.output_amount), // 简化处理
            price_impact: estimation.price_impact,
            fee_amount: estimation.fee_amount,
            price_after,
            direction,
        })
    }

    fn get_liquidity_info(&self) -> CoreLiquidityInfo {
        let current_price = self.pool_state.get_current_price().unwrap_or(0.0);

        // 计算总流动性（基础代币 + 报价代币等价值）
        let base_value = self.pool_state.get_base_reserve() as u128;
        let quote_value_in_base = if current_price > 0.0 {
            (self.pool_state.get_quote_reserve() as f64 / current_price) as u128
        } else {
            0
        };
        let total_liquidity = base_value + quote_value_in_base;

        // 创建流动性分布（简化为当前价格点）
        let distribution = vec![(
            current_price,
            self.pool_state.get_base_reserve() as u128,
            self.pool_state.get_quote_reserve() as u128,
        )];

        CoreLiquidityInfo {
            total_liquidity,
            active_ranges: 1, // CPMM 只有一个价格点
            price_range: (current_price, current_price),
            distribution,
        }
    }

    fn update_pool_state(&mut self, new_state: Self::State) -> Result<()> {
        // 验证新状态
        new_state.validate()?;

        // 更新状态
        self.pool_state = new_state;

        Ok(())
    }
}
