//! Pump Swap 池状态实现
//!
//! 实现 Pump Swap 特定的池状态逻辑

use std::collections::HashMap;
use super::types::*;
use crate::core::{PoolState};
use serde::{Deserialize, Serialize};
use shared::anchor_types::pump_swap::{GlobalConfig, Pool};
use shared::{EchoesError, Result, TokenMintInfo, DexProtocol, PoolType};
use solana_sdk::pubkey::Pubkey;

/// Pump Swap 池状态
/// 表示一个完整的 Pump Swap 流动性池
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PumpSwapPoolState {
    /// 池地址
    pub address: Pubkey,
    /// 基础代币铸币地址
    pub base_mint: Pubkey,
    /// 报价代币铸币地址
    pub quote_mint: Pubkey,
    /// LP 代币铸币地址
    pub lp_mint: Pubkey,
    /// 池子基础代币账户地址
    pub pool_base_token_account: Pubkey,
    /// 池子报价代币账户地址
    pub pool_quote_token_account: Pubkey,
    /// LP 代币供应量
    pub lp_supply: u64,
    /// 创建者地址
    pub creator: Pubkey,
    /// 币创建者地址
    pub coin_creator: Pubkey,
    /// 池子索引
    pub index: u16,
    /// 池子 bump seed
    pub pool_bump: u8,
    /// 费用信息
    pub fee_info: FeeInfo,
    /// 是否活跃
    pub is_active: bool,
    /// 最后更新时间戳
    pub last_updated_slot: u64,
    /// 代币信息映射
    pub token_mint_info: HashMap<Pubkey, TokenMintInfo>,
}

impl PumpSwapPoolState {
    /// 创建新的 Pump Swap 池状态
    pub fn new(
        address: Pubkey,
        pool_data: Pool,
        fee_info: FeeInfo,
    ) -> Self {
        Self {
            address,
            base_mint: pool_data.base_mint,
            quote_mint: pool_data.quote_mint,
            lp_mint: pool_data.lp_mint,
            pool_base_token_account: pool_data.pool_base_token_account,
            pool_quote_token_account: pool_data.pool_quote_token_account,
            lp_supply: pool_data.lp_supply,
            creator: pool_data.creator,
            coin_creator: pool_data.coin_creator,
            index: pool_data.index,
            pool_bump: pool_data.pool_bump,
            fee_info,
            is_active: true,
            last_updated_slot: 0,
            token_mint_info: HashMap::new(),
        }
    }

    /// 从 Pool 账号数据创建状态（简化版）
    pub fn from_pool_data(
        address: Pubkey,
        pool_data: Pool,
        global_config: &GlobalConfig,
    ) -> Self {
        let fee_info = FeeInfo::new(
            global_config.lp_fee_basis_points,
            global_config.protocol_fee_basis_points,
        );

        Self {
            address,
            base_mint: pool_data.base_mint,
            quote_mint: pool_data.quote_mint,
            lp_mint: pool_data.lp_mint,
            pool_base_token_account: pool_data.pool_base_token_account,
            pool_quote_token_account: pool_data.pool_quote_token_account,
            lp_supply: pool_data.lp_supply,
            creator: pool_data.creator,
            coin_creator: pool_data.coin_creator,
            index: pool_data.index,
            pool_bump: pool_data.pool_bump,
            fee_info,
            is_active: true,
            last_updated_slot: 0,
            token_mint_info: HashMap::new(),
        }
    }

    /// 更新储备量
    /// 需要提供默认精度，因为不再有内置的精度字段
    pub fn update_reserves(&mut self, base_reserve: u64, quote_reserve: u64, base_decimals: u8, quote_decimals: u8) {
        // 更新 base_mint 信息
        if let Some(info) = self.token_mint_info.get_mut(&self.base_mint) {
            info.token_balance = base_reserve as u128;
        } else {
            let info = TokenMintInfo {
                mint: self.base_mint,
                decimals: base_decimals,
                token_vault: self.pool_base_token_account,
                token_balance: base_reserve as u128,
            };
            self.token_mint_info.insert(self.base_mint, info);
        }

        // 更新 quote_mint 信息
        if let Some(info) = self.token_mint_info.get_mut(&self.quote_mint) {
            info.token_balance = quote_reserve as u128;
        } else {
            let info = TokenMintInfo {
                mint: self.quote_mint,
                decimals: quote_decimals,
                token_vault: self.pool_quote_token_account,
                token_balance: quote_reserve as u128,
            };
            self.token_mint_info.insert(self.quote_mint, info);
        }
    }

    /// 更新代币信息
    pub fn update_token_mint_info(&mut self, mint: Pubkey, info: TokenMintInfo) {
        self.token_mint_info.insert(mint, info);
    }

    /// 批量更新代币信息
    pub fn update_token_mint_infos(&mut self, infos: HashMap<Pubkey, TokenMintInfo>) {
        for (mint, info) in infos {
            self.token_mint_info.insert(mint, info);
        }
    }

    /// 获取代币信息
    pub fn get_token_mint_info(&self, mint: &Pubkey) -> Option<&TokenMintInfo> {
        self.token_mint_info.get(mint)
    }

    /// 获取基础代币信息
    pub fn get_base_info(&self) -> Option<&TokenMintInfo> {
        self.token_mint_info.get(&self.base_mint)
    }

    /// 获取报价代币信息
    pub fn get_quote_info(&self) -> Option<&TokenMintInfo> {
        self.token_mint_info.get(&self.quote_mint)
    }

    /// 获取基础代币储备量
    pub fn get_base_reserve(&self) -> u64 {
        self.get_base_info()
            .map(|info| info.token_balance as u64)
            .unwrap_or(0)
    }

    /// 获取报价代币储备量
    pub fn get_quote_reserve(&self) -> u64 {
        self.get_quote_info()
            .map(|info| info.token_balance as u64)
            .unwrap_or(0)
    }

    /// 获取基础代币精度
    pub fn get_base_decimals(&self) -> u8 {
        self.get_base_info()
            .map(|info| info.decimals)
            .unwrap_or(9) // 默认精度
    }

    /// 获取报价代币精度
    pub fn get_quote_decimals(&self) -> u8 {
        self.get_quote_info()
            .map(|info| info.decimals)
            .unwrap_or(6) // 默认精度
    }

    /// 获取当前价格（报价代币/基础代币）
    pub fn get_current_price(&self) -> Result<f64> {
        let base_reserve = self.get_base_reserve();
        if base_reserve == 0 {
            return Err(EchoesError::InvalidState(
                "Base reserve is zero".to_string(),
            ));
        }

        Ok(utils::calculate_price(
            base_reserve,
            self.get_quote_reserve(),
            self.get_base_decimals(),
            self.get_quote_decimals(),
        ))
    }

    /// 估算交换输出（基础代币 -> 报价代币）
    pub fn estimate_swap_base_to_quote(&self, input_amount: u64) -> Result<SwapEstimation> {
        self.estimate_swap_internal(input_amount, true)
    }

    /// 估算交换输出（报价代币 -> 基础代币）
    pub fn estimate_swap_quote_to_base(&self, input_amount: u64) -> Result<SwapEstimation> {
        self.estimate_swap_internal(input_amount, false)
    }

    /// 内部交换估算逻辑
    fn estimate_swap_internal(&self, input_amount: u64, base_to_quote: bool) -> Result<SwapEstimation> {
        if input_amount == 0 {
            return Err(EchoesError::InvalidInput("Input amount cannot be zero".to_string()));
        }

        // 检查流动性
        let base_reserve = self.get_base_reserve();
        let quote_reserve = self.get_quote_reserve();
        if base_reserve == 0 || quote_reserve == 0 {
            return Err(EchoesError::InvalidState("Insufficient liquidity".to_string()));
        }

        // 计算费用
        let fee_amount = self.fee_info.calculate_fee(input_amount)?;
        let input_after_fee = input_amount
            .checked_sub(fee_amount)
            .ok_or_else(|| EchoesError::InvalidInput("Fee exceeds input amount".to_string()))?;

        // 确定储备量
        let (reserve_in, reserve_out) = if base_to_quote {
            (base_reserve, quote_reserve)
        } else {
            (quote_reserve, base_reserve)
        };

        // 计算输出金额
        let output_amount = utils::calculate_amm_output(input_after_fee, reserve_in, reserve_out)?;

        // 计算价格影响
        let old_price = self.get_current_price()?;
        let new_reserve_in = reserve_in + input_after_fee;
        let new_reserve_out = reserve_out
            .checked_sub(output_amount)
            .ok_or_else(|| EchoesError::InvalidState("Output exceeds available liquidity".to_string()))?;

        let (new_base_reserve, new_quote_reserve) = if base_to_quote {
            (new_reserve_in, new_reserve_out)
        } else {
            (new_reserve_out, new_reserve_in)
        };

        let new_price = utils::calculate_price(
            new_base_reserve,
            new_quote_reserve,
            self.get_base_decimals(),
            self.get_quote_decimals(),
        );

        let price_impact = utils::calculate_price_impact(old_price, new_price);

        // 检查价格影响是否过大
        if price_impact > math_constants::MAX_SLIPPAGE {
            return Err(EchoesError::InvalidInput("Excessive price impact".to_string()));
        }

        // 分配费用
        let lp_fee_amount = self.fee_info.calculate_lp_fee(fee_amount)?;
        let protocol_fee_amount = self.fee_info.calculate_protocol_fee(fee_amount)?;

        Ok(SwapEstimation {
            input_amount,
            output_amount,
            fee_amount,
            protocol_fee_amount,
            lp_fee_amount,
            price_impact: price_impact * 100.0, // 转换为百分比
            slippage: price_impact * 100.0, // 简化处理，实际应该考虑容忍度
        })
    }

    /// 获取流动性信息
    pub fn get_liquidity_info(&self) -> LiquidityInfo {
        let total_liquidity_base = self.get_base_reserve();
        let total_liquidity_quote = self.get_quote_reserve();
        let current_price = self.get_current_price().unwrap_or(0.0);

        LiquidityInfo {
            total_liquidity_base,
            total_liquidity_quote,
            lp_count: 0, // 简化处理，实际需要跟踪 LP 持有者
            current_price,
        }
    }

    /// 获取价格信息
    pub fn get_price_info(&self) -> Result<PriceInfo> {
        let current_price = self.get_current_price()?;

        Ok(PriceInfo {
            current_price,
            price_change: None, // 需要历史数据
            price_change_percent: None, // 需要历史数据
        })
    }

    /// 获取池统计信息
    pub fn get_stats(&self) -> PumpSwapStats {
        PumpSwapStats {
            base_reserve: self.get_base_reserve(),
            quote_reserve: self.get_quote_reserve(),
            lp_supply: self.lp_supply,
            volume_24h: 0, // 需要外部数据源
            fees_24h: 0,   // 需要外部数据源
        }
    }

    /// 检查代币对是否匹配
    pub fn matches_token_pair(&self, token_a: &Pubkey, token_b: &Pubkey) -> bool {
        (self.base_mint == *token_a && self.quote_mint == *token_b)
            || (self.base_mint == *token_b && self.quote_mint == *token_a)
    }

    /// 检查池是否包含指定代币
    pub fn contains_token(&self, token: &Pubkey) -> bool {
        self.base_mint == *token || self.quote_mint == *token
    }

    /// 获取对应的输出代币
    pub fn get_output_mint(&self, input_mint: &Pubkey) -> Option<Pubkey> {
        if self.base_mint == *input_mint {
            Some(self.quote_mint)
        } else if self.quote_mint == *input_mint {
            Some(self.base_mint)
        } else {
            None
        }
    }

    /// 更新最后更新时间戳
    pub fn update_slot(&mut self, slot: u64) {
        self.last_updated_slot = slot;
    }

    /// 验证池状态
    pub fn validate(&self) -> Result<()> {
        if self.base_mint == Pubkey::default() || self.quote_mint == Pubkey::default() {
            return Err(EchoesError::InvalidState("Invalid mint addresses".to_string()));
        }

        if self.base_mint == self.quote_mint {
            return Err(EchoesError::InvalidState("Base and quote mints cannot be the same".to_string()));
        }

        if self.fee_info.total_fee_basis_points > math_constants::BASIS_POINT_MAX {
            return Err(EchoesError::InvalidState("Fee basis points exceed maximum".to_string()));
        }

        Ok(())
    }
}

impl PoolState for PumpSwapPoolState {
    fn pool_address(&self) -> Pubkey {
        self.address
    }

    fn token_pair(&self) -> (Pubkey, Pubkey, u8, u8) {
        (self.base_mint, self.quote_mint, self.get_base_decimals(), self.get_quote_decimals())
    }

    fn current_price(&self) -> Result<f64> {
        self.get_current_price()
    }

    fn is_active(&self) -> bool {
        self.is_active && self.get_base_reserve() > 0 && self.get_quote_reserve() > 0
    }

    fn protocol(&self) -> DexProtocol {
        DexProtocol::PumpFun
    }

    fn pool_type(&self) -> PoolType {
        PoolType::PumpSwap
    }
}
