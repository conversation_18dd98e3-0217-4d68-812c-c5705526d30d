//! Pump Swap 类型定义
//!
//! 定义 Pump Swap 特有的数据结构和类型

use serde::{Deserialize, Serialize};
use shared::{EchoesError, Result};

/// Pump Swap 数学常量
pub mod math_constants {
    /// 基础点精度 (10000 = 100%)
    pub const BASIS_POINT_MAX: u64 = 10000;
    /// 最小流动性
    pub const MIN_LIQUIDITY: u64 = 1000;
    /// 最大滑点 (50%)
    pub const MAX_SLIPPAGE: f64 = 0.5;
}

/// Pump Swap 池统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PumpSwapStats {
    /// 基础代币储备
    pub base_reserve: u64,
    /// 报价代币储备
    pub quote_reserve: u64,
    /// LP 代币供应量
    pub lp_supply: u64,
    /// 24小时交易量
    pub volume_24h: u64,
    /// 24小时费用
    pub fees_24h: u64,
}

/// 交换估算结果
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SwapEstimation {
    /// 输入金额
    pub input_amount: u64,
    /// 输出金额
    pub output_amount: u64,
    /// 费用金额
    pub fee_amount: u64,
    /// 协议费用金额
    pub protocol_fee_amount: u64,
    /// LP 费用金额
    pub lp_fee_amount: u64,
    /// 价格影响（百分比）
    pub price_impact: f64,
    /// 滑点（百分比）
    pub slippage: f64,
}

/// 流动性信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityInfo {
    /// 总流动性（以基础代币计价）
    pub total_liquidity_base: u64,
    /// 总流动性（以报价代币计价）
    pub total_liquidity_quote: u64,
    /// 流动性提供者数量
    pub lp_count: u32,
    /// 当前价格
    pub current_price: f64,
}

/// 价格信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceInfo {
    /// 当前价格（报价代币/基础代币）
    pub current_price: f64,
    /// 价格变化（相对于上次更新）
    pub price_change: Option<f64>,
    /// 价格变化百分比
    pub price_change_percent: Option<f64>,
}

/// 费用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeeInfo {
    /// LP 费用基点
    pub lp_fee_basis_points: u64,
    /// 协议费用基点
    pub protocol_fee_basis_points: u64,
    /// 总费用基点
    pub total_fee_basis_points: u64,
}

impl FeeInfo {
    /// 创建新的费用信息
    pub fn new(lp_fee_basis_points: u64, protocol_fee_basis_points: u64) -> Self {
        Self {
            lp_fee_basis_points,
            protocol_fee_basis_points,
            total_fee_basis_points: lp_fee_basis_points + protocol_fee_basis_points,
        }
    }

    /// 计算费用金额
    pub fn calculate_fee(&self, amount: u64) -> Result<u64> {
        amount
            .checked_mul(self.total_fee_basis_points)
            .and_then(|fee| fee.checked_div(math_constants::BASIS_POINT_MAX))
            .ok_or_else(|| EchoesError::InvalidInput("Fee calculation overflow".to_string()))
    }

    /// 计算 LP 费用
    pub fn calculate_lp_fee(&self, total_fee: u64) -> Result<u64> {
        if self.total_fee_basis_points == 0 {
            return Ok(0);
        }
        
        total_fee
            .checked_mul(self.lp_fee_basis_points)
            .and_then(|fee| fee.checked_div(self.total_fee_basis_points))
            .ok_or_else(|| EchoesError::InvalidInput("LP fee calculation overflow".to_string()))
    }

    /// 计算协议费用
    pub fn calculate_protocol_fee(&self, total_fee: u64) -> Result<u64> {
        if self.total_fee_basis_points == 0 {
            return Ok(0);
        }
        
        total_fee
            .checked_mul(self.protocol_fee_basis_points)
            .and_then(|fee| fee.checked_div(self.total_fee_basis_points))
            .ok_or_else(|| EchoesError::InvalidInput("Protocol fee calculation overflow".to_string()))
    }
}

/// 工具函数
pub mod utils {
    use super::*;

    /// 计算 AMM 输出金额（恒定乘积公式）
    /// output = (input * reserve_out) / (reserve_in + input)
    pub fn calculate_amm_output(
        input_amount: u64,
        reserve_in: u64,
        reserve_out: u64,
    ) -> Result<u64> {
        if reserve_in == 0 || reserve_out == 0 {
            return Err(EchoesError::InvalidInput(
                "Reserve cannot be zero".to_string(),
            ));
        }

        let numerator = (input_amount as u128)
            .checked_mul(reserve_out as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Numerator overflow".to_string()))?;

        let denominator = (reserve_in as u128)
            .checked_add(input_amount as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Denominator overflow".to_string()))?;

        let output = numerator
            .checked_div(denominator)
            .ok_or_else(|| EchoesError::InvalidInput("Division by zero".to_string()))?;

        Ok(output as u64)
    }

    /// 计算价格影响
    /// price_impact = abs(new_price - old_price) / old_price
    pub fn calculate_price_impact(
        old_price: f64,
        new_price: f64,
    ) -> f64 {
        if old_price == 0.0 {
            return 0.0;
        }
        
        ((new_price - old_price) / old_price).abs()
    }

    /// 计算当前价格（基于储备比例）
    /// price = reserve_quote / reserve_base
    pub fn calculate_price(
        reserve_base: u64,
        reserve_quote: u64,
        base_decimals: u8,
        quote_decimals: u8,
    ) -> f64 {
        if reserve_base == 0 {
            return 0.0;
        }

        let base_adjusted = reserve_base as f64 / 10_f64.powi(base_decimals as i32);
        let quote_adjusted = reserve_quote as f64 / 10_f64.powi(quote_decimals as i32);

        quote_adjusted / base_adjusted
    }

    /// 安全的百分比计算
    pub fn safe_percentage(value: f64) -> f64 {
        if value.is_finite() {
            value.max(0.0).min(100.0)
        } else {
            0.0
        }
    }

    /// 格式化金额为可读字符串
    pub fn format_amount(amount: u64, decimals: u8) -> String {
        let adjusted = amount as f64 / 10_f64.powi(decimals as i32);
        
        if adjusted >= 1_000_000_000.0 {
            format!("{:.2}B", adjusted / 1_000_000_000.0)
        } else if adjusted >= 1_000_000.0 {
            format!("{:.2}M", adjusted / 1_000_000.0)
        } else if adjusted >= 1_000.0 {
            format!("{:.2}K", adjusted / 1_000.0)
        } else {
            format!("{:.6}", adjusted)
        }
    }
}

/// Pump Swap 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PumpSwapError {
    /// 流动性不足
    InsufficientLiquidity,
    /// 价格影响过大
    ExcessivePriceImpact,
    /// 无效的代币对
    InvalidTokenPair,
    /// 无效的金额
    InvalidAmount,
    /// 池子不存在
    PoolNotFound,
    /// 计算溢出
    CalculationOverflow,
    /// 滑点过大
    ExcessiveSlippage,
}

impl std::fmt::Display for PumpSwapError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PumpSwapError::InsufficientLiquidity => write!(f, "Insufficient liquidity"),
            PumpSwapError::ExcessivePriceImpact => write!(f, "Excessive price impact"),
            PumpSwapError::InvalidTokenPair => write!(f, "Invalid token pair"),
            PumpSwapError::InvalidAmount => write!(f, "Invalid amount"),
            PumpSwapError::PoolNotFound => write!(f, "Pool not found"),
            PumpSwapError::CalculationOverflow => write!(f, "Calculation overflow"),
            PumpSwapError::ExcessiveSlippage => write!(f, "Excessive slippage"),
        }
    }
}

impl std::error::Error for PumpSwapError {}

/// Pump Swap 结果类型
pub type PumpSwapResult<T> = std::result::Result<T, PumpSwapError>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_amm_output_calculation() {
        // 测试基本的 AMM 输出计算
        let input = 1000;
        let reserve_in = 100000;
        let reserve_out = 200000;
        
        let output = utils::calculate_amm_output(input, reserve_in, reserve_out).unwrap();
        
        // 预期输出约为 1980
        assert!(output > 1900 && output < 2000);
    }

    #[test]
    fn test_fee_calculation() {
        let fee_info = FeeInfo::new(25, 5); // 0.25% LP + 0.05% protocol = 0.3% total
        
        let amount = 100000;
        let total_fee = fee_info.calculate_fee(amount).unwrap();
        assert_eq!(total_fee, 300); // 0.3% of 100000
        
        let lp_fee = fee_info.calculate_lp_fee(total_fee).unwrap();
        assert_eq!(lp_fee, 250); // 25/30 of 300
        
        let protocol_fee = fee_info.calculate_protocol_fee(total_fee).unwrap();
        assert_eq!(protocol_fee, 50); // 5/30 of 300
    }

    #[test]
    fn test_price_calculation() {
        // reserve_base = 1,000,000 (with 6 decimals = 1.0)
        // reserve_quote = 2,000,000,000 (with 9 decimals = 2.0)
        // price = 2.0 / 1.0 = 2.0
        let price = utils::calculate_price(1000000, 2000000000, 6, 9);
        assert!((price - 2.0).abs() < 0.001); // 应该约等于 2.0
    }

    #[test]
    fn test_price_impact_calculation() {
        let old_price = 100.0;
        let new_price = 105.0;
        let impact = utils::calculate_price_impact(old_price, new_price);
        assert!((impact - 0.05).abs() < 0.001); // 5% 价格影响
    }
}