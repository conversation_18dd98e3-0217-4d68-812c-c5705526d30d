//! Raydium CLMM 池管理器实现
//!
//! 实现 Raydium CLMM 特定的池管理逻辑

use super::pool::RaydiumClmmPoolState;
use super::types::*;
use crate::core::{LiquidityInfo, PoolManager, PoolState, SwapDirection, SwapEstimation};
use shared::{EchoesError, Result};
use solana_sdk::pubkey::Pubkey;
use std::collections::BTreeMap;

/// 交换状态
#[derive(Debug, Clone)]
struct SwapState {
    amount_specified_remaining: u128,
    amount_calculated: u128,
    sqrt_price: u128,
    tick: i32,
    liquidity: u128,
    fee_amount: u64,
    protocol_fee_amount: u64,
}

/// 交换步骤结果
#[derive(Debug, Clone)]
struct SwapStepResult {
    sqrt_price_next: u128,
    amount_in: u128,
    amount_out: u128,
    fee_amount: u64,
    tick_next: i32,
}

/// Raydium CLMM 池管理器
#[derive(Debug)]
pub struct RaydiumClmmPoolManager {
    /// 池状态
    pub pool_state: RaydiumClmmPoolState,
    /// Tick数组缓存 start_tick_index -> TickArrayState
    tick_arrays: BTreeMap<i32, TickArrayState>,
    /// 位图扩展
    bitmap_extension: Option<TickArrayBitmapExtension>,
}

impl RaydiumClmmPoolManager {
    /// 创建新的池管理器
    pub fn new(pool_state: RaydiumClmmPoolState) -> Self {
        Self {
            pool_state,
            tick_arrays: BTreeMap::new(),
            bitmap_extension: None,
        }
    }

    /// 更新池状态
    pub fn update_pool_state(&mut self, new_state: RaydiumClmmPoolState) {
        self.pool_state = new_state;
    }

    /// 添加或更新tick数组
    pub fn upsert_tick_array(&mut self, tick_array: TickArrayState) {
        self.tick_arrays
            .insert(tick_array.start_tick_index, tick_array);
    }

    /// 设置位图扩展
    pub fn set_bitmap_extension(&mut self, bitmap: TickArrayBitmapExtension) {
        self.bitmap_extension = Some(bitmap);
    }

    /// 获取当前价格 (Token1/Token0)
    pub fn get_current_price(&self) -> f64 {
        // sqrt_price_x64 转换为实际价格
        // price = (sqrt_price_x64 / 2^64)^2
        let sqrt_price = self.pool_state.sqrt_price_x64 as f64 / (1u128 << 64) as f64;
        let price = sqrt_price * sqrt_price;

        // 调整小数位差异
        let decimals_diff =
            self.pool_state.mint_decimals_0 as i32 - self.pool_state.mint_decimals_1 as i32;
        price * 10f64.powi(decimals_diff)
    }

    /// 获取指定tick的信息
    pub fn get_tick_info(&self, tick: i32) -> Option<&TickInfo> {
        // 计算包含此tick的tick数组起始索引
        let tick_array_start = self.get_tick_array_start_index(tick);

        if let Some(tick_array) = self.tick_arrays.get(&tick_array_start) {
            // 在tick数组中查找具体的tick
            tick_array.ticks.iter().find(|t| t.tick == tick)
        } else {
            None
        }
    }

    /// 计算tick数组的起始索引
    fn get_tick_array_start_index(&self, tick: i32) -> i32 {
        let tick_spacing = self.pool_state.tick_spacing as i32;
        let ticks_per_array = 60; // Raydium CLMM每个数组包含60个tick
        let array_size = tick_spacing * ticks_per_array;

        if tick >= 0 {
            (tick / array_size) * array_size
        } else {
            ((tick + 1) / array_size - 1) * array_size
        }
    }

    /// 估算交换输出 (生产级实现)
    pub fn estimate_swap_output(
        &self,
        input_amount: u64,
        zero_for_one: bool, // true: Token0 -> Token1, false: Token1 -> Token0
        sqrt_price_limit: Option<u128>,
    ) -> Result<SwapResult> {
        self.estimate_swap_output_with_config(
            input_amount,
            zero_for_one,
            sqrt_price_limit,
            &SwapConfig::default(),
        )
    }

    /// 带配置的交换输出估算
    pub fn estimate_swap_output_with_config(
        &self,
        input_amount: u64,
        zero_for_one: bool,
        sqrt_price_limit: Option<u128>,
        config: &SwapConfig,
    ) -> Result<SwapResult> {
        if input_amount == 0 {
            return Err(EchoesError::InvalidInput(
                "Input amount cannot be zero".to_string(),
            ));
        }

        // 验证价格限制
        if let Some(limit) = sqrt_price_limit {
            if (zero_for_one && limit >= self.pool_state.sqrt_price_x64)
                || (!zero_for_one && limit <= self.pool_state.sqrt_price_x64)
            {
                return Err(EchoesError::InvalidInput("Invalid price limit".to_string()));
            }
        }

        let initial_sqrt_price = self.pool_state.sqrt_price_x64;
        let mut state = SwapState {
            amount_specified_remaining: input_amount as u128,
            amount_calculated: 0,
            sqrt_price: initial_sqrt_price,
            tick: self.pool_state.tick_current,
            liquidity: self.pool_state.liquidity,
            fee_amount: 0,
            protocol_fee_amount: 0,
        };

        // 设置价格限制
        let sqrt_price_limit = sqrt_price_limit.unwrap_or(if zero_for_one {
            math_constants::MIN_SQRT_PRICE + 1
        } else {
            math_constants::MAX_SQRT_PRICE - 1
        });

        println!("=== 开始交换计算 ===");
        println!(
            "初始状态: 流动性={}, tick={}, sqrt_price={}",
            state.liquidity, state.tick, state.sqrt_price
        );

        // 主交换循环
        let mut step_count = 0;
        while state.amount_specified_remaining != 0 && state.sqrt_price != sqrt_price_limit {
            step_count += 1;
            println!("\n--- 步骤 {} ---", step_count);
            println!(
                "剩余输入: {}, 当前流动性: {}",
                state.amount_specified_remaining, state.liquidity
            );

            let step = self.compute_swap_step_advanced(
                &state, // 传递不可变引用，避免在计算中修改状态
                sqrt_price_limit,
                zero_for_one,
                config,
            )?;

            // 更新价格和数量状态
            state.sqrt_price = step.sqrt_price_next;
            state.amount_specified_remaining = state
                .amount_specified_remaining
                .saturating_sub(step.amount_in);
            state.amount_calculated += step.amount_out;
            state.fee_amount += step.fee_amount;

            println!("计算步骤结果: {:?}", step);
            println!("当前状态: {:?}", state);

            // 正确处理 tick 边界跨越和流动性更新
            // 官方逻辑：检查是否达到了目标价格（即跨越了 tick 边界）
            let sqrt_price_target = if zero_for_one {
                fixed_point_math::tick_to_sqrt_price(step.tick_next).unwrap_or(step.sqrt_price_next)
            } else {
                fixed_point_math::tick_to_sqrt_price(step.tick_next).unwrap_or(step.sqrt_price_next)
            };

            let reached_tick_boundary = step.sqrt_price_next == sqrt_price_target;

            if reached_tick_boundary {
                println!("跨越 tick 边界: {}", step.tick_next);

                // 获取该 tick 的流动性变化
                if let Some(tick_info) = self.get_tick_info(step.tick_next) {
                    println!(
                        "Tick {} 流动性变化: net={}, gross={}",
                        step.tick_next, tick_info.liquidity_net, tick_info.liquidity_gross
                    );

                    // 根据交换方向正确应用流动性变化
                    let liquidity_delta = if zero_for_one {
                        // Token0 -> Token1，价格下降，从右向左移动
                        // 跨越 tick 时，减去该 tick 的流动性净变化
                        -tick_info.liquidity_net
                    } else {
                        // Token1 -> Token0，价格上升，从左向右移动
                        // 跨越 tick 时，加上该 tick 的流动性净变化
                        tick_info.liquidity_net
                    };

                    // 使用精确的流动性更新机制（模仿官方 liquidity_math::add_delta）
                    match liquidity_math::add_delta(state.liquidity, liquidity_delta) {
                        Ok(new_liquidity) => {
                            println!(
                                "流动性更新: {} -> {}, delta: {}",
                                state.liquidity, new_liquidity, liquidity_delta
                            );
                            state.liquidity = new_liquidity;
                        }
                        Err(e) => {
                            println!("流动性更新失败: {}, 保持当前流动性: {}", e, state.liquidity);
                            // 在错误情况下保持当前流动性，避免交易失败
                        }
                    }
                } else {
                    println!("警告: 未找到 tick {} 的信息", step.tick_next);
                }

                // 更新当前 tick 位置（官方逻辑）
                // 在 Uniswap v3/Raydium 中，当跨越 tick 时：
                // - zero_for_one=true 时（价格下降），当前 tick = tick_next - 1
                // - zero_for_one=false 时（价格上升），当前 tick = tick_next
                state.tick = if zero_for_one {
                    step.tick_next - 1
                } else {
                    step.tick_next
                };
            } else {
                // 未跨越 tick 边界，根据价格计算 tick
                state.tick = fixed_point_math::sqrt_price_to_tick(step.sqrt_price_next)?;
            }

            println!(
                "步骤结束状态: tick={}, 流动性={}, sqrt_price={}",
                state.tick, state.liquidity, state.sqrt_price
            );

            // 防止无限循环
            if step_count > 1000 {
                println!("警告: 交换步骤超过 1000，可能存在无限循环");
                break;
            }
        }

        // 计算价格影响
        let price_impact_bps = self.calculate_price_impact(initial_sqrt_price, state.sqrt_price);

        Ok(SwapResult {
            amount_in: (input_amount as u128 - state.amount_specified_remaining) as u64,
            amount_out: state.amount_calculated as u64,
            fee_amount: state.fee_amount,
            protocol_fee_amount: state.protocol_fee_amount,
            sqrt_price_after: state.sqrt_price,
            tick_after: state.tick,
            liquidity_after: state.liquidity,
            price_impact_bps,
        })
    }

    /// 检查是否到达了 tick 边界（更精确的官方实现）
    fn is_at_tick_boundary(&self, sqrt_price: u128, tick: i32) -> Result<bool> {
        // 官方逻辑：检查是否达到了目标价格限制
        // 在 compute_swap_step 中，如果 sqrt_price_next == sqrt_price_target，则表示达到了边界
        let tick_sqrt_price = fixed_point_math::tick_to_sqrt_price(tick)?;
        // 使用小的容差来处理浮点精度问题
        let tolerance = 1u128; // 在 Q64.64 格式下是非常小的值
        Ok((sqrt_price as i128 - tick_sqrt_price as i128).abs() <= tolerance as i128)
    }

    /// 高级交换步骤计算
    fn compute_swap_step_advanced(
        &self,
        state: &SwapState,
        sqrt_price_limit: u128,
        zero_for_one: bool,
        config: &SwapConfig,
    ) -> Result<SwapStepResult> {
        println!("当前tick：{}, liquidity: {}", state.tick, state.liquidity);

        // 获取下一个初始化的tick
        let next_tick = self.get_next_initialized_tick_optimized(state.tick, zero_for_one)?;
        println!("next tick: {:?}", next_tick);

        let sqrt_price_next_tick = if let Some(tick) = next_tick {
            fixed_point_math::tick_to_sqrt_price(tick)?
        } else {
            sqrt_price_limit
        };

        // 确定目标价格
        let sqrt_price_target = if zero_for_one {
            sqrt_price_next_tick.max(sqrt_price_limit)
        } else {
            sqrt_price_next_tick.min(sqrt_price_limit)
        };

        println!(
            "高级交换: sqrt_price_target={}, sqrt_price_next_tick={}",
            sqrt_price_target, sqrt_price_next_tick
        );

        // 检查流动性是否足够
        if state.liquidity == 0 {
            println!("流动性为0，无法进行交换");
            return Ok(SwapStepResult {
                sqrt_price_next: state.sqrt_price,
                amount_in: 0,
                amount_out: 0,
                fee_amount: 0,
                tick_next: next_tick.unwrap_or(state.tick),
            });
        }

        // 使用高精度数学计算交换
        let swap_computation = swap_math::compute_swap_step(
            state.sqrt_price,
            sqrt_price_target,
            state.liquidity,
            state.amount_specified_remaining,
            config.fee_rate,
            zero_for_one,
            config.exact_in,
        )?;

        println!(
            "  交换结果: amount_in={}, amount_out={}, fee_amount={}, sqrt_price_next={}",
            swap_computation.amount_in,
            swap_computation.amount_out,
            swap_computation.fee_amount,
            swap_computation.sqrt_price_next
        );

        Ok(SwapStepResult {
            sqrt_price_next: swap_computation.sqrt_price_next,
            amount_in: swap_computation.amount_in,
            amount_out: swap_computation.amount_out,
            fee_amount: swap_computation.fee_amount,
            tick_next: next_tick.unwrap_or(state.tick),
        })
    }

    /// 优化的下一个初始化tick查找（模仿官方实现）
    fn get_next_initialized_tick_optimized(
        &self,
        current_tick: i32,
        zero_for_one: bool,
    ) -> Result<Option<i32>> {
        // 模仿官方的 tick 查找逻辑：
        // 1. 首先在当前 tick array 中查找
        if let Some(next_tick) = self.find_next_tick_in_current_array(current_tick, zero_for_one)? {
            return Ok(Some(next_tick));
        }

        // 2. 如果当前 tick array 中没有，在相邻 tick arrays 中查找
        if let Some(next_tick) =
            self.find_next_tick_in_adjacent_arrays(current_tick, zero_for_one)?
        {
            return Ok(Some(next_tick));
        }

        // 3. 最后使用位图查找（如果可用）
        if let Some(next_tick) = self.find_next_tick_with_bitmap(current_tick, zero_for_one)? {
            return Ok(Some(next_tick));
        }

        // 4. 回退到线性搜索（用于调试或位图不可用时）
        self.find_next_tick_linear(current_tick, zero_for_one)
    }

    /// 使用位图查找下一个初始化的tick
    fn find_next_tick_with_bitmap(
        &self,
        current_tick: i32,
        zero_for_one: bool,
    ) -> Result<Option<i32>> {
        println!("使用位图查找下一个初始化的tick");
        let tick_spacing = self.pool_state.tick_spacing as i32;

        // 使用主位图
        if let Some(tick) = self.search_in_main_bitmap(current_tick, zero_for_one, tick_spacing) {
            return Ok(Some(tick));
        }

        // 如果有扩展位图，也搜索扩展位图
        if let Some(bitmap_ext) = &self.bitmap_extension {
            if let Some(tick) =
                self.search_in_extended_bitmap(current_tick, zero_for_one, tick_spacing, bitmap_ext)
            {
                return Ok(Some(tick));
            }
        }

        Ok(None)
    }

    /// 在主位图中搜索
    fn search_in_main_bitmap(
        &self,
        current_tick: i32,
        zero_for_one: bool,
        tick_spacing: i32,
    ) -> Option<i32> {
        let bitmap = &self.pool_state.tick_array_bitmap;

        // 计算当前tick在位图中的位置，使用安全的方法
        let (word_pos, bit_pos) = bitmap_utils::position(current_tick, tick_spacing);
        let compressed = current_tick / tick_spacing;
        let word_index_i16 = word_pos + 8; // 偏移到数组中心

        println!(
            "位图计算详情: tick={}, tick_spacing={}, compressed={}, word_pos={}, bit_pos={}",
            current_tick, tick_spacing, compressed, word_pos, bit_pos
        );

        // 检查索引是否在有效范围内
        if word_index_i16 < 0 || word_index_i16 >= 16 {
            println!("tick {} word_pos {} 超出位图范围", current_tick, word_pos);
            return None;
        }

        let word_index = word_index_i16 as usize;

        println!(
            "current_tick: {}, word_index: {}, zero_for_one: {}",
            current_tick, word_index, zero_for_one
        );
        println!("当前字的位图值: 0x{:016x}", bitmap[word_index]);

        // 在当前字中查找
        // 需要传递当前tick所在的基础位置，而不是current_tick本身
        let tick_array_size = tick_spacing * 60;
        let current_tick_array_index = if current_tick >= 0 {
            current_tick / tick_array_size
        } else {
            (current_tick - (tick_array_size - 1)) / tick_array_size
        };
        let word_base_tick_array_index = (word_index as i32 - 8) * 64;
        let base_tick = word_base_tick_array_index * tick_array_size;

        if let Some((next_tick, _)) = bitmap_utils::next_initialized_tick_within_one_word(
            bitmap[word_index],
            current_tick, // 传递实际的current_tick用于比较
            tick_spacing,
            !zero_for_one,
        ) {
            println!("在当前字中找到下一个tick: {}", next_tick);
            return Some(next_tick);
        }

        println!("在当前字中未找到，开始搜索相邻字");

        // 在相邻字中查找
        let search_range = if zero_for_one {
            (0..word_index).rev().collect::<Vec<_>>()
        } else {
            ((word_index + 1)..16).collect::<Vec<_>>()
        };

        println!("搜索范围: {:?}", search_range);

        for &idx in &search_range {
            println!("检查索引 {}, 位图值: 0x{:016x}", idx, bitmap[idx]);
            if bitmap[idx] != 0 {
                // 修正基础tick计算：每个字代表64个tick_array，每个tick_array包含60个tick
                let tick_array_size = tick_spacing * 60;
                let word_base_tick_array_index = (idx as i32 - 8) * 64;
                let base_tick = word_base_tick_array_index * tick_array_size;
                println!(
                    "索引 {} 的基础tick: {} (word_base_tick_array_index: {})",
                    idx, base_tick, word_base_tick_array_index
                );

                if let Some((next_tick, _)) = bitmap_utils::next_initialized_tick_within_one_word(
                    bitmap[idx],
                    current_tick, // 传递实际的current_tick用于比较
                    tick_spacing,
                    !zero_for_one,
                ) {
                    println!("在索引 {} 中找到下一个tick: {}", idx, next_tick);

                    // 验证找到的tick是否符合方向要求
                    let is_valid = if zero_for_one {
                        next_tick < current_tick
                    } else {
                        next_tick > current_tick
                    };

                    if is_valid {
                        return Some(next_tick);
                    } else {
                        println!(
                            "找到的tick {} 不符合方向要求 (zero_for_one: {})",
                            next_tick, zero_for_one
                        );
                    }
                }
            }
        }

        println!("在所有字中都未找到合适的tick");
        None
    }

    /// 在扩展位图中搜索
    fn search_in_extended_bitmap(
        &self,
        current_tick: i32,
        zero_for_one: bool,
        tick_spacing: i32,
        bitmap_ext: &TickArrayBitmapExtension,
    ) -> Option<i32> {
        let bitmap_array = if current_tick >= 0 {
            &bitmap_ext.positive_tick_array_bitmap
        } else {
            &bitmap_ext.negative_tick_array_bitmap
        };

        // 简化的扩展位图搜索实现
        for (i, row) in bitmap_array.iter().enumerate() {
            for (j, &word) in row.iter().enumerate() {
                if word != 0 {
                    let base_tick = (i as i32 * 8 + j as i32) * 256 * tick_spacing;
                    let base_tick = if current_tick < 0 {
                        -base_tick
                    } else {
                        base_tick
                    };

                    if let Some((next_tick, _)) =
                        bitmap_utils::next_initialized_tick_within_one_word(
                            word,
                            base_tick,
                            tick_spacing,
                            !zero_for_one,
                        )
                    {
                        if (zero_for_one && next_tick < current_tick)
                            || (!zero_for_one && next_tick > current_tick)
                        {
                            return Some(next_tick);
                        }
                    }
                }
            }
        }

        None
    }

    /// 在当前 tick array 中查找下一个初始化的 tick
    fn find_next_tick_in_current_array(
        &self,
        current_tick: i32,
        zero_for_one: bool,
    ) -> Result<Option<i32>> {
        println!("查找当前 tick array 中的下一个初始化的 tick");
        let tick_array_start = self.get_tick_array_start_index(current_tick);

        if let Some(tick_array) = self.tick_arrays.get(&tick_array_start) {
            let mut best_tick: Option<i32> = None;

            for tick_info in &tick_array.ticks {
                if tick_info.liquidity_gross > 0 {
                    let is_valid = if zero_for_one {
                        tick_info.tick < current_tick
                    } else {
                        tick_info.tick > current_tick
                    };

                    if is_valid {
                        match best_tick {
                            None => best_tick = Some(tick_info.tick),
                            Some(current_best) => {
                                if zero_for_one && tick_info.tick > current_best {
                                    best_tick = Some(tick_info.tick);
                                } else if !zero_for_one && tick_info.tick < current_best {
                                    best_tick = Some(tick_info.tick);
                                }
                            }
                        }
                    }
                }
            }

            return Ok(best_tick);
        }

        Ok(None)
    }

    /// 在相邻 tick arrays 中查找下一个初始化的 tick
    fn find_next_tick_in_adjacent_arrays(
        &self,
        current_tick: i32,
        zero_for_one: bool,
    ) -> Result<Option<i32>> {
        println!("查找相邻 tick arrays 中的下一个初始化的 tick");
        let tick_spacing = self.pool_state.tick_spacing as i32;
        let array_size = tick_spacing * 60; // Raydium CLMM每个tick array包含60个tick
        let current_array_start = self.get_tick_array_start_index(current_tick);

        // 搜索相邻的 3 个 tick arrays（官方通常搜索的范围）
        let search_range = if zero_for_one {
            // 向左搜索（tick 减少）
            vec![
                current_array_start - array_size,
                current_array_start - 2 * array_size,
                current_array_start - 3 * array_size,
            ]
        } else {
            // 向右搜索（tick 增加）
            vec![
                current_array_start + array_size,
                current_array_start + 2 * array_size,
                current_array_start + 3 * array_size,
            ]
        };

        for &array_start in &search_range {
            if let Some(tick_array) = self.tick_arrays.get(&array_start) {
                for tick_info in &tick_array.ticks {
                    if tick_info.liquidity_gross > 0 {
                        let is_valid = if zero_for_one {
                            tick_info.tick < current_tick
                        } else {
                            tick_info.tick > current_tick
                        };

                        if is_valid {
                            return Ok(Some(tick_info.tick));
                        }
                    }
                }
            }
        }

        Ok(None)
    }

    /// 线性搜索下一个初始化的tick (回退方法)
    fn find_next_tick_linear(&self, current_tick: i32, zero_for_one: bool) -> Result<Option<i32>> {
        let _tick_spacing = self.pool_state.tick_spacing as i32;
        println!("线性搜索下一个初始化的 tick");
        let mut best_tick: Option<i32> = None;

        for tick_array in self.tick_arrays.values() {
            for tick_info in &tick_array.ticks {
                if tick_info.liquidity_gross > 0 {
                    let is_valid = if zero_for_one {
                        tick_info.tick < current_tick
                    } else {
                        tick_info.tick > current_tick
                    };

                    if is_valid {
                        match best_tick {
                            None => best_tick = Some(tick_info.tick),
                            Some(current_best) => {
                                if zero_for_one && tick_info.tick > current_best {
                                    best_tick = Some(tick_info.tick);
                                } else if !zero_for_one && tick_info.tick < current_best {
                                    best_tick = Some(tick_info.tick);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(best_tick)
    }

    /// 计算价格影响 (以基点为单位)
    fn calculate_price_impact(&self, initial_sqrt_price: u128, final_sqrt_price: u128) -> u32 {
        if initial_sqrt_price == 0 {
            return 0;
        }

        let price_change = if final_sqrt_price > initial_sqrt_price {
            final_sqrt_price - initial_sqrt_price
        } else {
            initial_sqrt_price - final_sqrt_price
        };

        // 避免除零和极小值的影响
        if price_change == 0 || initial_sqrt_price == final_sqrt_price {
            return 0;
        }

        // 计算百分比变化并转换为基点
        let impact_ratio = (price_change as f64) / (initial_sqrt_price as f64);
        let impact_bps = (impact_ratio * 10000.0) as u32;

        println!(
            "价格影响计算: 初始={}, 最终={}, 变化={}, 影响={}bps",
            initial_sqrt_price, final_sqrt_price, price_change, impact_bps
        );

        impact_bps
    }

    /// 获取池的流动性分布信息
    pub fn get_liquidity_distribution(&self) -> Vec<(i32, u128)> {
        let mut distribution = Vec::new();

        for tick_array in self.tick_arrays.values() {
            for tick_info in &tick_array.ticks {
                if tick_info.liquidity_gross > 0 {
                    distribution.push((tick_info.tick, tick_info.liquidity_gross));
                }
            }
        }

        distribution.sort_by_key(|&(tick, _)| tick);
        distribution
    }

    /// 获取池统计信息
    pub fn get_pool_stats(&self) -> PoolStats {
        let total_ticks = self
            .tick_arrays
            .values()
            .map(|array| array.ticks.len())
            .sum();

        let initialized_ticks = self
            .tick_arrays
            .values()
            .map(|array| array.ticks.iter().filter(|t| t.liquidity_gross > 0).count())
            .sum();

        PoolStats {
            total_liquidity: self.pool_state.liquidity,
            current_tick: self.pool_state.tick_current,
            current_price: self.get_current_price(),
            total_ticks,
            initialized_ticks,
            tick_arrays_count: self.tick_arrays.len(),
            tvl_token_0: 0.0, // 需要根据价格计算
            tvl_token_1: 0.0, // 需要根据价格计算
        }
    }

    /// 获取池状态引用
    pub fn get_state(&self) -> &RaydiumClmmPoolState {
        &self.pool_state
    }

    /// 验证交换参数
    pub fn validate_swap_params(
        &self,
        input_amount: u64,
        zero_for_one: bool,
        sqrt_price_limit: Option<u128>,
    ) -> Result<()> {
        // 验证输入数量
        if input_amount == 0 {
            return Err(EchoesError::InvalidInput(
                "Input amount cannot be zero".to_string(),
            ));
        }

        // 验证流动性
        if self.pool_state.liquidity == 0 {
            return Err(EchoesError::InvalidInput(
                "Pool has no liquidity".to_string(),
            ));
        }

        // 验证价格限制
        if let Some(limit) = sqrt_price_limit {
            if limit < math_constants::MIN_SQRT_PRICE || limit > math_constants::MAX_SQRT_PRICE {
                return Err(EchoesError::InvalidInput(
                    "Price limit out of bounds".to_string(),
                ));
            }

            // 检查价格限制方向是否正确
            if (zero_for_one && limit >= self.pool_state.sqrt_price_x64)
                || (!zero_for_one && limit <= self.pool_state.sqrt_price_x64)
            {
                return Err(EchoesError::InvalidInput(
                    "Invalid price limit direction".to_string(),
                ));
            }
        }

        // 验证池状态是否活跃
        if self.pool_state.status != 1 {
            return Err(EchoesError::InvalidInput("Pool is not active".to_string()));
        }

        Ok(())
    }

    /// 估算最大交换量
    pub fn estimate_max_swap_amount(
        &self,
        zero_for_one: bool,
        sqrt_price_limit: Option<u128>,
    ) -> Result<u64> {
        // 检查流动性
        if self.pool_state.liquidity == 0 {
            return Ok(0);
        }

        let current_sqrt_price = self.pool_state.sqrt_price_x64;
        let liquidity = self.pool_state.liquidity;

        // 设置合理的价格限制：基于当前价格的合理滑点范围
        let sqrt_price_limit = sqrt_price_limit.unwrap_or({
            if zero_for_one {
                // Token0 -> Token1，价格下降，设置5%滑点作为下限
                let slippage_factor = 0.95_f64.sqrt(); // 5%价格滑点对应的sqrt_price因子
                (current_sqrt_price as f64 * slippage_factor) as u128
            } else {
                // Token1 -> Token0，价格上升，设置5%滑点作为上限
                let slippage_factor = 1.05_f64.sqrt(); // 5%价格滑点对应的sqrt_price因子
                (current_sqrt_price as f64 * slippage_factor) as u128
            }
        });

        println!("估算最大交换量:");
        println!("  当前 sqrt_price: {}", current_sqrt_price);
        println!("  限制 sqrt_price: {}", sqrt_price_limit);
        println!("  流动性: {}", liquidity);

        // 使用 Uniswap v3 标准公式计算最大交换量
        let max_amount = if zero_for_one {
            // Token0 -> Token1: 使用公式 Δx = L * (1/√P_new - 1/√P_current)
            // 注意：这里的流动性 L 已经是以 token 为单位，不需要额外的 Q64.64 缩放
            let current_sqrt_price_f64 = current_sqrt_price as f64;
            let limit_sqrt_price_f64 = sqrt_price_limit as f64;

            // 计算 1/√P 的差值（以Q64.64格式）
            let q64_64 = (1u128 << 64) as f64;
            let inv_current = q64_64 / current_sqrt_price_f64;
            let inv_limit = q64_64 / limit_sqrt_price_f64;
            // 对于 Token0 -> Token1，价格下降，所以 1/√P_limit > 1/√P_current
            let delta_inv_sqrt_price = inv_limit - inv_current;

            println!("  Token0->Token1 计算:");
            println!("    1/√P_current: {:.6}", inv_current);
            println!("    1/√P_limit: {:.6}", inv_limit);
            println!("    差值: {:.6}", delta_inv_sqrt_price);

            // Δx = L * (1/√P_current - 1/√P_limit)
            let max_amount_f64 = (liquidity as f64) * delta_inv_sqrt_price;
            max_amount_f64.max(0.0) as u64
        } else {
            // Token1 -> Token0: 使用公式 Δy = L * (√P_new - √P_current)
            let delta_sqrt_price = (sqrt_price_limit as f64) - (current_sqrt_price as f64);

            println!("  Token1->Token0 计算:");
            println!("    √P_limit - √P_current: {:.6}", delta_sqrt_price);

            // 转换为实际token单位：除以Q64.64
            let q64_64 = (1u128 << 64) as f64;
            let max_amount_f64 = (liquidity as f64) * delta_sqrt_price / q64_64;
            max_amount_f64.max(0.0) as u64
        };

        println!("  原始最大量: {}", max_amount);

        // 应用安全系数，避免因为tick边界导致的计算误差
        let safe_amount = (max_amount as f64 * 0.90) as u64; // 90%安全边际
        let final_amount = safe_amount.max(1000); // 最少1000单位，避免过小的值

        println!("  安全系数后: {}", safe_amount);
        println!("  最终结果: {}", final_amount);

        Ok(final_amount)
    }
}

/// 实现 PoolManager trait for RaydiumClmmPoolManager
impl PoolManager for RaydiumClmmPoolManager {
    type State = RaydiumClmmPoolState;

    fn get_state(&self) -> &Self::State {
        &self.pool_state
    }

    fn get_current_price(&self) -> Result<f64> {
        self.pool_state.current_price()
    }

    fn estimate_swap_output(
        &self,
        input_amount: u64,
        direction: SwapDirection,
        max_price_impact: Option<f64>,
    ) -> Result<SwapEstimation> {
        // 转换方向参数
        let zero_for_one = match direction {
            SwapDirection::AToB => true,  // Token0 -> Token1
            SwapDirection::BToA => false, // Token1 -> Token0
        };

        // 调用现有的估算方法
        let swap_result = self.estimate_swap_output(input_amount, zero_for_one, None)?;

        // 转换为统一的 SwapEstimation 格式
        Ok(SwapEstimation {
            input_amount,
            output_amount: swap_result.amount_out,
            minimum_output: None,
            price_impact: swap_result.price_impact_bps as f64 / 100.0,
            fee_amount: swap_result.fee_amount,
            price_after: swap_result.sqrt_price_after as f64 / (1u128 << 64) as f64,
            direction,
        })
    }

    fn get_liquidity_info(&self) -> LiquidityInfo {
        let stats = self.get_pool_stats();

        LiquidityInfo {
            total_liquidity: stats.total_liquidity,
            active_ranges: stats.tick_arrays_count,
            price_range: (0.0, f64::MAX), // 简化实现
            distribution: Vec::new(),     // 简化实现
        }
    }

    fn update_pool_state(&mut self, new_state: Self::State) -> Result<()> {
        self.pool_state = new_state;
        Ok(())
    }
}
