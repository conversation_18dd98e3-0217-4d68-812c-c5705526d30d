//! Raydium CPMM 池管理器实现
//!
//! 实现 Raydium CPMM 特定的池管理逻辑

use super::pool::RaydiumCpmmPool;
use super::types::*;
use crate::core::{LiquidityInfo as CoreLiquidityInfo, PoolManager, SwapDirection, SwapEstimation as CoreSwapEstimation};
use shared::anchor_types::raydium_cpmm::{AmmConfig, PoolState as RaydiumCpmmPoolState};
use shared::{EchoesError, Result};
use solana_sdk::pubkey::Pubkey;

/// Raydium CPMM 池管理器
/// 负责管理 Raydium CPMM 池的状态和计算交换
#[derive(Debug, Clone)]
pub struct RaydiumCpmmPoolManager {
    /// 池状态
    pub pool_state: RaydiumCpmmPool,
}

impl RaydiumCpmmPoolManager {
    /// 创建新的池管理器
    pub fn new(pool_state: RaydiumCpmmPool) -> Self {
        Self { pool_state }
    }

    /// 从账号数据创建池管理器
    pub fn from_account_data(
        address: Pubkey,
        pool_data: RaydiumCpmmPoolState,
        amm_config: &AmmConfig,
        token0_reserve: Option<u64>,
        token1_reserve: Option<u64>,
    ) -> Result<Self> {
        let mut pool_state = RaydiumCpmmPool::from_pool_data(address, pool_data, amm_config);
        
        // 如果提供了储备数据，则更新
        if let (Some(token0), Some(token1)) = (token0_reserve, token1_reserve) {
            // 使用默认精度，实际应用中应该从代币信息获取
            pool_state.update_reserves(token0, token1, 9, 6);
        }

        // 验证池状态
        pool_state.validate()?;

        Ok(Self::new(pool_state))
    }

    /// 更新储备量
    pub fn update_reserves(&mut self, token0_reserve: u64, token1_reserve: u64, token0_decimals: u8, token1_decimals: u8) {
        self.pool_state.update_reserves(token0_reserve, token1_reserve, token0_decimals, token1_decimals);
    }

    /// 获取 token0 储备
    pub fn get_token0_reserve(&self) -> u64 {
        self.pool_state.get_token0_reserve()
    }

    /// 获取 token1 储备
    pub fn get_token1_reserve(&self) -> u64 {
        self.pool_state.get_token1_reserve()
    }

    /// 获取费用信息
    pub fn get_fee_info(&self) -> &FeeInfo {
        &self.pool_state.fee_info
    }

    /// 检查是否为指定代币对的池
    pub fn is_pool_for_pair(&self, token_a: &Pubkey, token_b: &Pubkey) -> bool {
        self.pool_state.matches_token_pair(token_a, token_b)
    }

    /// 获取最佳路由（对于单一池子，路由就是直接交换）
    pub fn get_best_route(
        &self,
        input_mint: &Pubkey,
        output_mint: &Pubkey,
        input_amount: u64,
    ) -> Result<SwapEstimation> {
        // 验证代币对
        if !self.pool_state.matches_token_pair(input_mint, output_mint) {
            return Err(EchoesError::InvalidInput(
                "Token pair not supported by this pool".to_string(),
            ));
        }

        // 确定交换方向
        let token0_to_token1 = self.pool_state.token0_mint == *input_mint;
        
        if token0_to_token1 {
            self.pool_state.estimate_swap_token0_to_token1(input_amount)
        } else {
            self.pool_state.estimate_swap_token1_to_token0(input_amount)
        }
    }

    /// 获取流动性深度信息
    pub fn get_liquidity_depth(&self, levels: usize) -> Vec<(f64, u64, u64)> {
        // 简化实现：返回当前价格层级
        // 实际实现可能需要模拟不同输入量的价格影响
        let mut depth = Vec::new();
        let current_price = self.pool_state.get_current_price().unwrap_or(0.0);
        
        for i in 0..levels {
            let price_offset = (i as f64) * 0.01; // 1% 间隔
            let bid_price = current_price * (1.0 - price_offset);
            let ask_price = current_price * (1.0 + price_offset);
            
            // 简化的流动性估算
            let liquidity = if i == 0 {
                self.pool_state.get_token0_reserve().min(self.pool_state.get_token1_reserve())
            } else {
                (self.pool_state.get_token0_reserve().min(self.pool_state.get_token1_reserve())) / (i as u64 + 1)
            };
            
            depth.push((bid_price, liquidity, liquidity));
            if bid_price != ask_price {
                depth.push((ask_price, liquidity, liquidity));
            }
        }
        
        depth.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap());
        depth
    }

    /// 检查池的健康状态
    pub fn check_health(&self) -> Result<()> {
        // 检查基本状态
        self.pool_state.validate()?;

        // 检查流动性
        if self.pool_state.get_token0_reserve() < math_constants::MIN_LIQUIDITY 
            || self.pool_state.get_token1_reserve() < math_constants::MIN_LIQUIDITY {
            return Err(EchoesError::InvalidState("Insufficient liquidity".to_string()));
        }

        // 检查价格合理性
        let price = self.pool_state.get_current_price()?;
        if !price.is_finite() || price <= 0.0 {
            return Err(EchoesError::InvalidState("Invalid price".to_string()));
        }

        // 检查池状态标志
        if !self.pool_state.can_swap() {
            return Err(EchoesError::InvalidState("Pool swapping is disabled".to_string()));
        }

        Ok(())
    }

    /// 获取池的摘要信息
    pub fn get_pool_summary(&self) -> String {
        format!(
            "Raydium CPMM Pool {}: {}/{} (Price: {:.6}, Token0: {}, Token1: {})",
            self.pool_state.address,
            self.pool_state.token0_mint,
            self.pool_state.token1_mint,
            self.pool_state.get_current_price().unwrap_or(0.0),
            utils::format_amount(self.pool_state.get_token0_reserve(), self.pool_state.get_token0_decimals()),
            utils::format_amount(self.pool_state.get_token1_reserve(), self.pool_state.get_token1_decimals()),
        )
    }

    /// 估算给定输出金额所需的输入金额
    pub fn estimate_input_for_output(
        &self,
        output_amount: u64,
        token0_to_token1: bool,
    ) -> Result<u64> {
        self.pool_state.estimate_input_for_output(output_amount, token0_to_token1)
    }

    /// 获取池状态信息
    pub fn get_pool_status(&self) -> (bool, bool, bool) {
        (
            self.pool_state.can_deposit(),
            self.pool_state.can_withdraw(),
            self.pool_state.can_swap(),
        )
    }

    /// 获取协议费用累积
    pub fn get_protocol_fees(&self) -> (u64, u64) {
        (self.pool_state.protocol_fees_token0, self.pool_state.protocol_fees_token1)
    }

    /// 获取基金费用累积
    pub fn get_fund_fees(&self) -> (u64, u64) {
        (self.pool_state.fund_fees_token0, self.pool_state.fund_fees_token1)
    }

    /// 计算价格范围内的流动性
    pub fn calculate_liquidity_in_range(&self, price_lower: f64, price_upper: f64) -> u64 {
        let current_price = self.pool_state.get_current_price().unwrap_or(0.0);
        
        // 对于 CPMM，所有流动性都在当前价格点
        if current_price >= price_lower && current_price <= price_upper {
            // 返回以较小储备为基准的流动性
            self.pool_state.get_token0_reserve().min(self.pool_state.get_token1_reserve())
        } else {
            0
        }
    }

    /// 模拟大额交换的价格影响
    pub fn simulate_large_swap(&self, input_amount: u64, token0_to_token1: bool) -> Result<Vec<(u64, f64, f64)>> {
        let mut results = Vec::new();
        let chunk_size = input_amount / 10; // 分成10份模拟
        let mut cumulative_input = 0u64;
        let mut current_token0_reserve = self.pool_state.get_token0_reserve();
        let mut current_token1_reserve = self.pool_state.get_token1_reserve();

        for i in 1..=10 {
            let chunk_input = if i == 10 {
                input_amount - cumulative_input // 最后一块包含剩余部分
            } else {
                chunk_size
            };

            cumulative_input += chunk_input;

            // 计算这一块的输出
            let (reserve_in, reserve_out) = if token0_to_token1 {
                (current_token0_reserve, current_token1_reserve)
            } else {
                (current_token1_reserve, current_token0_reserve)
            };

            let fee_amount = self.pool_state.fee_info.calculate_fee(chunk_input)?;
            let input_after_fee = chunk_input.saturating_sub(fee_amount);
            let output = utils::calculate_cpmm_output(input_after_fee, reserve_in, reserve_out)?;

            // 更新储备
            if token0_to_token1 {
                current_token0_reserve += input_after_fee;
                current_token1_reserve = current_token1_reserve.saturating_sub(output);
            } else {
                current_token1_reserve += input_after_fee;
                current_token0_reserve = current_token0_reserve.saturating_sub(output);
            }

            // 计算当前价格
            let current_price = utils::calculate_price(
                current_token0_reserve,
                current_token1_reserve,
                self.pool_state.get_token0_decimals(),
                self.pool_state.get_token1_decimals(),
            );

            // 计算价格影响
            let original_price = self.pool_state.get_current_price()?;
            let price_impact = utils::calculate_price_impact(original_price, current_price);

            results.push((cumulative_input, current_price, price_impact * 100.0));
        }

        Ok(results)
    }
}

impl PoolManager for RaydiumCpmmPoolManager {
    type State = RaydiumCpmmPool;

    fn get_state(&self) -> &Self::State {
        &self.pool_state
    }

    fn get_current_price(&self) -> Result<f64> {
        self.pool_state.get_current_price()
    }

    fn estimate_swap_output(
        &self,
        input_amount: u64,
        direction: SwapDirection,
        max_price_impact: Option<f64>,
    ) -> Result<CoreSwapEstimation> {
        let estimation = match direction {
            SwapDirection::AToB => {
                // Token0 -> Token1
                self.pool_state.estimate_swap_token0_to_token1(input_amount)?
            }
            SwapDirection::BToA => {
                // Token1 -> Token0
                self.pool_state.estimate_swap_token1_to_token0(input_amount)?
            }
        };

        // 检查价格影响限制
        if let Some(max_impact) = max_price_impact {
            if estimation.price_impact > max_impact {
                return Err(EchoesError::InvalidInput(
                    format!("Price impact {:.2}% exceeds maximum {:.2}%", 
                           estimation.price_impact, max_impact)
                ));
            }
        }

        // 计算交换后价格
        let price_after = if direction == SwapDirection::AToB {
            // Token0 -> Token1，计算新价格
            let new_token0_reserve = self.pool_state.get_token0_reserve() + input_amount;
            let new_token1_reserve = self.pool_state.get_token1_reserve()
                .checked_sub(estimation.output_amount)
                .unwrap_or(self.pool_state.get_token1_reserve());
            utils::calculate_price(new_token0_reserve, new_token1_reserve, 
                                 self.pool_state.get_token0_decimals(), self.pool_state.get_token1_decimals())
        } else {
            // Token1 -> Token0，计算新价格
            let new_token1_reserve = self.pool_state.get_token1_reserve() + input_amount;
            let new_token0_reserve = self.pool_state.get_token0_reserve()
                .checked_sub(estimation.output_amount)
                .unwrap_or(self.pool_state.get_token0_reserve());
            utils::calculate_price(new_token0_reserve, new_token1_reserve, 
                                 self.pool_state.get_token0_decimals(), self.pool_state.get_token1_decimals())
        };

        // 转换为核心类型
        Ok(CoreSwapEstimation {
            input_amount: estimation.input_amount,
            output_amount: estimation.output_amount,
            minimum_output: Some(estimation.output_amount), // 简化处理
            price_impact: estimation.price_impact,
            fee_amount: estimation.fee_amount,
            price_after,
            direction,
        })
    }

    fn get_liquidity_info(&self) -> CoreLiquidityInfo {
        let current_price = self.pool_state.get_current_price().unwrap_or(0.0);
        
        // 计算总流动性（token0 + token1 等价值）
        let token0_value = self.pool_state.get_token0_reserve() as u128;
        let token1_value_in_token0 = if current_price > 0.0 {
            (self.pool_state.get_token1_reserve() as f64 / current_price) as u128
        } else {
            0
        };
        let total_liquidity = token0_value + token1_value_in_token0;
        
        // 创建流动性分布（简化为当前价格点）
        let distribution = vec![(
            current_price,
            self.pool_state.get_token0_reserve() as u128,
            self.pool_state.get_token1_reserve() as u128,
        )];
        
        CoreLiquidityInfo {
            total_liquidity,
            active_ranges: 1, // CPMM 只有一个价格点
            price_range: (current_price, current_price),
            distribution,
        }
    }

    fn update_pool_state(&mut self, new_state: Self::State) -> Result<()> {
        // 验证新状态
        new_state.validate()?;
        
        // 更新状态
        self.pool_state = new_state;
        
        Ok(())
    }
}