//! Raydium CPMM 池状态实现
//!
//! 实现 Raydium CPMM 特定的池状态逻辑

use std::collections::HashMap;
use super::types::*;
use crate::core::{PoolState};
use serde::{Deserialize, Serialize};
use shared::anchor_types::raydium_cpmm::{AmmConfig, PoolState as RaydiumCpmmPoolState};
use shared::{EchoesError, Result, TokenMintInfo, DexProtocol, PoolType};
use solana_sdk::pubkey::Pubkey;

/// Raydium CPMM 池状态
/// 表示一个完整的 Raydium CPMM 流动性池
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RaydiumCpmmPool {
    /// 池地址
    pub address: Pubkey,
    /// AMM 配置地址
    pub amm_config: Pubkey,
    /// 池创建者
    pub pool_creator: Pubkey,
    /// Token 0 铸币地址
    pub token0_mint: Pubkey,
    /// Token 1 铸币地址
    pub token1_mint: Pubkey,
    /// Token 0 金库地址
    pub token0_vault: Pubkey,
    /// Token 1 金库地址
    pub token1_vault: Pubkey,
    /// LP 代币铸币地址
    pub lp_mint: Pubkey,
    /// LP 代币精度
    pub lp_decimals: u8,
    /// 观察状态地址
    pub observation_key: Pubkey,
    /// 代币信息映射
    pub token_mint_info: HashMap<Pubkey, TokenMintInfo>,
    /// LP 代币供应量
    pub lp_supply: u64,
    /// 费用信息
    pub fee_info: FeeInfo,
    /// 协议费用 token0
    pub protocol_fees_token0: u64,
    /// 协议费用 token1
    pub protocol_fees_token1: u64,
    /// 基金费用 token0
    pub fund_fees_token0: u64,
    /// 基金费用 token1
    pub fund_fees_token1: u64,
    /// 池状态位标识
    pub status: u8,
    /// 池开放时间
    pub open_time: u64,
    /// 是否活跃
    pub is_active: bool,
    /// 最后更新时间戳
    pub last_updated_slot: u64,
}

impl RaydiumCpmmPool {
    /// 创建新的 Raydium CPMM 池状态
    pub fn new(
        address: Pubkey,
        pool_data: RaydiumCpmmPoolState,
        amm_config: &AmmConfig,
    ) -> Self {
        let fee_info = FeeInfo::new(
            amm_config.trade_fee_rate,
            amm_config.protocol_fee_rate,
            amm_config.fund_fee_rate,
        );

        Self {
            address,
            amm_config: pool_data.amm_config,
            pool_creator: pool_data.pool_creator,
            token0_mint: pool_data.token0_mint,
            token1_mint: pool_data.token1_mint,
            token0_vault: pool_data.token0_vault,
            token1_vault: pool_data.token1_vault,
            lp_mint: pool_data.lp_mint,
            lp_decimals: pool_data.lp_mint_decimals,
            observation_key: pool_data.observation_key,
            token_mint_info: HashMap::new(),
            lp_supply: pool_data.lp_supply,
            fee_info,
            protocol_fees_token0: pool_data.protocol_fees_token0,
            protocol_fees_token1: pool_data.protocol_fees_token1,
            fund_fees_token0: pool_data.fund_fees_token0,
            fund_fees_token1: pool_data.fund_fees_token1,
            status: pool_data.status,
            open_time: pool_data.open_time,
            is_active: Self::is_pool_active(pool_data.status),
            last_updated_slot: 0,
        }
    }

    /// 从池数据和配置创建状态（简化版）
    pub fn from_pool_data(
        address: Pubkey,
        pool_data: RaydiumCpmmPoolState,
        amm_config: &AmmConfig,
    ) -> Self {
        let fee_info = FeeInfo::new(
            amm_config.trade_fee_rate,
            amm_config.protocol_fee_rate,
            amm_config.fund_fee_rate,
        );

        let mut pool = Self {
            address,
            amm_config: pool_data.amm_config,
            pool_creator: pool_data.pool_creator,
            token0_mint: pool_data.token0_mint,
            token1_mint: pool_data.token1_mint,
            token0_vault: pool_data.token0_vault,
            token1_vault: pool_data.token1_vault,
            lp_mint: pool_data.lp_mint,
            lp_decimals: pool_data.lp_mint_decimals,
            observation_key: pool_data.observation_key,
            token_mint_info: HashMap::new(),
            lp_supply: pool_data.lp_supply,
            fee_info,
            protocol_fees_token0: pool_data.protocol_fees_token0,
            protocol_fees_token1: pool_data.protocol_fees_token1,
            fund_fees_token0: pool_data.fund_fees_token0,
            fund_fees_token1: pool_data.fund_fees_token1,
            status: pool_data.status,
            open_time: pool_data.open_time,
            is_active: Self::is_pool_active(pool_data.status),
            last_updated_slot: 0,
        };

        // 初始化 token_mint_info
        let token0_info = TokenMintInfo {
            mint: pool_data.token0_mint,
            decimals: pool_data.mint0_decimals,
            token_vault: pool_data.token0_vault,
            token_balance: 0, // 需要从金库账户数据获取
        };
        let token1_info = TokenMintInfo {
            mint: pool_data.token1_mint,
            decimals: pool_data.mint1_decimals,
            token_vault: pool_data.token1_vault,
            token_balance: 0, // 需要从金库账户数据获取
        };

        pool.token_mint_info.insert(pool_data.token0_mint, token0_info);
        pool.token_mint_info.insert(pool_data.token1_mint, token1_info);

        pool
    }

    /// 检查池状态是否活跃
    fn is_pool_active(status: u8) -> bool {
        // bit0: 禁用存款 (1), bit1: 禁用提取 (2), bit2: 禁用交换 (4)
        // 如果没有禁用交换，则认为池子是活跃的
        (status & 4) == 0
    }

    /// 更新储备量
    pub fn update_reserves(&mut self, token0_reserve: u64, token1_reserve: u64, token0_decimals: u8, token1_decimals: u8) {
        // 更新 token0_mint 信息
        if let Some(info) = self.token_mint_info.get_mut(&self.token0_mint) {
            info.token_balance = token0_reserve as u128;
            info.decimals = token0_decimals;
        } else {
            let info = TokenMintInfo {
                mint: self.token0_mint,
                decimals: token0_decimals,
                token_vault: self.token0_vault,
                token_balance: token0_reserve as u128,
            };
            self.token_mint_info.insert(self.token0_mint, info);
        }

        // 更新 token1_mint 信息
        if let Some(info) = self.token_mint_info.get_mut(&self.token1_mint) {
            info.token_balance = token1_reserve as u128;
            info.decimals = token1_decimals;
        } else {
            let info = TokenMintInfo {
                mint: self.token1_mint,
                decimals: token1_decimals,
                token_vault: self.token1_vault,
                token_balance: token1_reserve as u128,
            };
            self.token_mint_info.insert(self.token1_mint, info);
        }
    }

    /// 更新代币信息
    pub fn update_token_mint_info(&mut self, mint: Pubkey, info: TokenMintInfo) {
        self.token_mint_info.insert(mint, info);
    }

    /// 批量更新代币信息
    pub fn update_token_mint_infos(&mut self, infos: HashMap<Pubkey, TokenMintInfo>) {
        for (mint, info) in infos {
            self.token_mint_info.insert(mint, info);
        }
    }

    /// 获取代币信息
    pub fn get_token_mint_info(&self, mint: &Pubkey) -> Option<&TokenMintInfo> {
        self.token_mint_info.get(mint)
    }

    /// 获取 token0 信息
    pub fn get_token0_info(&self) -> Option<&TokenMintInfo> {
        self.token_mint_info.get(&self.token0_mint)
    }

    /// 获取 token1 信息
    pub fn get_token1_info(&self) -> Option<&TokenMintInfo> {
        self.token_mint_info.get(&self.token1_mint)
    }

    /// 获取 token0 储备量
    pub fn get_token0_reserve(&self) -> u64 {
        self.get_token0_info()
            .map(|info| info.token_balance as u64)
            .unwrap_or(0)
    }

    /// 获取 token1 储备量
    pub fn get_token1_reserve(&self) -> u64 {
        self.get_token1_info()
            .map(|info| info.token_balance as u64)
            .unwrap_or(0)
    }

    /// 获取 token0 精度
    pub fn get_token0_decimals(&self) -> u8 {
        self.get_token0_info()
            .map(|info| info.decimals)
            .unwrap_or(9) // 默认精度
    }

    /// 获取 token1 精度
    pub fn get_token1_decimals(&self) -> u8 {
        self.get_token1_info()
            .map(|info| info.decimals)
            .unwrap_or(6) // 默认精度
    }

    /// 获取当前价格（token1/token0）
    pub fn get_current_price(&self) -> Result<f64> {
        let token0_reserve = self.get_token0_reserve();
        if token0_reserve == 0 {
            return Err(EchoesError::InvalidState(
                "Token0 reserve is zero".to_string(),
            ));
        }

        Ok(utils::calculate_price(
            token0_reserve,
            self.get_token1_reserve(),
            self.get_token0_decimals(),
            self.get_token1_decimals(),
        ))
    }

    /// 估算交换输出（token0 -> token1）
    pub fn estimate_swap_token0_to_token1(&self, input_amount: u64) -> Result<SwapEstimation> {
        self.estimate_swap_internal(input_amount, true)
    }

    /// 估算交换输出（token1 -> token0）
    pub fn estimate_swap_token1_to_token0(&self, input_amount: u64) -> Result<SwapEstimation> {
        self.estimate_swap_internal(input_amount, false)
    }

    /// 内部交换估算逻辑
    fn estimate_swap_internal(&self, input_amount: u64, token0_to_token1: bool) -> Result<SwapEstimation> {
        if input_amount == 0 {
            return Err(EchoesError::InvalidInput("Input amount cannot be zero".to_string()));
        }

        // 检查流动性
        let token0_reserve = self.get_token0_reserve();
        let token1_reserve = self.get_token1_reserve();
        if token0_reserve == 0 || token1_reserve == 0 {
            return Err(EchoesError::InvalidState("Insufficient liquidity".to_string()));
        }

        // 计算费用
        let fee_amount = self.fee_info.calculate_fee(input_amount)?;
        let input_after_fee = input_amount
            .checked_sub(fee_amount)
            .ok_or_else(|| EchoesError::InvalidInput("Fee exceeds input amount".to_string()))?;

        // 确定储备量
        let (reserve_in, reserve_out) = if token0_to_token1 {
            (token0_reserve, token1_reserve)
        } else {
            (token1_reserve, token0_reserve)
        };

        // 计算输出金额
        let output_amount = utils::calculate_cpmm_output(input_after_fee, reserve_in, reserve_out)?;

        // 计算价格影响
        let old_price = self.get_current_price()?;
        let new_reserve_in = reserve_in + input_after_fee;
        let new_reserve_out = reserve_out
            .checked_sub(output_amount)
            .ok_or_else(|| EchoesError::InvalidState("Output exceeds available liquidity".to_string()))?;

        let (new_token0_reserve, new_token1_reserve) = if token0_to_token1 {
            (new_reserve_in, new_reserve_out)
        } else {
            (new_reserve_out, new_reserve_in)
        };

        let new_price = utils::calculate_price(
            new_token0_reserve,
            new_token1_reserve,
            self.get_token0_decimals(),
            self.get_token1_decimals(),
        );

        let price_impact = utils::calculate_price_impact(old_price, new_price);

        // 检查价格影响是否过大
        if price_impact > math_constants::MAX_SLIPPAGE {
            return Err(EchoesError::InvalidInput("Excessive price impact".to_string()));
        }

        // 分配费用
        let protocol_fee_amount = self.fee_info.calculate_protocol_fee(fee_amount)?;
        let fund_fee_amount = self.fee_info.calculate_fund_fee(fee_amount)?;

        Ok(SwapEstimation {
            input_amount,
            output_amount,
            fee_amount,
            protocol_fee_amount,
            fund_fee_amount,
            price_impact: price_impact * 100.0, // 转换为百分比
            slippage: price_impact * 100.0, // 简化处理，实际应该考虑容忍度
        })
    }

    /// 获取流动性信息
    pub fn get_liquidity_info(&self) -> LiquidityInfo {
        let total_liquidity_token0 = self.get_token0_reserve();
        let total_liquidity_token1 = self.get_token1_reserve();
        let current_price = self.get_current_price().unwrap_or(0.0);

        LiquidityInfo {
            total_liquidity_token0,
            total_liquidity_token1,
            lp_count: 0, // 简化处理，实际需要跟踪 LP 持有者
            current_price,
        }
    }

    /// 获取价格信息
    pub fn get_price_info(&self) -> Result<PriceInfo> {
        let current_price = self.get_current_price()?;
        
        Ok(PriceInfo {
            current_price,
            price_change: None, // 需要历史数据
            price_change_percent: None, // 需要历史数据
        })
    }

    /// 获取池统计信息
    pub fn get_stats(&self) -> RaydiumCpmmStats {
        RaydiumCpmmStats {
            token0_reserve: self.get_token0_reserve(),
            token1_reserve: self.get_token1_reserve(),
            lp_supply: self.lp_supply,
            volume_24h: 0, // 需要外部数据源
            fees_24h: 0,   // 需要外部数据源
            protocol_fees_token0: self.protocol_fees_token0,
            protocol_fees_token1: self.protocol_fees_token1,
        }
    }

    /// 检查代币对是否匹配
    pub fn matches_token_pair(&self, token_a: &Pubkey, token_b: &Pubkey) -> bool {
        (self.token0_mint == *token_a && self.token1_mint == *token_b)
            || (self.token0_mint == *token_b && self.token1_mint == *token_a)
    }

    /// 检查池是否包含指定代币
    pub fn contains_token(&self, token: &Pubkey) -> bool {
        self.token0_mint == *token || self.token1_mint == *token
    }

    /// 获取对应的输出代币
    pub fn get_output_mint(&self, input_mint: &Pubkey) -> Option<Pubkey> {
        if self.token0_mint == *input_mint {
            Some(self.token1_mint)
        } else if self.token1_mint == *input_mint {
            Some(self.token0_mint)
        } else {
            None
        }
    }

    /// 更新最后更新时间戳
    pub fn update_slot(&mut self, slot: u64) {
        self.last_updated_slot = slot;
    }

    /// 验证池状态
    pub fn validate(&self) -> Result<()> {
        if self.token0_mint == Pubkey::default() || self.token1_mint == Pubkey::default() {
            return Err(EchoesError::InvalidState("Invalid mint addresses".to_string()));
        }

        if self.token0_mint == self.token1_mint {
            return Err(EchoesError::InvalidState("Token0 and token1 mints cannot be the same".to_string()));
        }

        if self.fee_info.total_fee_rate > math_constants::FEE_RATE_DENOMINATOR {
            return Err(EchoesError::InvalidState("Fee rate exceeds maximum".to_string()));
        }

        Ok(())
    }

    /// 检查池是否可以进行交换
    pub fn can_swap(&self) -> bool {
        self.is_active && (self.status & 4) == 0 // bit2: 禁用交换
    }

    /// 检查池是否可以进行存款
    pub fn can_deposit(&self) -> bool {
        self.is_active && (self.status & 1) == 0 // bit0: 禁用存款
    }

    /// 检查池是否可以进行提取
    pub fn can_withdraw(&self) -> bool {
        self.is_active && (self.status & 2) == 0 // bit1: 禁用提取
    }

    /// 估算给定输出金额所需的输入金额
    pub fn estimate_input_for_output(
        &self,
        output_amount: u64,
        token0_to_token1: bool,
    ) -> Result<u64> {
        let (reserve_in, reserve_out) = if token0_to_token1 {
            (self.get_token0_reserve(), self.get_token1_reserve())
        } else {
            (self.get_token1_reserve(), self.get_token0_reserve())
        };

        let input_without_fee = utils::calculate_input_for_output(
            output_amount, 
            reserve_in, 
            reserve_out
        )?;

        // 考虑费用：input_with_fee = input_without_fee / (1 - fee_rate)
        let fee_rate = self.fee_info.total_fee_rate as f64 / math_constants::FEE_RATE_DENOMINATOR as f64;
        let input_with_fee = (input_without_fee as f64 / (1.0 - fee_rate)) as u64;

        Ok(input_with_fee)
    }
}

impl PoolState for RaydiumCpmmPool {
    fn pool_address(&self) -> Pubkey {
        self.address
    }

    fn token_pair(&self) -> (Pubkey, Pubkey, u8, u8) {
        (self.token0_mint, self.token1_mint, self.get_token0_decimals(), self.get_token1_decimals())
    }

    fn current_price(&self) -> Result<f64> {
        self.get_current_price()
    }

    fn is_active(&self) -> bool {
        self.is_active && self.can_swap() && self.get_token0_reserve() > 0 && self.get_token1_reserve() > 0
    }

    fn protocol(&self) -> DexProtocol {
        DexProtocol::Raydium
    }

    fn pool_type(&self) -> PoolType {
        PoolType::Cpmm
    }
}