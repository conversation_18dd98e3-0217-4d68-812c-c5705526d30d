//! Raydium CPMM 类型定义
//!
//! 定义 Raydium CPMM 特有的数据结构和类型

use serde::{Deserialize, Serialize};
use shared::{EchoesError, Result};

/// Raydium CPMM 数学常量
pub mod math_constants {
    /// 基础点精度 (10000 = 100%)
    pub const BASIS_POINT_MAX: u64 = 10000;
    /// 最小流动性
    pub const MIN_LIQUIDITY: u64 = 1000;
    /// 最大滑点 (50%)
    pub const MAX_SLIPPAGE: f64 = 0.5;
    /// 费率精度 (1000000 = 100%)
    pub const FEE_RATE_DENOMINATOR: u64 = 1000000;
}

/// Raydium CPMM 池统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RaydiumCpmmStats {
    /// Token 0 金库储备
    pub token0_reserve: u64,
    /// Token 1 金库储备
    pub token1_reserve: u64,
    /// LP 代币供应量
    pub lp_supply: u64,
    /// 24小时交易量
    pub volume_24h: u64,
    /// 24小时费用
    pub fees_24h: u64,
    /// 协议费用 token0
    pub protocol_fees_token0: u64,
    /// 协议费用 token1
    pub protocol_fees_token1: u64,
}

/// 交换估算结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapEstimation {
    /// 输入金额
    pub input_amount: u64,
    /// 输出金额
    pub output_amount: u64,
    /// 费用金额
    pub fee_amount: u64,
    /// 协议费用金额
    pub protocol_fee_amount: u64,
    /// 基金费用金额
    pub fund_fee_amount: u64,
    /// 价格影响（百分比）
    pub price_impact: f64,
    /// 滑点（百分比）
    pub slippage: f64,
}

/// 流动性信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityInfo {
    /// 总流动性（以 token0 计价）
    pub total_liquidity_token0: u64,
    /// 总流动性（以 token1 计价）
    pub total_liquidity_token1: u64,
    /// 流动性提供者数量（LP 代币持有者）
    pub lp_count: u32,
    /// 当前价格
    pub current_price: f64,
}

/// 价格信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceInfo {
    /// 当前价格（token1/token0）
    pub current_price: f64,
    /// 价格变化（相对于上次更新）
    pub price_change: Option<f64>,
    /// 价格变化百分比
    pub price_change_percent: Option<f64>,
}

/// 费用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeeInfo {
    /// 交易费率（万分之一）
    pub trade_fee_rate: u64,
    /// 协议费率（万分之一）
    pub protocol_fee_rate: u64,
    /// 基金费率（万分之一）
    pub fund_fee_rate: u64,
    /// 总费率（万分之一）
    pub total_fee_rate: u64,
}

impl FeeInfo {
    /// 创建新的费用信息
    pub fn new(trade_fee_rate: u64, protocol_fee_rate: u64, fund_fee_rate: u64) -> Self {
        Self {
            trade_fee_rate,
            protocol_fee_rate,
            fund_fee_rate,
            total_fee_rate: trade_fee_rate + protocol_fee_rate + fund_fee_rate,
        }
    }

    /// 计算费用金额
    pub fn calculate_fee(&self, amount: u64) -> Result<u64> {
        amount
            .checked_mul(self.total_fee_rate)
            .and_then(|fee| fee.checked_div(math_constants::FEE_RATE_DENOMINATOR))
            .ok_or_else(|| EchoesError::InvalidInput("Fee calculation overflow".to_string()))
    }

    /// 计算协议费用
    pub fn calculate_protocol_fee(&self, total_fee: u64) -> Result<u64> {
        if self.total_fee_rate == 0 {
            return Ok(0);
        }
        
        total_fee
            .checked_mul(self.protocol_fee_rate)
            .and_then(|fee| fee.checked_div(self.total_fee_rate))
            .ok_or_else(|| EchoesError::InvalidInput("Protocol fee calculation overflow".to_string()))
    }

    /// 计算基金费用
    pub fn calculate_fund_fee(&self, total_fee: u64) -> Result<u64> {
        if self.total_fee_rate == 0 {
            return Ok(0);
        }
        
        total_fee
            .checked_mul(self.fund_fee_rate)
            .and_then(|fee| fee.checked_div(self.total_fee_rate))
            .ok_or_else(|| EchoesError::InvalidInput("Fund fee calculation overflow".to_string()))
    }
}

/// 工具函数
pub mod utils {
    use super::*;

    /// 计算 CPMM 输出金额（恒定乘积公式）
    /// output = (input * reserve_out) / (reserve_in + input)
    pub fn calculate_cpmm_output(
        input_amount: u64,
        reserve_in: u64,
        reserve_out: u64,
    ) -> Result<u64> {
        if reserve_in == 0 || reserve_out == 0 {
            return Err(EchoesError::InvalidInput(
                "Reserve cannot be zero".to_string(),
            ));
        }

        let numerator = (input_amount as u128)
            .checked_mul(reserve_out as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Numerator overflow".to_string()))?;

        let denominator = (reserve_in as u128)
            .checked_add(input_amount as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Denominator overflow".to_string()))?;

        let output = numerator
            .checked_div(denominator)
            .ok_or_else(|| EchoesError::InvalidInput("Division by zero".to_string()))?;

        Ok(output as u64)
    }

    /// 计算价格影响
    /// price_impact = abs(new_price - old_price) / old_price
    pub fn calculate_price_impact(
        old_price: f64,
        new_price: f64,
    ) -> f64 {
        if old_price == 0.0 {
            return 0.0;
        }
        
        ((new_price - old_price) / old_price).abs()
    }

    /// 计算当前价格（基于储备比例）
    /// price = reserve_token1 / reserve_token0
    pub fn calculate_price(
        reserve_token0: u64,
        reserve_token1: u64,
        token0_decimals: u8,
        token1_decimals: u8,
    ) -> f64 {
        if reserve_token0 == 0 {
            return 0.0;
        }

        let token0_adjusted = reserve_token0 as f64 / 10_f64.powi(token0_decimals as i32);
        let token1_adjusted = reserve_token1 as f64 / 10_f64.powi(token1_decimals as i32);

        token1_adjusted / token0_adjusted
    }

    /// 安全的百分比计算
    pub fn safe_percentage(value: f64) -> f64 {
        if value.is_finite() {
            value.max(0.0).min(100.0)
        } else {
            0.0
        }
    }

    /// 格式化金额为可读字符串
    pub fn format_amount(amount: u64, decimals: u8) -> String {
        let adjusted = amount as f64 / 10_f64.powi(decimals as i32);
        
        if adjusted >= 1_000_000_000.0 {
            format!("{:.2}B", adjusted / 1_000_000_000.0)
        } else if adjusted >= 1_000_000.0 {
            format!("{:.2}M", adjusted / 1_000_000.0)
        } else if adjusted >= 1_000.0 {
            format!("{:.2}K", adjusted / 1_000.0)
        } else {
            format!("{:.6}", adjusted)
        }
    }

    /// 计算给定输出所需的输入金额（反向 CPMM 公式）
    pub fn calculate_input_for_output(
        output_amount: u64,
        reserve_in: u64,
        reserve_out: u64,
    ) -> Result<u64> {
        if reserve_out <= output_amount {
            return Err(EchoesError::InvalidInput(
                "Output amount exceeds available liquidity".to_string(),
            ));
        }

        // input = (output * reserve_in) / (reserve_out - output)
        let numerator = (output_amount as u128)
            .checked_mul(reserve_in as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Numerator overflow".to_string()))?;

        let denominator = (reserve_out as u128)
            .checked_sub(output_amount as u128)
            .ok_or_else(|| EchoesError::InvalidInput("Denominator underflow".to_string()))?;

        if denominator == 0 {
            return Err(EchoesError::InvalidInput("Division by zero".to_string()));
        }

        let input = numerator
            .checked_div(denominator)
            .ok_or_else(|| EchoesError::InvalidInput("Division overflow".to_string()))?;

        Ok(input as u64)
    }
}

/// Raydium CPMM 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RaydiumCpmmError {
    /// 流动性不足
    InsufficientLiquidity,
    /// 价格影响过大
    ExcessivePriceImpact,
    /// 无效的代币对
    InvalidTokenPair,
    /// 无效的金额
    InvalidAmount,
    /// 池子不存在
    PoolNotFound,
    /// 计算溢出
    CalculationOverflow,
    /// 滑点过大
    ExcessiveSlippage,
    /// 池子状态无效
    InvalidPoolStatus,
    /// 费率配置无效
    InvalidFeeConfig,
}

impl std::fmt::Display for RaydiumCpmmError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RaydiumCpmmError::InsufficientLiquidity => write!(f, "Insufficient liquidity"),
            RaydiumCpmmError::ExcessivePriceImpact => write!(f, "Excessive price impact"),
            RaydiumCpmmError::InvalidTokenPair => write!(f, "Invalid token pair"),
            RaydiumCpmmError::InvalidAmount => write!(f, "Invalid amount"),
            RaydiumCpmmError::PoolNotFound => write!(f, "Pool not found"),
            RaydiumCpmmError::CalculationOverflow => write!(f, "Calculation overflow"),
            RaydiumCpmmError::ExcessiveSlippage => write!(f, "Excessive slippage"),
            RaydiumCpmmError::InvalidPoolStatus => write!(f, "Invalid pool status"),
            RaydiumCpmmError::InvalidFeeConfig => write!(f, "Invalid fee configuration"),
        }
    }
}

impl std::error::Error for RaydiumCpmmError {}

/// Raydium CPMM 结果类型
pub type RaydiumCpmmResult<T> = std::result::Result<T, RaydiumCpmmError>;