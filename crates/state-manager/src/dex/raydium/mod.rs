//! Raydium DEX 实现
//!
//! 包含 Raydium 协议的所有池类型实现

pub mod clmm;
pub mod cpmm;

// 未来可以添加更多 Raydium 池类型
// pub mod stable;

// 重新导出具体类型，避免命名冲突
pub use clmm::{
    RaydiumClmmPoolManager, RaydiumClmmPoolState, 
    RewardInfo,
};
pub use cpmm::{
    RaydiumCpmmPoolManager, RaydiumCpmmPool,
    RaydiumCpmmStats, RaydiumCpmmError, RaydiumCpmmResult,
};

// 命名空间导出以避免冲突
pub mod clmm_types {
    pub use super::clmm::types::*;
}

pub mod cpmm_types {
    pub use super::cpmm::types::*;
}
