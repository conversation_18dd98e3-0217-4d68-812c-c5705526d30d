//! # State Manager
//!
//! 多 DEX 流动性池状态管理系统
//!
//! 本模块提供了一个统一的接口来管理不同 DEX 协议的流动性池状态，
//! 包括 Raydium、Meteora、Orca 等主要 DEX 的各种池类型。
//!

// 核心模块
pub mod core;
pub mod utils;

// DEX 特定实现
pub mod dex;

// 重新导出核心类型和功能
pub use core::dex_manager::{DexPoolManager, PoolManagerWrapper};
pub use core::manager::PoolManager;
pub use core::pool::PoolState;
pub use core::types::*;

// 重新导出 DEX 实现
pub use dex::*;

// 重新导出工具
pub use utils::*;
