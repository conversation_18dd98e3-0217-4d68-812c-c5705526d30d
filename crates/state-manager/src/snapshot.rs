//! 状态快照和恢复功能
//!
//! 提供池状态的快照保存和恢复机制

use crate::pool::{PoolState, PoolStateCache};
use shared::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use solana_sdk::pubkey::Pubkey;
use tokio::fs;
use tracing::{info, warn, debug};

/// 池状态快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStateSnapshot {
    /// 快照创建时间戳
    pub timestamp: u64,
    /// 快照版本
    pub version: u32,
    /// 所有池状态 (使用字符串作为key以支持JSON序列化)
    #[serde(with = "pool_map_serde")]
    pub pools: HashMap<Pubkey, PoolState>,
    /// 快照元数据
    pub metadata: SnapshotMetadata,
}

// 自定义序列化模块for HashMap<Pubkey, PoolState>
mod pool_map_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use solana_sdk::pubkey::Pubkey;
    use std::collections::HashMap;
    use std::str::FromStr;
    use crate::pool::PoolState;

    pub fn serialize<S>(map: &HashMap<Pubkey, PoolState>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let string_map: HashMap<String, &PoolState> = map
            .iter()
            .map(|(k, v)| (k.to_string(), v))
            .collect();
        string_map.serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<HashMap<Pubkey, PoolState>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let string_map = HashMap::<String, PoolState>::deserialize(deserializer)?;
        string_map
            .into_iter()
            .map(|(k, v)| {
                Pubkey::from_str(&k)
                    .map(|pubkey| (pubkey, v))
                    .map_err(serde::de::Error::custom)
            })
            .collect()
    }
}

/// 快照元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnapshotMetadata {
    /// 快照ID
    pub id: String,
    /// 创建时间戳
    pub created_at: u64,
    /// 包含的池数量
    pub pool_count: usize,
    /// 数据大小（字节）
    pub data_size: usize,
    /// 最后更新的槽位
    pub last_slot: u64,
    /// 快照描述
    pub description: Option<String>,
    /// 快照类型
    pub snapshot_type: SnapshotType,
}

/// 快照类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SnapshotType {
    /// 定期自动快照
    Automatic,
    /// 手动创建的快照
    Manual,
    /// 故障恢复快照
    Recovery,
    /// 系统升级前的快照
    PreUpgrade,
}

/// 快照管理器
pub struct SnapshotManager {
    /// 快照存储目录
    snapshot_dir: std::path::PathBuf,
    /// 当前快照版本
    current_version: u32,
}

impl SnapshotManager {
    /// 创建新的快照管理器
    pub fn new<P: AsRef<Path>>(snapshot_dir: P) -> Self {
        Self {
            snapshot_dir: snapshot_dir.as_ref().to_path_buf(),
            current_version: 1,
        }
    }

    /// 创建池状态快照
    pub async fn create_snapshot(
        &mut self,
        pool_cache: &PoolStateCache,
        snapshot_type: SnapshotType,
        description: Option<String>,
    ) -> Result<PoolStateSnapshot> {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        // 生成快照ID
        let snapshot_id = format!("snapshot_{}_{}", timestamp, self.current_version);

        // 收集所有池状态
        let pools = self.collect_all_pools(pool_cache).await?;
        let pool_count = pools.len();

        // 计算最后更新的槽位
        let last_slot = pools.values()
            .map(|pool| pool.last_slot)
            .max()
            .unwrap_or(0);

        let metadata = SnapshotMetadata {
            id: snapshot_id.clone(),
            created_at: timestamp,
            pool_count,
            data_size: 0, // 将在序列化后更新
            last_slot,
            description,
            snapshot_type,
        };

        let mut snapshot = PoolStateSnapshot {
            timestamp,
            version: self.current_version,
            pools,
            metadata,
        };

        // 序列化计算大小
        let serialized = serde_json::to_vec(&snapshot)
            .map_err(|e| shared::EchoesError::Internal(format!("Failed to serialize snapshot: {}", e)))?;

        snapshot.metadata.data_size = serialized.len();

        info!(
            "Created snapshot {} with {} pools, size: {} bytes",
            snapshot_id, pool_count, serialized.len()
        );

        self.current_version += 1;
        Ok(snapshot)
    }

    /// 保存快照到文件
    pub async fn save_snapshot(&self, snapshot: &PoolStateSnapshot) -> Result<std::path::PathBuf> {
        // 确保快照目录存在
        fs::create_dir_all(&self.snapshot_dir).await
            .map_err(|e| shared::EchoesError::Storage(format!("Failed to create snapshot directory: {}", e)))?;

        let filename = format!("{}.json", snapshot.metadata.id);
        let file_path = self.snapshot_dir.join(filename);

        // 序列化并保存
        let data = serde_json::to_vec_pretty(&snapshot)
            .map_err(|e| shared::EchoesError::Internal(format!("Failed to serialize snapshot: {}", e)))?;

        fs::write(&file_path, data).await
            .map_err(|e| shared::EchoesError::Storage(format!("Failed to write snapshot file: {}", e)))?;

        info!("Saved snapshot to: {}", file_path.display());
        Ok(file_path)
    }

    /// 从文件加载快照
    pub async fn load_snapshot<P: AsRef<Path>>(&self, file_path: P) -> Result<PoolStateSnapshot> {
        let data = fs::read(file_path.as_ref()).await
            .map_err(|e| shared::EchoesError::Storage(format!("Failed to read snapshot file: {}", e)))?;

        let snapshot: PoolStateSnapshot = serde_json::from_slice(&data)
            .map_err(|e| shared::EchoesError::Parsing(format!("Failed to parse snapshot: {}", e)))?;

        info!("Loaded snapshot {} with {} pools", snapshot.metadata.id, snapshot.metadata.pool_count);
        Ok(snapshot)
    }

    /// 从快照恢复池状态缓存
    pub async fn restore_from_snapshot(
        &self,
        snapshot: &PoolStateSnapshot,
        pool_cache: &PoolStateCache,
    ) -> Result<usize> {
        debug!("Starting restore from snapshot {}", snapshot.metadata.id);

        // 清除现有缓存
        pool_cache.clear();

        let mut restored_count = 0;
        for (address, pool_state) in &snapshot.pools {
            match pool_cache.upsert_pool(pool_state.clone()) {
                Ok(()) => {
                    restored_count += 1;
                }
                Err(e) => {
                    warn!("Failed to restore pool {}: {}", address, e);
                }
            }
        }

        info!(
            "Restored {} out of {} pools from snapshot {}",
            restored_count, snapshot.pools.len(), snapshot.metadata.id
        );

        Ok(restored_count)
    }

    /// 列出所有可用的快照
    pub async fn list_snapshots(&self) -> Result<Vec<SnapshotInfo>> {
        let mut snapshots = Vec::new();

        if !self.snapshot_dir.exists() {
            return Ok(snapshots);
        }

        let mut entries = fs::read_dir(&self.snapshot_dir).await
            .map_err(|e| shared::EchoesError::Storage(format!("Failed to read snapshot directory: {}", e)))?;

        while let Some(entry) = entries.next_entry().await
            .map_err(|e| shared::EchoesError::Storage(format!("Failed to read directory entry: {}", e)))? {

            let path = entry.path();
            if path.extension().and_then(|s| s.to_str()) == Some("json") {
                match self.get_snapshot_info(&path).await {
                    Ok(info) => snapshots.push(info),
                    Err(e) => warn!("Failed to read snapshot info for {}: {}", path.display(), e),
                }
            }
        }

        // 按时间戳排序（最新的在前）
        snapshots.sort_by(|a, b| b.created_at.cmp(&a.created_at));

        Ok(snapshots)
    }

    /// 删除快照文件
    pub async fn delete_snapshot<P: AsRef<Path>>(&self, file_path: P) -> Result<()> {
        fs::remove_file(file_path.as_ref()).await
            .map_err(|e| shared::EchoesError::Storage(format!("Failed to delete snapshot: {}", e)))?;

        info!("Deleted snapshot: {}", file_path.as_ref().display());
        Ok(())
    }

    /// 清理旧快照（保留指定数量的最新快照）
    pub async fn cleanup_old_snapshots(&self, keep_count: usize) -> Result<usize> {
        let snapshots = self.list_snapshots().await?;

        if snapshots.len() <= keep_count {
            return Ok(0);
        }

        let to_delete = &snapshots[keep_count..];
        let mut deleted_count = 0;

        for snapshot_info in to_delete {
            match self.delete_snapshot(&snapshot_info.file_path).await {
                Ok(()) => deleted_count += 1,
                Err(e) => warn!("Failed to delete snapshot {}: {}", snapshot_info.file_path.display(), e),
            }
        }

        info!("Cleaned up {} old snapshots, kept {}", deleted_count, keep_count);
        Ok(deleted_count)
    }

    /// 收集所有池状态
    async fn collect_all_pools(&self, pool_cache: &PoolStateCache) -> Result<HashMap<Pubkey, PoolState>> {
        // 由于PoolStateCache没有提供遍历所有池的方法，我们需要通过不同的池类型来收集
        let mut all_pools = HashMap::new();

        // 这是一个简化的实现，在实际使用中可能需要更复杂的逻辑
        // 来遍历缓存中的所有池
        let pool_types = [
            crate::pool::PoolType::RaydiumClmm,
            crate::pool::PoolType::RaydiumCpmm,
            crate::pool::PoolType::Orca,
            crate::pool::PoolType::MeteoraLbClmm,
            crate::pool::PoolType::PumpFun,
            crate::pool::PoolType::Bonk,
        ];

        for pool_type in &pool_types {
            let pools = pool_cache.find_pools_by_type(*pool_type);
            for pool in pools {
                all_pools.insert(pool.address, pool);
            }
        }

        Ok(all_pools)
    }

    /// 获取快照信息
    async fn get_snapshot_info(&self, file_path: &Path) -> Result<SnapshotInfo> {
        // 只读取元数据部分以提高性能
        let data = fs::read(file_path).await
            .map_err(|e| shared::EchoesError::Storage(format!("Failed to read snapshot file: {}", e)))?;

        // 使用流式解析只提取元数据
        let snapshot: PoolStateSnapshot = serde_json::from_slice(&data)
            .map_err(|e| shared::EchoesError::Parsing(format!("Failed to parse snapshot: {}", e)))?;

        let file_size = data.len();

        Ok(SnapshotInfo {
            id: snapshot.metadata.id,
            created_at: snapshot.metadata.created_at,
            pool_count: snapshot.metadata.pool_count,
            file_size,
            last_slot: snapshot.metadata.last_slot,
            description: snapshot.metadata.description,
            snapshot_type: snapshot.metadata.snapshot_type,
            file_path: file_path.to_path_buf(),
        })
    }
}

/// 快照信息（用于列表展示）
#[derive(Debug, Clone)]
pub struct SnapshotInfo {
    pub id: String,
    pub created_at: u64,
    pub pool_count: usize,
    pub file_size: usize,
    pub last_slot: u64,
    pub description: Option<String>,
    pub snapshot_type: SnapshotType,
    pub file_path: std::path::PathBuf,
}

/// 自动快照服务
pub struct AutoSnapshotService {
    manager: SnapshotManager,
    interval_ms: u64,
    max_snapshots: usize,
}

impl AutoSnapshotService {
    /// 创建新的自动快照服务
    pub fn new(manager: SnapshotManager, interval_ms: u64, max_snapshots: usize) -> Self {
        Self {
            manager,
            interval_ms,
            max_snapshots,
        }
    }

    /// 启动自动快照服务
    pub async fn start(&mut self, pool_cache: &PoolStateCache) -> Result<()> {
        info!("Starting auto snapshot service with {}ms interval", self.interval_ms);

        loop {
            tokio::time::sleep(tokio::time::Duration::from_millis(self.interval_ms)).await;

            // 创建自动快照
            match self.manager.create_snapshot(
                pool_cache,
                SnapshotType::Automatic,
                Some("Automatic periodic snapshot".to_string()),
            ).await {
                Ok(snapshot) => {
                    // 保存快照
                    if let Err(e) = self.manager.save_snapshot(&snapshot).await {
                        warn!("Failed to save automatic snapshot: {}", e);
                        continue;
                    }

                    // 清理旧快照
                    if let Err(e) = self.manager.cleanup_old_snapshots(self.max_snapshots).await {
                        warn!("Failed to cleanup old snapshots: {}", e);
                    }
                }
                Err(e) => {
                    warn!("Failed to create automatic snapshot: {}", e);
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::pool::{TokenReserve, PoolType, PoolSpecificState, CpmmPoolState};
    use tempfile::TempDir;

    async fn create_test_pool_cache() -> PoolStateCache {
        let cache = PoolStateCache::new();

        // 添加一些测试池
        for i in 0..3 {
            let token_a = TokenReserve {
                mint: Pubkey::new_unique(),
                amount: 1000000 + i * 100000,
                decimals: 6,
                symbol: Some(format!("TOKEN_A_{}", i)),
            };

            let token_b = TokenReserve {
                mint: Pubkey::new_unique(),
                amount: 2000000 + i * 200000,
                decimals: 9,
                symbol: Some(format!("TOKEN_B_{}", i)),
            };

            let cpmm_state = CpmmPoolState {
                k_value: (token_a.amount as u128) * (token_b.amount as u128),
                fee_rate: 30,
                lp_supply: 1000000 + i,
            };

            let pool = PoolState::new(
                Pubkey::new_unique(),
                PoolType::RaydiumCpmm,
                token_a,
                token_b,
                PoolSpecificState::Cpmm(cpmm_state),
            );

            cache.upsert_pool(pool).unwrap();
        }

        cache
    }

    #[tokio::test]
    async fn test_snapshot_creation() {
        let temp_dir = TempDir::new().unwrap();
        let mut manager = SnapshotManager::new(temp_dir.path());
        let cache = create_test_pool_cache().await;

        let snapshot = manager.create_snapshot(
            &cache,
            SnapshotType::Manual,
            Some("Test snapshot".to_string()),
        ).await.unwrap();

        assert_eq!(snapshot.pools.len(), 3);
        assert_eq!(snapshot.metadata.pool_count, 3);
        assert!(snapshot.metadata.data_size > 0);
        assert_eq!(snapshot.metadata.description, Some("Test snapshot".to_string()));
    }

    #[tokio::test]
    async fn test_snapshot_save_and_load() {
        let temp_dir = TempDir::new().unwrap();
        let mut manager = SnapshotManager::new(temp_dir.path());
        let cache = create_test_pool_cache().await;

        // 创建快照
        let snapshot = manager.create_snapshot(
            &cache,
            SnapshotType::Manual,
            Some("Test snapshot".to_string()),
        ).await.unwrap();

        let original_id = snapshot.metadata.id.clone();

        // 保存快照
        let file_path = manager.save_snapshot(&snapshot).await.unwrap();
        assert!(file_path.exists());

        // 加载快照
        let loaded_snapshot = manager.load_snapshot(&file_path).await.unwrap();
        assert_eq!(loaded_snapshot.metadata.id, original_id);
        assert_eq!(loaded_snapshot.pools.len(), 3);
    }

    #[tokio::test]
    async fn test_restore_from_snapshot() {
        let temp_dir = TempDir::new().unwrap();
        let mut manager = SnapshotManager::new(temp_dir.path());
        let cache = create_test_pool_cache().await;

        // 创建快照
        let snapshot = manager.create_snapshot(
            &cache,
            SnapshotType::Manual,
            None,
        ).await.unwrap();

        // 创建新的空缓存
        let new_cache = PoolStateCache::new();
        assert_eq!(new_cache.get_stats().total_pools, 0);

        // 从快照恢复
        let restored_count = manager.restore_from_snapshot(&snapshot, &new_cache).await.unwrap();
        assert_eq!(restored_count, 3);
        assert_eq!(new_cache.get_stats().total_pools, 3);
    }

    #[tokio::test]
    async fn test_list_snapshots() {
        let temp_dir = TempDir::new().unwrap();
        let mut manager = SnapshotManager::new(temp_dir.path());
        let cache = create_test_pool_cache().await;

        // 创建多个快照
        for i in 0..3 {
            let snapshot = manager.create_snapshot(
                &cache,
                SnapshotType::Manual,
                Some(format!("Test snapshot {}", i)),
            ).await.unwrap();

            manager.save_snapshot(&snapshot).await.unwrap();
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await; // 确保时间戳不同
        }

        // 列出快照
        let snapshots = manager.list_snapshots().await.unwrap();
        assert_eq!(snapshots.len(), 3);

        // 验证排序（最新的在前）
        for i in 1..snapshots.len() {
            assert!(snapshots[i-1].created_at >= snapshots[i].created_at);
        }
    }

    #[tokio::test]
    async fn test_cleanup_old_snapshots() {
        let temp_dir = TempDir::new().unwrap();
        let mut manager = SnapshotManager::new(temp_dir.path());
        let cache = create_test_pool_cache().await;

        // 创建5个快照
        for i in 0..5 {
            let snapshot = manager.create_snapshot(
                &cache,
                SnapshotType::Manual,
                Some(format!("Test snapshot {}", i)),
            ).await.unwrap();

            manager.save_snapshot(&snapshot).await.unwrap();
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }

        // 验证有5个快照
        let snapshots_before = manager.list_snapshots().await.unwrap();
        assert_eq!(snapshots_before.len(), 5);

        // 清理，只保留3个
        let deleted_count = manager.cleanup_old_snapshots(3).await.unwrap();
        assert_eq!(deleted_count, 2);

        // 验证只剩3个快照
        let snapshots_after = manager.list_snapshots().await.unwrap();
        assert_eq!(snapshots_after.len(), 3);
    }
}
