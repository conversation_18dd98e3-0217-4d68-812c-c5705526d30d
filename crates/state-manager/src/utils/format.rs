//! 格式化工具
//!
//! 提供各种数据格式化功能

use crate::core::types::*;

/// 数字单位配置
#[derive(Debug, <PERSON>lone)]
struct NumberUnit {
    threshold: u128,
    divisor: f64,
    suffix: &'static str,
}

/// 数字格式化工具
pub struct NumberFormatter;

impl NumberFormatter {
    /// 标准数字单位（K, M, B, T）
    const STANDARD_UNITS: &'static [NumberUnit] = &[
        NumberUnit {
            threshold: 1_000_000_000_000,
            divisor: 1_000_000_000_000.0,
            suffix: "T",
        },
        NumberUnit {
            threshold: 1_000_000_000,
            divisor: 1_000_000_000.0,
            suffix: "B",
        },
        NumberUnit {
            threshold: 1_000_000,
            divisor: 1_000_000.0,
            suffix: "M",
        },
        NumberUnit {
            threshold: 1_000,
            divisor: 1_000.0,
            suffix: "K",
        },
    ];

    /// 扩展数字单位（包含 P, E）
    const EXTENDED_UNITS: &'static [NumberUnit] = &[
        NumberUnit {
            threshold: 1_000_000_000_000_000_000,
            divisor: 1_000_000_000_000_000_000.0,
            suffix: "E",
        },
        NumberUnit {
            threshold: 1_000_000_000_000_000,
            divisor: 1_000_000_000_000_000.0,
            suffix: "P",
        },
        NumberUnit {
            threshold: 1_000_000_000_000,
            divisor: 1_000_000_000_000.0,
            suffix: "T",
        },
        NumberUnit {
            threshold: 1_000_000_000,
            divisor: 1_000_000_000.0,
            suffix: "B",
        },
        NumberUnit {
            threshold: 1_000_000,
            divisor: 1_000_000.0,
            suffix: "M",
        },
        NumberUnit {
            threshold: 1_000,
            divisor: 1_000.0,
            suffix: "K",
        },
    ];

    /// 通用数字格式化函数
    fn format_with_units(value: u128, units: &[NumberUnit], precision: usize) -> String {
        for unit in units {
            if value >= unit.threshold {
                let formatted_value = value as f64 / unit.divisor;
                return format!(
                    "{:.prec$}{}",
                    formatted_value,
                    unit.suffix,
                    prec = precision
                );
            }
        }
        value.to_string()
    }

    /// 格式化大数字为可读字符串
    pub fn format_large_number(num: u64) -> String {
        Self::format_with_units(num as u128, Self::STANDARD_UNITS, 2)
    }

    /// 格式化流动性数量
    pub fn format_liquidity(liquidity: u128) -> String {
        Self::format_with_units(liquidity, Self::EXTENDED_UNITS, 2)
    }

    /// 格式化代币数量（考虑小数位）
    pub fn format_token_amount(amount: u64, decimals: u8) -> String {
        let divisor = 10_f64.powi(decimals as i32);
        let formatted_amount = amount as f64 / divisor;

        // 对于代币数量，使用简化的单位系统
        if formatted_amount >= 1_000_000.0 {
            format!("{:.2}M", formatted_amount / 1_000_000.0)
        } else if formatted_amount >= 1_000.0 {
            format!("{:.2}K", formatted_amount / 1_000.0)
        } else {
            Self::format_decimal_amount(formatted_amount)
        }
    }

    /// 格式化小数金额，根据大小选择合适的精度
    fn format_decimal_amount(amount: f64) -> String {
        if amount >= 1.0 {
            format!("{:.6}", amount)
        } else if amount >= 0.00001 {
            format!("{:.8}", amount)
        } else if amount > 0.0 {
            format!("{:.2e}", amount)
        } else {
            "0".to_string()
        }
    }

    /// 格式化价格
    pub fn format_price(price: f64, precision: Option<usize>) -> String {
        let precision = precision.unwrap_or(6);

        // 大数值使用单位缩写
        if price >= 1_000_000.0 {
            format!("{:.2}M", price / 1_000_000.0)
        } else if price >= 1_000.0 {
            format!("{:.2}K", price / 1_000.0)
        } else if price >= 1.0 {
            format!("{:.prec$}", price, prec = precision.min(6))
        } else if price >= 0.001 {
            format!("{:.prec$}", price, prec = precision.min(8))
        } else if price > 0.0 {
            format!("{:.2e}", price)
        } else {
            "0".to_string()
        }
    }

    /// 格式化百分比
    pub fn format_percentage(value: f64, precision: Option<usize>) -> String {
        let precision = precision.unwrap_or(2);
        let formatted = if value.abs() < 0.01 && value != 0.0 {
            format!("{:.4}", value)
        } else {
            format!("{:.prec$}", value, prec = precision)
        };
        format!("{}%", formatted)
    }

    /// 格式化变化百分比（带符号和颜色提示）
    pub fn format_change_percentage(value: f64, precision: Option<usize>) -> String {
        let precision = precision.unwrap_or(2);
        let sign = if value > 0.0 { "+" } else { "" };
        let formatted = if value.abs() < 0.01 && value != 0.0 {
            format!("{:.4}", value)
        } else {
            format!("{:.prec$}", value, prec = precision)
        };
        format!("{}{}%", sign, formatted)
    }

    /// 格式化 USD 金额
    pub fn format_usd(amount: f64) -> String {
        let formatted = if amount >= 1_000_000_000.0 {
            format!("{:.2}B", amount / 1_000_000_000.0)
        } else if amount >= 1_000_000.0 {
            format!("{:.2}M", amount / 1_000_000.0)
        } else if amount >= 1_000.0 {
            format!("{:.2}K", amount / 1_000.0)
        } else if amount >= 1.0 {
            format!("{:.2}", amount)
        } else if amount > 0.0 {
            format!("{:.4}", amount)
        } else {
            "0.00".to_string()
        };

        format!("${}", formatted)
    }

    /// 通用货币格式化函数
    pub fn format_currency(amount: f64, symbol: &str, precision: Option<usize>) -> String {
        let precision = precision.unwrap_or(2);

        let formatted = if amount >= 1_000_000_000.0 {
            format!("{:.2}B", amount / 1_000_000_000.0)
        } else if amount >= 1_000_000.0 {
            format!("{:.2}M", amount / 1_000_000.0)
        } else if amount >= 1_000.0 {
            format!("{:.2}K", amount / 1_000.0)
        } else if amount > 0.0 {
            format!("{:.prec$}", amount, prec = precision)
        } else {
            "0".to_string()
        };

        format!("{}{}", symbol, formatted)
    }
}

/// 时间格式化工具
pub struct TimeFormatter;

impl TimeFormatter {
    /// 格式化时间戳为可读字符串
    pub fn format_timestamp(timestamp: u64) -> String {
        // 简单的格式化，实际项目中可能需要使用 chrono 库
        format!("Timestamp: {}", timestamp)
    }

    /// 格式化持续时间
    pub fn format_duration(duration_ms: u64) -> String {
        let seconds = duration_ms / 1000;
        let minutes = seconds / 60;
        let hours = minutes / 60;
        let days = hours / 24;

        if days > 0 {
            format!("{}d {}h", days, hours % 24)
        } else if hours > 0 {
            format!("{}h {}m", hours, minutes % 60)
        } else if minutes > 0 {
            format!("{}m {}s", minutes, seconds % 60)
        } else {
            format!("{}s", seconds)
        }
    }

    /// 格式化相对时间
    pub fn format_relative_time(timestamp: u64) -> String {
        use std::time::{SystemTime, UNIX_EPOCH};

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        if now > timestamp {
            let diff = now - timestamp;
            format!("{} ago", Self::format_duration(diff))
        } else {
            let diff = timestamp - now;
            format!("in {}", Self::format_duration(diff))
        }
    }
}

/// 地址格式化工具
pub struct AddressFormatter;

impl AddressFormatter {
    /// 缩短 Pubkey 显示
    pub fn shorten_pubkey(pubkey: &solana_sdk::pubkey::Pubkey, chars: Option<usize>) -> String {
        let chars = chars.unwrap_or(4);
        let pubkey_str = pubkey.to_string();

        if pubkey_str.len() <= chars * 2 + 3 {
            pubkey_str
        } else {
            format!(
                "{}...{}",
                &pubkey_str[..chars],
                &pubkey_str[pubkey_str.len() - chars..]
            )
        }
    }

    /// 格式化池标识符
    pub fn format_pool_id(pool_id: &PoolId, shorten: bool) -> String {
        let address = if shorten {
            Self::shorten_pubkey(&pool_id.address, Some(6))
        } else {
            pool_id.address.to_string()
        };

        format!("{}:{}:{}", pool_id.protocol, pool_id.pool_type, address)
    }
}

/// 表格格式化工具
pub struct TableFormatter;

impl TableFormatter {
    /// 创建简单的表格行
    pub fn create_row(columns: &[String], widths: &[usize]) -> String {
        let mut row = String::new();

        for (i, (column, &width)) in columns.iter().zip(widths.iter()).enumerate() {
            if i > 0 {
                row.push_str(" | ");
            }

            if column.len() > width {
                row.push_str(&format!(
                    "{:.width$}",
                    column,
                    width = width.saturating_sub(3)
                ));
                row.push_str("...");
            } else {
                row.push_str(&format!("{:width$}", column, width = width));
            }
        }

        row
    }

    /// 创建表格分隔符
    pub fn create_separator(widths: &[usize]) -> String {
        let mut separator = String::new();

        for (i, &width) in widths.iter().enumerate() {
            if i > 0 {
                separator.push_str("-+-");
            }
            separator.push_str(&"-".repeat(width));
        }

        separator
    }

    /// 格式化池状态表格
    pub fn format_pool_table(pools: &[(PoolId, LiquiditySnapshot)]) -> String {
        let mut table = String::new();

        // 表头
        let headers = vec![
            "Protocol".to_string(),
            "Type".to_string(),
            "Address".to_string(),
            "TVL".to_string(),
            "Volume 24h".to_string(),
            "Fee Rate".to_string(),
        ];

        let widths = [10, 6, 12, 12, 12, 8];

        table.push_str(&Self::create_row(&headers, &widths));
        table.push('\n');
        table.push_str(&Self::create_separator(&widths));
        table.push('\n');

        // 数据行
        for (pool_id, liquidity) in pools {
            let row = vec![
                pool_id.protocol.to_string(),
                pool_id.pool_type.to_string(),
                AddressFormatter::shorten_pubkey(&pool_id.address, Some(6)),
                NumberFormatter::format_usd(liquidity.total_value_usd),
                NumberFormatter::format_usd(liquidity.volume_24h_usd),
                NumberFormatter::format_percentage(liquidity.fee_rate as f64 / 100.0, Some(2)),
            ];

            table.push_str(&Self::create_row(&row, &widths));
            table.push('\n');
        }

        table
    }
}

/// 日志格式化工具
pub struct LogFormatter;

impl LogFormatter {
    /// 格式化交换事件日志
    pub fn format_swap_event(pool_id: &PoolId, quote: &SwapQuote, timestamp: u64) -> String {
        format!(
            "[{}] SWAP {} -> {} | Pool: {} | Input: {} | Output: {} | Fee: {} | Impact: {}",
            TimeFormatter::format_timestamp(timestamp),
            AddressFormatter::shorten_pubkey(&quote.input_token, Some(6)),
            AddressFormatter::shorten_pubkey(&quote.output_token, Some(6)),
            AddressFormatter::format_pool_id(pool_id, true),
            NumberFormatter::format_large_number(quote.input_amount),
            NumberFormatter::format_large_number(quote.output_amount),
            NumberFormatter::format_large_number(quote.fee_amount),
            NumberFormatter::format_percentage(quote.price_impact, Some(2))
        )
    }

    /// 格式化池更新事件日志
    pub fn format_pool_update_event(
        pool_id: &PoolId,
        changes: &[String],
        timestamp: u64,
    ) -> String {
        format!(
            "[{}] POOL_UPDATE {} | Changes: {}",
            TimeFormatter::format_timestamp(timestamp),
            AddressFormatter::format_pool_id(pool_id, true),
            changes.join(", ")
        )
    }

    /// 格式化错误日志
    pub fn format_error_log(operation: &str, error: &PoolError, timestamp: u64) -> String {
        format!(
            "[{}] ERROR {} | {}",
            TimeFormatter::format_timestamp(timestamp),
            operation,
            error
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    #[test]
    fn test_number_formatting() {
        assert_eq!(NumberFormatter::format_large_number(1_500_000), "1.50M");
        assert_eq!(NumberFormatter::format_large_number(2_500_000_000), "2.50B");
        assert_eq!(NumberFormatter::format_large_number(999), "999");
        assert_eq!(
            NumberFormatter::format_liquidity(1_500_000_000_000),
            "1.50T"
        );
        assert_eq!(
            NumberFormatter::format_liquidity(1_500_000_000_000_000),
            "1.50P"
        );
    }

    #[test]
    fn test_decimal_amount_formatting() {
        assert_eq!(NumberFormatter::format_decimal_amount(1.234567), "1.234567");
        assert_eq!(
            NumberFormatter::format_decimal_amount(0.00000123),
            "1.23e-6"
        );
        assert_eq!(
            NumberFormatter::format_decimal_amount(0.000123),
            "0.00012300"
        );
        assert_eq!(NumberFormatter::format_decimal_amount(0.0), "0");
    }

    #[test]
    fn test_address_formatting() {
        let pubkey = Pubkey::from_str("11111111111111111111111111111111").unwrap();
        let shortened = AddressFormatter::shorten_pubkey(&pubkey, Some(4));
        assert_eq!(shortened, "1111...1111");
    }

    #[test]
    fn test_price_formatting() {
        assert_eq!(NumberFormatter::format_price(1.234567, Some(4)), "1.2346");
        assert_eq!(NumberFormatter::format_price(0.000123, None), "1.23e-4");
        assert_eq!(NumberFormatter::format_price(1_500_000.0, None), "1.50M");
    }

    #[test]
    fn test_percentage_formatting() {
        assert_eq!(
            NumberFormatter::format_percentage(12.345, Some(2)),
            "12.35%"
        );
        assert_eq!(NumberFormatter::format_percentage(0.123, Some(3)), "0.123%");
        assert_eq!(NumberFormatter::format_percentage(0.005, None), "0.0050%");
    }

    #[test]
    fn test_change_percentage_formatting() {
        assert_eq!(
            NumberFormatter::format_change_percentage(12.345, Some(2)),
            "+12.35%"
        );
        assert_eq!(
            NumberFormatter::format_change_percentage(-5.67, Some(2)),
            "-5.67%"
        );
        assert_eq!(
            NumberFormatter::format_change_percentage(0.0, None),
            "0.00%"
        );
    }

    #[test]
    fn test_usd_formatting() {
        assert_eq!(NumberFormatter::format_usd(1_500_000.0), "$1.50M");
        assert_eq!(NumberFormatter::format_usd(123.45), "$123.45");
        assert_eq!(NumberFormatter::format_usd(0.1234), "$0.1234");
        assert_eq!(NumberFormatter::format_usd(0.0), "$0.00");
    }

    #[test]
    fn test_currency_formatting() {
        assert_eq!(
            NumberFormatter::format_currency(1_500_000.0, "€", None),
            "€1.50M"
        );
        assert_eq!(
            NumberFormatter::format_currency(123.45, "¥", Some(0)),
            "¥123"
        );
        assert_eq!(NumberFormatter::format_currency(0.0, "$", None), "$0");
    }
}
