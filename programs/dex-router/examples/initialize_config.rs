//! DEX Router 配置初始化示例 (纯 Solana SDK)
//!
//! 此示例展示如何使用纯 Solana SDK 初始化 DEX Router 的全局配置
//! 不依赖 Anchor 客户端，直接构建和发送交易

use solana_sdk::{
    commitment_config::CommitmentConfig,
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_program,
    transaction::Transaction,
    message::Message,
};
use solana_client::rpc_client::RpcClient;
use borsh::{BorshDeserialize, BorshSerialize};
use std::str::FromStr;

// 程序 ID
const PROGRAM_ID: &str = "9zYWuMhEVUj953Zv3Aq2VvBGHrxsbfgnFbWDLjbTocPo";

// 常量定义
const CONFIG_SEED: &[u8] = b"config";

// 指令标识符 (基于 Anchor 的 8 字节判别器)
const INITIALIZE_CONFIG_DISCRIMINATOR: [u8; 8] = [208, 127, 21, 1, 194, 190, 196, 70];

// 配置参数结构
#[derive(BorshSerialize, BorshDeserialize, Debug, Clone)]
pub struct ConfigArgs {
    pub max_route_amount: u64,
    pub max_flash_loan_amount: u64,
    pub max_slippage_bps: u16,
    pub min_profit_threshold: u64,
    pub protocol_fee_bps: u16,
    pub max_route_steps: u8,
}

impl Default for ConfigArgs {
    fn default() -> Self {
        Self {
            max_route_amount: 1_000_000_000_000, // 1M USDC
            max_flash_loan_amount: 10_000_000_000_000, // 10M USDC
            max_slippage_bps: 300, // 3%
            min_profit_threshold: 1_000_000, // 1 USDC
            protocol_fee_bps: 30, // 0.3%
            max_route_steps: 6,
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 设置 RPC 客户端
    let rpc_url = "https://api.devnet.solana.com"; // 或使用 mainnet-beta
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::processed());

    // 2. 加载付款人密钥对 (在实际使用中从文件加载)
    let payer = Keypair::new();
    println!("付款人公钥: {}", payer.pubkey());

    // 3. 解析程序 ID
    let program_id = Pubkey::from_str(PROGRAM_ID)?;

    // 4. 生成配置账户的 PDA
    let (config_pda, config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );

    println!("配置账户 PDA: {}", config_pda);
    println!("配置账户 Bump: {}", config_bump);

    // 5. 准备配置参数
    let config_args = ConfigArgs::default();

    println!("配置参数:");
    println!("  最大路由金额: {} (约 {} USDC)", config_args.max_route_amount, config_args.max_route_amount / 1_000_000);
    println!("  最大闪电贷金额: {} (约 {} USDC)", config_args.max_flash_loan_amount, config_args.max_flash_loan_amount / 1_000_000);
    println!("  最大滑点: {} 基点 ({}%)", config_args.max_slippage_bps, config_args.max_slippage_bps as f64 / 100.0);
    println!("  最小利润阈值: {} (约 {} USDC)", config_args.min_profit_threshold, config_args.min_profit_threshold / 1_000_000);
    println!("  协议费率: {} 基点 ({}%)", config_args.protocol_fee_bps, config_args.protocol_fee_bps as f64 / 100.0);
    println!("  最大路由步骤: {}", config_args.max_route_steps);

    // 6. 构建指令数据
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&INITIALIZE_CONFIG_DISCRIMINATOR);
    config_args.serialize(&mut instruction_data)?;

    // 7. 构建指令
    let instruction = Instruction {
        program_id,
        accounts: vec![
            AccountMeta::new(payer.pubkey(), true),  // admin (signer, writable)
            AccountMeta::new(config_pda, false),     // config (writable)
            AccountMeta::new_readonly(system_program::ID, false), // system_program
        ],
        data: instruction_data,
    };

    // 8. 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash()?;

    // 9. 构建并签名交易
    let message = Message::new(&[instruction], Some(&payer.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&payer], recent_blockhash);

    // 10. 发送交易
    let signature = client.send_and_confirm_transaction(&transaction)?;
    println!("交易签名: {}", signature);
    println!("配置初始化成功！");

    // 11. 验证配置账户 (可选)
    println!("\n验证配置账户...");
    match client.get_account(&config_pda) {
        Ok(account) => {
            println!("  ✓ 配置账户已创建");
            println!("  账户所有者: {}", account.owner);
            println!("  账户数据长度: {} 字节", account.data.len());
            println!("  账户余额: {} lamports", account.lamports);
        }
        Err(e) => {
            println!("  ✗ 无法获取配置账户: {}", e);
        }
    }

    Ok(())
}

/// 从文件加载密钥对的辅助函数
pub fn load_keypair_from_file(path: &str) -> Result<Keypair, Box<dyn std::error::Error>> {
    let keypair_bytes = std::fs::read(path)?;
    let keypair: Vec<u8> = serde_json::from_slice(&keypair_bytes)?;
    Ok(Keypair::from_bytes(&keypair)?)
}

/// 创建自定义配置的示例函数
pub fn create_custom_config() -> ConfigArgs {
    ConfigArgs {
        max_route_amount: 500_000_000_000, // 500K USDC (更保守)
        max_flash_loan_amount: 5_000_000_000_000, // 5M USDC (更保守)
        max_slippage_bps: 200, // 2% (更严格的滑点控制)
        min_profit_threshold: 2_000_000, // 2 USDC (更高的利润要求)
        protocol_fee_bps: 50, // 0.5% (更高的协议费)
        max_route_steps: 4, // 更少的路由步骤
    }
}
