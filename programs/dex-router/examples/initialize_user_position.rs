//! DEX Router 用户位置初始化示例
//! 
//! 此示例展示如何为用户初始化位置跟踪账户
//! 用于跟踪用户的交易历史、风险评分等信息

use anchor_client::solana_sdk::{
    commitment_config::CommitmentConfig,
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_program,
};
use anchor_client::{Client, Cluster};
use dex_router::{
    constants::seeds,
    ID as PROGRAM_ID,
};
use std::rc::Rc;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 设置客户端连接
    let payer = Keypair::new(); // 在实际使用中，应该从文件或环境变量加载
    let client = Client::new_with_options(
        Cluster::Devnet, // 或者使用 Cluster::Mainnet
        Rc::new(payer),
        CommitmentConfig::processed(),
    );
    let program = client.program(PROGRAM_ID)?;

    // 2. 生成用户位置账户的 PDA
    let user_pubkey = program.payer();
    let (position_pda, position_bump) = Pubkey::find_program_address(
        &[seeds::POSITION, user_pubkey.as_ref()],
        &PROGRAM_ID,
    );

    println!("用户公钥: {}", user_pubkey);
    println!("用户位置账户 PDA: {}", position_pda);
    println!("用户位置账户 Bump: {}", position_bump);

    // 3. 构建并发送交易
    let tx = program
        .request()
        .accounts(dex_router::accounts::InitializeUserPosition {
            user: program.payer(),
            position: position_pda,
            system_program: system_program::ID,
        })
        .args(dex_router::instruction::InitializeUserPosition {})
        .send()?;

    println!("交易签名: {}", tx);
    println!("用户位置初始化成功！");

    // 4. 验证用户位置账户
    let position_account: dex_router::state::UserPosition = program.account(position_pda)?;
    println!("\n已创建的用户位置:");
    println!("  用户: {}", position_account.user);
    println!("  创建时间: {}", position_account.created_at);
    println!("  最后更新时间: {}", position_account.last_updated);
    println!("  总交易次数: {}", position_account.total_trades);
    println!("  总交易量: {}", position_account.total_volume);
    println!("  当前风险评分: {}", position_account.risk_score);
    println!("  用户等级: {}", position_account.user_level);
    println!("  是否活跃: {}", position_account.is_active);

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use anchor_client::solana_sdk::signature::Keypair;

    #[test]
    fn test_position_pda_generation() {
        let user_keypair = Keypair::new();
        let user_pubkey = user_keypair.pubkey();
        
        let (position_pda, _bump) = Pubkey::find_program_address(
            &[seeds::POSITION, user_pubkey.as_ref()],
            &PROGRAM_ID,
        );
        
        // 验证 PDA 生成是确定性的
        let (position_pda2, _bump2) = Pubkey::find_program_address(
            &[seeds::POSITION, user_pubkey.as_ref()],
            &PROGRAM_ID,
        );
        
        assert_eq!(position_pda, position_pda2);
    }

    #[test]
    fn test_different_users_different_pdas() {
        let user1 = Keypair::new();
        let user2 = Keypair::new();
        
        let (position_pda1, _) = Pubkey::find_program_address(
            &[seeds::POSITION, user1.pubkey().as_ref()],
            &PROGRAM_ID,
        );
        
        let (position_pda2, _) = Pubkey::find_program_address(
            &[seeds::POSITION, user2.pubkey().as_ref()],
            &PROGRAM_ID,
        );
        
        // 不同用户应该有不同的位置账户 PDA
        assert_ne!(position_pda1, position_pda2);
    }
}
