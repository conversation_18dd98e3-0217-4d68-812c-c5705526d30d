//! DEX Router 用户位置初始化示例 (纯 Solana SDK)
//! 
//! 此示例展示如何使用纯 Solana SDK 初始化用户位置跟踪账户
//! 不依赖 Anchor 客户端，直接构建和发送交易

use solana_sdk::{
    commitment_config::CommitmentConfig,
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_program,
    transaction::Transaction,
    message::Message,
};
use solana_client::rpc_client::RpcClient;
use std::str::FromStr;

// 程序 ID
const PROGRAM_ID: &str = "9zYWuMhEVUj953Zv3Aq2VvBGHrxsbfgnFbWDLjbTocPo";

// 常量定义
const POSITION_SEED: &[u8] = b"position";

// 指令标识符 (基于 Anchor 的 8 字节判别器)
const INITIALIZE_USER_POSITION_DISCRIMINATOR: [u8; 8] = [111, 17, 185, 250, 60, 122, 38, 254];

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 设置 RPC 客户端
    let rpc_url = "https://api.devnet.solana.com"; // 或使用 mainnet-beta
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::processed());
    
    // 2. 加载用户密钥对 (在实际使用中从文件加载)
    let user = Keypair::new();
    println!("用户公钥: {}", user.pubkey());

    // 3. 解析程序 ID
    let program_id = Pubkey::from_str(PROGRAM_ID)?;

    // 4. 生成用户位置账户的 PDA
    let (position_pda, position_bump) = Pubkey::find_program_address(
        &[POSITION_SEED, user.pubkey().as_ref()],
        &program_id,
    );

    println!("用户位置账户 PDA: {}", position_pda);
    println!("用户位置账户 Bump: {}", position_bump);

    // 5. 构建指令数据 (initialize_user_position 不需要额外参数)
    let instruction_data = INITIALIZE_USER_POSITION_DISCRIMINATOR.to_vec();

    // 6. 构建指令
    let instruction = Instruction {
        program_id,
        accounts: vec![
            AccountMeta::new(user.pubkey(), true),       // user (signer, writable)
            AccountMeta::new(position_pda, false),       // position (writable)
            AccountMeta::new_readonly(system_program::ID, false), // system_program
        ],
        data: instruction_data,
    };

    // 7. 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash()?;

    // 8. 构建并签名交易
    let message = Message::new(&[instruction], Some(&user.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&user], recent_blockhash);

    // 9. 发送交易
    let signature = client.send_and_confirm_transaction(&transaction)?;
    println!("交易签名: {}", signature);
    println!("用户位置初始化成功！");

    // 10. 验证用户位置账户 (可选)
    println!("\n验证用户位置账户...");
    match client.get_account(&position_pda) {
        Ok(account) => {
            println!("  ✓ 用户位置账户已创建");
            println!("  账户所有者: {}", account.owner);
            println!("  账户数据长度: {} 字节", account.data.len());
            println!("  账户余额: {} lamports", account.lamports);
        }
        Err(e) => {
            println!("  ✗ 无法获取用户位置账户: {}", e);
        }
    }

    Ok(())
}

/// 批量初始化多个用户位置的函数
pub fn batch_initialize_user_positions(
    client: &RpcClient,
    users: Vec<Keypair>,
) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let program_id = Pubkey::from_str(PROGRAM_ID)?;
    let mut signatures = Vec::new();

    println!("开始批量初始化 {} 个用户位置...", users.len());

    for (i, user) in users.iter().enumerate() {
        println!("\n处理用户 {}/{}: {}", i + 1, users.len(), user.pubkey());

        // 生成用户位置账户的 PDA
        let (position_pda, _position_bump) = Pubkey::find_program_address(
            &[POSITION_SEED, user.pubkey().as_ref()],
            &program_id,
        );

        // 构建指令
        let instruction = Instruction {
            program_id,
            accounts: vec![
                AccountMeta::new(user.pubkey(), true),
                AccountMeta::new(position_pda, false),
                AccountMeta::new_readonly(system_program::ID, false),
            ],
            data: INITIALIZE_USER_POSITION_DISCRIMINATOR.to_vec(),
        };

        // 获取最新的区块哈希
        let recent_blockhash = client.get_latest_blockhash()?;

        // 构建并签名交易
        let message = Message::new(&[instruction], Some(&user.pubkey()));
        let mut transaction = Transaction::new_unsigned(message);
        transaction.sign(&[user], recent_blockhash);

        // 发送交易
        match client.send_and_confirm_transaction(&transaction) {
            Ok(signature) => {
                println!("  ✓ 成功，交易签名: {}", signature);
                signatures.push(signature.to_string());
            }
            Err(e) => {
                println!("  ✗ 失败: {}", e);
                // 继续处理下一个用户，不中断批量操作
            }
        }

        // 添加延迟以避免 RPC 限制
        if i < users.len() - 1 {
            std::thread::sleep(std::time::Duration::from_millis(1000));
        }
    }

    println!("\n批量初始化完成，成功处理 {}/{} 个用户", signatures.len(), users.len());
    Ok(signatures)
}

/// 检查用户位置账户是否存在
pub fn check_user_position_exists(
    client: &RpcClient,
    user_pubkey: &Pubkey,
) -> Result<bool, Box<dyn std::error::Error>> {
    let program_id = Pubkey::from_str(PROGRAM_ID)?;
    let (position_pda, _) = Pubkey::find_program_address(
        &[POSITION_SEED, user_pubkey.as_ref()],
        &program_id,
    );

    match client.get_account(&position_pda) {
        Ok(_) => Ok(true),
        Err(_) => Ok(false),
    }
}

/// 从文件加载密钥对的辅助函数
pub fn load_keypair_from_file(path: &str) -> Result<Keypair, Box<dyn std::error::Error>> {
    let keypair_bytes = std::fs::read(path)?;
    let keypair: Vec<u8> = serde_json::from_slice(&keypair_bytes)?;
    Ok(Keypair::from_bytes(&keypair)?)
}

/// 生成测试用户的辅助函数
pub fn generate_test_users(count: usize) -> Vec<Keypair> {
    (0..count).map(|_| Keypair::new()).collect()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_position_pda_generation() {
        let program_id = Pubkey::from_str(PROGRAM_ID).unwrap();
        let user = Keypair::new();
        
        let (position_pda, _bump) = Pubkey::find_program_address(
            &[POSITION_SEED, user.pubkey().as_ref()],
            &program_id,
        );
        
        // 验证 PDA 生成是确定性的
        let (position_pda2, _bump2) = Pubkey::find_program_address(
            &[POSITION_SEED, user.pubkey().as_ref()],
            &program_id,
        );
        
        assert_eq!(position_pda, position_pda2);
    }

    #[test]
    fn test_different_users_different_pdas() {
        let program_id = Pubkey::from_str(PROGRAM_ID).unwrap();
        let user1 = Keypair::new();
        let user2 = Keypair::new();
        
        let (position_pda1, _) = Pubkey::find_program_address(
            &[POSITION_SEED, user1.pubkey().as_ref()],
            &program_id,
        );
        
        let (position_pda2, _) = Pubkey::find_program_address(
            &[POSITION_SEED, user2.pubkey().as_ref()],
            &program_id,
        );
        
        // 不同用户应该有不同的位置账户 PDA
        assert_ne!(position_pda1, position_pda2);
    }

    #[test]
    fn test_instruction_data_construction() {
        let instruction_data = INITIALIZE_USER_POSITION_DISCRIMINATOR.to_vec();
        
        // 验证指令数据格式
        assert_eq!(instruction_data.len(), 8);
        assert_eq!(instruction_data, INITIALIZE_USER_POSITION_DISCRIMINATOR);
    }

    #[test]
    fn test_generate_test_users() {
        let users = generate_test_users(3);
        assert_eq!(users.len(), 3);
        
        // 确保生成的用户都是唯一的
        for i in 0..users.len() {
            for j in (i + 1)..users.len() {
                assert_ne!(users[i].pubkey(), users[j].pubkey());
            }
        }
    }
}
