//! DEX Router 交换指令示例 (纯 Solana SDK)
//!
//! 此示例展示如何使用纯 Solana SDK 调用 DEX Router 的 swap 指令
//! 执行 WSOL -> WFLI 的代币交换操作

use solana_sdk::{
    commitment_config::CommitmentConfig,
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_program,
    transaction::Transaction,
    message::Message,
};
use solana_client::rpc_client::RpcClient;
use borsh::{BorshDeserialize, BorshSerialize};
use std::str::FromStr;

// 程序 ID (与 lib.rs 中的 declare_id 保持一致)
const PROGRAM_ID: &str = "4TaLvAnovnQmxL2kJNSY3YmrWxnn9NucptcviTJa8iqg";

// 常量定义
const CONFIG_SEED: &[u8] = b"config";

// Swap 指令判别器 (基于 Anchor 的 8 字节判别器)
const SWAP_DISCRIMINATOR: [u8; 8] = [248, 198, 158, 145, 225, 117, 135, 200];

// 代币地址常量
const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";
const WFLI_MINT: &str = "s162E5ZhWYRSCyvKnNjTa4A3GX1WJfmheubSr2UYgtQ";
const WSOL_ACCOUNT: &str = "8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG";
const WFLI_ACCOUNT: &str = "Hn3ahebfrC3EtZHADyhz6rS19kbZnnjRfoC468bAgbwL";

// Token 程序 ID
const TOKEN_PROGRAM_ID: &str = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";

// DEX 类型枚举
#[derive(BorshSerialize, BorshDeserialize, Clone, Copy, Debug)]
enum Dex {
    RaydiumClmm = 0,
    RaydiumCpmm = 1,
    MeteoraDlmm = 2,
    MeteoraAmm = 3,
    Orca = 4,
    PumpSwap = 5,
}

// 路由模式枚举
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
enum RoutingMode {
    Linear = 0,
    Circular = 1,
    Branching = 2,
    Batched = 3,
}

// 单个路由定义
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
struct Route {
    pub dex: Dex,
    pub input_mint: Pubkey,
    pub output_mint: Pubkey,
    pub swap_data: Vec<u8>,
}

// 闪电贷配置（可选）
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
struct FlashLoanConfig {
    pub provider: u8,
    pub provider_program: Pubkey,
    pub borrower: Pubkey,
    pub amount: u64,
    pub max_fee_bps: u16,
}

// 路由配置
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
struct RouteConfig {
    pub mode: RoutingMode,
    pub routes: Vec<Route>,
    pub amount_in: u64,
    pub min_amount_out: u64,
    pub max_slippage_bps: u16,
    pub flash_loan: Option<FlashLoanConfig>,
}

// SwapArgs 参数结构
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
struct SwapArgs {
    pub route_config: RouteConfig,
    pub amount_in: u64,
    pub expect_amount_out: u64,
    pub min_return: u64,
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 设置 RPC 客户端
    let rpc_url = "https://api.devnet.solana.com"; // 或使用 mainnet-beta
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::processed());

    // 2. 加载用户密钥对 (在实际使用中从文件加载)
    let user = Keypair::new();
    println!("用户公钥: {}", user.pubkey());

    // 3. 解析程序 ID 和相关地址
    let program_id = Pubkey::from_str(PROGRAM_ID)?;
    let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
    let wfli_mint = Pubkey::from_str(WFLI_MINT)?;
    let wsol_account = Pubkey::from_str(WSOL_ACCOUNT)?;
    let wfli_account = Pubkey::from_str(WFLI_ACCOUNT)?;
    let token_program = Pubkey::from_str(TOKEN_PROGRAM_ID)?;

    // 4. 生成配置账户的 PDA
    let (config_pda, _config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );

        println!("配置账户 PDA: {}", config_pda);

    // 5. 构建路由配置
    let route = Route {
        dex: Dex::Orca, // 使用 Orca 作为示例
        input_mint: wsol_mint,
        output_mint: wfli_mint,
        swap_data: vec![], // 实际使用时需要根据 DEX 要求填充特定数据
    };

    let route_config = RouteConfig {
        mode: RoutingMode::Linear,
        routes: vec![route],
        amount_in: 1_000_000, // 0.001 WSOL (9 位小数)
        min_amount_out: 500_000, // 预期最少得到的 WFLI 数量
        max_slippage_bps: 300, // 3% 滑点
        flash_loan: None, // 不使用闪电贷
    };

    // 6. 构建 SwapArgs
    let swap_args = SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 600_000, // 预期输出量
        min_return: route_config.min_amount_out,
    };

    let order_id: u64 = 12345; // 订单 ID

    println!("交换配置:");
    println!("  输入代币: {} (WSOL)", wsol_mint);
    println!("  输出代币: {} (WFLI)", wfli_mint);
    println!("  输入金额: {} (约 {} SOL)", swap_args.amount_in, swap_args.amount_in as f64 / 1_000_000_000.0);
    println!("  期望输出: {}", swap_args.expect_amount_out);
    println!("  最小返回: {}", swap_args.min_return);
    println!("  最大滑点: {} 基点 ({}%)", route_config.max_slippage_bps, route_config.max_slippage_bps as f64 / 100.0);
    println!("  订单 ID: {}", order_id);

    // 7. 构建指令数据
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
    swap_args.serialize(&mut instruction_data)?;
    order_id.serialize(&mut instruction_data)?;

    // 8. 构建指令账户
    let accounts = vec![
        AccountMeta::new(user.pubkey(), true),        // user (signer, writable)
        AccountMeta::new_readonly(config_pda, false), // config (readable)
        AccountMeta::new(wsol_account, false),        // source_token_account (writable)
        AccountMeta::new(wfli_account, false),        // destination_token_account (writable)
        AccountMeta::new_readonly(wsol_mint, false),  // source_mint (readable)
        AccountMeta::new_readonly(wfli_mint, false),  // destination_mint (readable)
    ];

    // 9. 构建指令
    let instruction = Instruction {
        program_id,
        accounts,
        data: instruction_data,
    };

    // 10. 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash()?;

    // 11. 构建并签名交易
    let message = Message::new(&[instruction], Some(&user.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&user], recent_blockhash);

    // 12. 模拟交易 (推荐在发送前先模拟)
    println!("\n模拟交易...");
    match client.simulate_transaction(&transaction) {
        Ok(result) => {
            if result.value.err.is_some() {
                println!("  ✗ 模拟失败: {:?}", result.value.err);
                return Err("交易模拟失败".into());
            } else {
                println!("  ✓ 模拟成功");
                if let Some(logs) = result.value.logs {
                    println!("  执行日志:");
                    for log in logs.iter().take(10) { // 只显示前10行日志
                        println!("    {}", log);
                    }
                }
            }
        }
        Err(e) => {
            println!("  ✗ 模拟错误: {}", e);
            return Err(e.into());
        }
    }

    // 13. 发送交易 (取消注释以实际执行)
    /*
    println!("\n发送交易...");
    let signature = client.send_and_confirm_transaction(&transaction)?;
    println!("交易签名: {}", signature);
    println!("交换操作成功！");
    
    // 14. 验证交易结果
    println!("\n验证交易结果...");
    match client.get_transaction(&signature, solana_sdk::commitment_config::UiTransactionEncoding::Json) {
        Ok(confirmed_tx) => {
            println!("  ✓ 交易已确认");
            if let Some(meta) = confirmed_tx.transaction.meta {
                println!("  交易费用: {} lamports", meta.fee);
                if meta.err.is_some() {
                    println!("  ✗ 交易执行错误: {:?}", meta.err);
                } else {
                    println!("  ✓ 交易执行成功");
                }
            }
        }
        Err(e) => {
            println!("  ✗ 无法获取交易详情: {}", e);
        }
    }
    */

    println!("\n注意: 实际发送交易的代码已被注释，取消注释以执行真实交易");

    Ok(())
}

/// 从文件加载密钥对的辅助函数
pub fn load_keypair_from_file(path: &str) -> Result<Keypair, Box<dyn std::error::Error>> {
    let keypair_bytes = std::fs::read(path)?;
    let keypair: Vec<u8> = serde_json::from_slice(&keypair_bytes)?;
    Ok(Keypair::from_bytes(&keypair)?)
}

/// 创建自定义交换配置的示例函数
pub fn create_custom_swap_config() -> SwapArgs {
    let route = Route {
        dex: Dex::RaydiumClmm, // 使用 Raydium CLMM
        input_mint: Pubkey::from_str(WSOL_MINT).unwrap(),
        output_mint: Pubkey::from_str(WFLI_MINT).unwrap(),
        swap_data: vec![], // 需要根据实际 DEX 要求填充
    };

    let route_config = RouteConfig {
        mode: RoutingMode::Linear,
        routes: vec![route],
        amount_in: 5_000_000, // 0.005 WSOL
        min_amount_out: 2_000_000, // 最少期望输出
        max_slippage_bps: 200, // 2% 滑点
        flash_loan: None,
    };

    SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 2_500_000, // 预期输出
        min_return: route_config.min_amount_out,
    }
}

/// 创建多步路由交换的示例函数
pub fn create_multi_step_swap() -> SwapArgs {
    // 示例: WSOL -> USDC -> WFLI (多步路由)
    let usdc_mint = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(); // USDC

    let route1 = Route {
        dex: Dex::Orca,
        input_mint: Pubkey::from_str(WSOL_MINT).unwrap(),
        output_mint: usdc_mint,
        swap_data: vec![],
    };

    let route2 = Route {
        dex: Dex::RaydiumClmm,
        input_mint: usdc_mint,
        output_mint: Pubkey::from_str(WFLI_MINT).unwrap(),
        swap_data: vec![],
    };

    let route_config = RouteConfig {
        mode: RoutingMode::Linear,
        routes: vec![route1, route2],
        amount_in: 10_000_000, // 0.01 WSOL
        min_amount_out: 8_000_000, // 最少期望输出
        max_slippage_bps: 500, // 5% 滑点 (多步路由需要更高滑点保护)
        flash_loan: None,
    };

    SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 10_000_000,
        min_return: route_config.min_amount_out,
    }
}