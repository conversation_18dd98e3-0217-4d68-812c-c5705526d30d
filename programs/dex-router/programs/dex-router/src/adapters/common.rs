//! 通用DEX适配器接口和工具
//!
//! 定义标准化的DEX交互接口，统一CPI调用和验证逻辑

use anchor_lang::prelude::*;
use anchor_spl::token_interface::{TokenAccount};
use anchor_lang::solana_program::{instruction::Instruction};
use anchor_lang::solana_program::program::invoke;
use crate::{ACTUAL_IN_LOWER_BOUND_DEN, ACTUAL_IN_LOWER_BOUND_NUM};
use crate::error::RouteError;

/// 交换执行上下文
/// 包含执行CPI前后需要的所有信息
#[derive(Debug, Clone)]
pub struct SwapExecutionContext {
    pub expected_amount_in: u64,
    pub before_source_balance: u64,
    pub before_destination_balance: u64,
}

/// DEX适配器核心接口
/// 所有DEX适配器都必须实现此接口
pub trait DexProcessor {
    /// 交换前预处理
    /// 返回需要的上下文信息，供后续处理使用
    fn before_invoke(&self, _accounts: &[AccountInfo]) -> Result<u64> {
        // 默认实现：返回默认值
        Ok(0)
    }

    /// 交换后处理
    /// 核心职责：重新加载账户、计算余额、验证结果
    fn after_invoke<'info>(
        &self,
        _accounts: &[AccountInfo],
    ) -> Result<u64> {
        Ok(0)
    }
}

/// 路由前验证逻辑
/// 统一的安全检查，适用于所有DEX
pub fn route_before_check(
    swap_authority: &AccountInfo,
    source_token_account: &Account<TokenAccount>,
    owner_seeds: Option<&[&[&[u8]]]>,
) -> Result<()> {
    // 验证交换权限
    require!(
        swap_authority.key() == source_token_account.owner,
        RouteError::InvalidSwapAuthority
    );

    // 第一步需要签名（除非使用PDA）
    // TODO hop = 0 ，第一步时需要验证
    if owner_seeds.is_none() {
        require!(
            swap_authority.is_signer,
            RouteError::SwapAuthorityIsNotSigner
        );
    }

    // 验证源代币余额
    require!(
        source_token_account.amount > 0,
        RouteError::InsufficientBalance
    );

    // 验证目标代币mint（如果不是第一步）
    // 这里可以添加更多的代币验证逻辑
    // 例如验证代币链的连续性

    Ok(())
}


pub fn invoke_process<'info, T: DexProcessor>(
    amount_in: u64,
    dex_processor: &T,
    account_infos: &[AccountInfo],
    swap_source_token: &mut InterfaceAccount<'info, TokenAccount>,
    swap_destination_token: &mut InterfaceAccount<'info, TokenAccount>,
    instruction: Instruction,
    offset: &mut usize,
    accounts_len: usize,
) -> Result<u64> {
    msg!("invoke process: 开始执行CPI指令, amount_in: {}", amount_in);

    // 1. 记录执行前的余额
    let before_source_balance = swap_source_token.amount;
    let before_destination_balance = swap_destination_token.amount;
    msg!("执行前余额: 源账户={}, 目标账户={}", before_source_balance, before_destination_balance);

    // 2. 执行前钩子函数
    let _before_sa_authority_lamports = dex_processor.before_invoke(account_infos)?;

    // 3. 执行CPI指令
    invoke(&instruction, account_infos)
        .map_err(|e| {
            msg!("CPI调用失败: {:?}", e);
            RouteError::DexCpiCallFailed
        })?;

    dex_processor.after_invoke(account_infos)?;

    post_swap_check(
        swap_source_token,
        swap_destination_token,
        accounts_len,
        offset,
        amount_in,
        before_source_balance,
        before_destination_balance,
    )
}


fn post_swap_check<'info>(
    swap_source_token: &mut InterfaceAccount<'info, TokenAccount>,
    swap_destination_token: &mut InterfaceAccount<'info, TokenAccount>,
    accounts_len: usize,
    offset: &mut usize,
    amount_in: u64,
    before_source_balance: u64,
    before_destination_balance: u64,
) -> Result<u64> {
    // 1. calculate & check actual amount in
    if swap_source_token.get_lamports() > 0 {
        // source token account may be closed in pumpfun buy
        swap_source_token.reload()?;
        let after_source_balance = swap_source_token.amount;
        let actual_amount_in = before_source_balance
            .checked_sub(after_source_balance)
            .ok_or(RouteError::CalculationError)?;

        // min_amount_in = 90% of amount_in
        let min_amount_in = u64::try_from(
            u128::from(amount_in)
                .checked_mul(ACTUAL_IN_LOWER_BOUND_NUM)
                .and_then(|v| v.checked_div(ACTUAL_IN_LOWER_BOUND_DEN))
                .ok_or(RouteError::CalculationError)?,
        )
            .map_err(|_| RouteError::CalculationError)?;
        if !(actual_amount_in <= amount_in && actual_amount_in >= min_amount_in) {
            msg!(
                "InvalidActualAmountIn: actual_amount_in={}, amount_in={}",
                actual_amount_in,
                amount_in,
            );
            return Err(RouteError::InvalidActualAmountIn.into());
        }
    }

    // 2. calculate & check actual amount out
    swap_destination_token.reload()?;
    let after_destination_balance = swap_destination_token.amount;
    let actual_amount_out = after_destination_balance
        .checked_sub(before_destination_balance)
        .ok_or(RouteError::CalculationError)?;
    require!(
        actual_amount_out > 0,
        RouteError::AmountOutMustBeGreaterThanZero
    );

    // 更新offset
    *offset += accounts_len;

    Ok(actual_amount_out)
}
