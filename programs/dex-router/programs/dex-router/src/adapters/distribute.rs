use anchor_lang::prelude::*;
use crate::{meteora, orca, pumpswap, raydium, Dex};


pub fn distribute_swap<'a>(
    dex: &<PERSON>,
    remaining_accounts: &'a [AccountInfo<'a>],
    amount_in: u64,
    offset: &mut usize,
    from_account: Pubkey,
    to_account: Pubkey,
    payer: Option<&AccountInfo<'a>>
) -> Result<u64> {
    let swap_function = match dex {
        Dex::RaydiumClmm => raydium::swap_clmm,
        Dex::RaydiumCpmm => raydium::swap_cpmm,
        Dex::MeteoraDlmm => meteora::swap_dlmm2,
        Dex::MeteoraAmm => meteora::swap_amm,
        Dex::PumpSwapBuy => pumpswap::buy,
        Dex::PumpSwapSell => {
            return pumpswap::sell(remaining_accounts, amount_in, offset, payer);
        },
        Dex::Orca => orca::swap_v2,
    };
    swap_function(remaining_accounts, amount_in, offset, from_account, to_account)
}
