//! Meteora DEX 适配器
//!
//! 支持Meteora的DLMM和Dynamic AMM协议

use anchor_lang::prelude::*;
use anchor_lang::solana_program::{instruction::Instruction, instruction::AccountMeta};
use anchor_spl::token_interface::{Mint, TokenAccount, TokenInterface};
use arrayref::array_ref;
use crate::adapters::common::*;
use crate::error::RouteError;
use crate::constants::ZERO_ADDRESS;
use crate::meteora_programs::{meteora_dlmm_program, meteora_dynamicpool_program};
use crate::{METEORA_SWAP2_DAMM_SELECTOR, METEORA_SWAP2_SELECTOR, METEORA_SWAP_SELECTOR};

const ARGS_LEN: usize = 24;
const DLMM_SWAP2_ARGS_LEN: usize = 28;

// 账户长度常量
const DLMM_ACCOUNTS_LEN: usize = 18;
const DLMM_SWAP2_ACCOUNTS_LEN: usize = 19;
const AMM_ACCOUNTS_LEN: usize = 16;


/// Meteora DLMM处理器
pub struct MeteoraDlmmProcessor;
impl DexProcessor for MeteoraDlmmProcessor {}

/// Meteora Dynamic AMM处理器
pub struct MeteoraAmmProcessor;
impl DexProcessor for MeteoraAmmProcessor {}

/// Meteora DLMM交换账户
pub struct MeteoraDlmmAccounts<'info> {
    pub dex_program_id: &'info AccountInfo<'info>,
    pub swap_authority_pubkey: &'info AccountInfo<'info>,
    pub swap_source_token: InterfaceAccount<'info, TokenAccount>,
    pub swap_destination_token: InterfaceAccount<'info, TokenAccount>,

    pub lb_pair: &'info AccountInfo<'info>,
    pub bin_array_bitmap_extension: &'info AccountInfo<'info>,
    pub reserve_x: InterfaceAccount<'info, TokenAccount>,
    pub reserve_y: InterfaceAccount<'info, TokenAccount>,
    pub token_x_mint: InterfaceAccount<'info, Mint>,
    pub token_y_mint: InterfaceAccount<'info, Mint>,
    pub oracle: &'info AccountInfo<'info>,
    pub host_fee_in: &'info AccountInfo<'info>,
    pub token_x_program: &'info AccountInfo<'info>,
    pub token_y_program: &'info AccountInfo<'info>,
    pub event_authority: &'info AccountInfo<'info>,
    pub bin_array0: &'info AccountInfo<'info>,
    pub bin_array1: &'info AccountInfo<'info>,
    pub bin_array2: &'info AccountInfo<'info>,
}

/// Meteora DLMM Swap2账户（带memo程序）
pub struct MeteoraDlmmSwap2Accounts<'info> {
    pub dex_program_id: &'info AccountInfo<'info>,
    pub swap_authority_pubkey: &'info AccountInfo<'info>,
    pub swap_source_token: InterfaceAccount<'info, TokenAccount>,
    pub swap_destination_token: InterfaceAccount<'info, TokenAccount>,

    pub lb_pair: &'info AccountInfo<'info>,
    pub bin_array_bitmap_extension: &'info AccountInfo<'info>,
    pub reserve_x: InterfaceAccount<'info, TokenAccount>,
    pub reserve_y: InterfaceAccount<'info, TokenAccount>,
    pub token_x_mint: InterfaceAccount<'info, Mint>,
    pub token_y_mint: InterfaceAccount<'info, Mint>,
    pub oracle: &'info AccountInfo<'info>,
    pub host_fee_in: &'info AccountInfo<'info>,
    pub token_x_program: &'info AccountInfo<'info>,
    pub token_y_program: &'info AccountInfo<'info>,
    pub memo_program: &'info AccountInfo<'info>,
    pub event_authority: &'info AccountInfo<'info>,
    pub bin_array0: &'info AccountInfo<'info>,
    pub bin_array1: &'info AccountInfo<'info>,
    pub bin_array2: &'info AccountInfo<'info>,
}

/// Meteora Dynamic AMM账户
pub struct MeteoraAmmAccounts<'info> {
    pub dex_program_id: &'info AccountInfo<'info>,
    pub swap_authority_pubkey: &'info AccountInfo<'info>,
    pub swap_source_token: InterfaceAccount<'info, TokenAccount>,
    pub swap_destination_token: InterfaceAccount<'info, TokenAccount>,

    pub pool_authority: &'info AccountInfo<'info>,
    pub pool: &'info AccountInfo<'info>,
    pub input_token_account: Box<InterfaceAccount<'info, TokenAccount>>,
    pub output_token_account: Box<InterfaceAccount<'info, TokenAccount>>,
    pub token_a_vault: Box<InterfaceAccount<'info, TokenAccount>>,
    pub token_b_vault: Box<InterfaceAccount<'info, TokenAccount>>,
    pub token_a_mint: InterfaceAccount<'info, Mint>,
    pub token_b_mint: InterfaceAccount<'info, Mint>,
    pub token_a_program: Interface<'info, TokenInterface>,
    pub token_b_program: Interface<'info, TokenInterface>,
    pub referral_token_account: &'info AccountInfo<'info>,
    pub event_authority: &'info AccountInfo<'info>,
}

impl<'info> MeteoraDlmmAccounts<'info> {
    fn parse_accounts(accounts: &'info [AccountInfo<'info>], offset: usize) -> Result<Self> {
        let [
            dex_program_id,
            swap_authority_pubkey,
            swap_source_token,
            swap_destination_token,
            lb_pair,
            bin_array_bitmap_extension,
            reserve_x,
            reserve_y,
            token_x_mint,
            token_y_mint,
            oracle,
            host_fee_in,
            token_x_program,
            token_y_program,
            event_authority,
            bin_array0,
            bin_array1,
            bin_array2,
        ]: &[AccountInfo<'info>; DLMM_ACCOUNTS_LEN] = array_ref![accounts, offset, DLMM_ACCOUNTS_LEN];

        Ok(Self {
            dex_program_id,
            swap_authority_pubkey,
            swap_source_token: InterfaceAccount::try_from(swap_source_token)?,
            swap_destination_token: InterfaceAccount::try_from(swap_destination_token)?,
            lb_pair,
            bin_array_bitmap_extension,
            reserve_x: InterfaceAccount::try_from(reserve_x)?,
            reserve_y: InterfaceAccount::try_from(reserve_y)?,
            token_x_mint: InterfaceAccount::try_from(token_x_mint)?,
            token_y_mint: InterfaceAccount::try_from(token_y_mint)?,
            oracle,
            host_fee_in,
            token_x_program,
            token_y_program,
            event_authority,
            bin_array0,
            bin_array1,
            bin_array2,
        })
    }
}

impl<'info> MeteoraDlmmSwap2Accounts<'info> {
    fn parse_accounts(accounts: &'info [AccountInfo<'info>], offset: usize) -> Result<Self> {
        let [
            dex_program_id,
            swap_authority_pubkey,
            swap_source_token,
            swap_destination_token,
            lb_pair,
            bin_array_bitmap_extension,
            reserve_x,
            reserve_y,
            token_x_mint,
            token_y_mint,
            oracle,
            host_fee_in,
            token_x_program,
            token_y_program,
            memo_program,
            event_authority,
            bin_array0,
            bin_array1,
            bin_array2,
        ]: &[AccountInfo<'info>; DLMM_SWAP2_ACCOUNTS_LEN] = array_ref![accounts, offset, DLMM_SWAP2_ACCOUNTS_LEN];

        Ok(Self {
            dex_program_id,
            swap_authority_pubkey,
            swap_source_token: InterfaceAccount::try_from(swap_source_token)?,
            swap_destination_token: InterfaceAccount::try_from(swap_destination_token)?,
            lb_pair,
            bin_array_bitmap_extension,
            reserve_x: InterfaceAccount::try_from(reserve_x)?,
            reserve_y: InterfaceAccount::try_from(reserve_y)?,
            token_x_mint: InterfaceAccount::try_from(token_x_mint)?,
            token_y_mint: InterfaceAccount::try_from(token_y_mint)?,
            oracle,
            host_fee_in,
            token_x_program,
            token_y_program,
            memo_program,
            event_authority,
            bin_array0,
            bin_array1,
            bin_array2,
        })
    }
}

impl<'info> MeteoraAmmAccounts<'info> {
    fn parse_accounts(accounts: &'info [AccountInfo<'info>], offset: usize) -> Result<Self> {
        let [
            dex_program_id,
            swap_authority_pubkey,
            swap_source_token,
            swap_destination_token,
            pool_authority,
            pool,
            input_token_account,
            output_token_account,
            token_a_vault,
            token_b_vault,
            token_a_mint,
            token_b_mint,
            token_a_program,
            token_b_program,
            referral_token_account,
            event_authority
        ]: &[AccountInfo<'info>; AMM_ACCOUNTS_LEN] = array_ref![accounts, offset, AMM_ACCOUNTS_LEN];

        Ok(Self {
            dex_program_id,
            swap_authority_pubkey,
            swap_source_token: InterfaceAccount::try_from(swap_source_token)?,
            swap_destination_token: InterfaceAccount::try_from(swap_destination_token)?,
            pool_authority,
            pool,
            input_token_account: Box::new(InterfaceAccount::try_from(input_token_account)?),
            output_token_account: Box::new(InterfaceAccount::try_from(output_token_account)?),
            token_a_vault: Box::new(InterfaceAccount::try_from(token_a_vault)?),
            token_b_vault: Box::new(InterfaceAccount::try_from(token_b_vault)?),
            token_a_mint: InterfaceAccount::try_from(token_a_mint)?,
            token_b_mint: InterfaceAccount::try_from(token_b_mint)?,
            token_a_program: Interface::try_from(token_a_program)?,
            token_b_program: Interface::try_from(token_b_program)?,
            referral_token_account,
            event_authority,
        })
    }
}

/// Meteora DLMM交换
pub fn swap_dlmm<'a>(
    remaining_accounts: &'a [AccountInfo<'a>],
    amount_in: u64,
    offset: &mut usize,
    _from_account: Pubkey,
    _to_account: Pubkey,
) -> Result<u64> {
    msg!(
        "Dex::MeteoraDlmm amount_in: {}, offset: {}",
        amount_in,
        offset
    );
    require!(
        remaining_accounts.len() >= *offset + DLMM_ACCOUNTS_LEN,
        RouteError::InvalidAccountsLength
    );

    let mut swap_accounts = MeteoraDlmmAccounts::parse_accounts(remaining_accounts, *offset)?;
    if swap_accounts.dex_program_id.key != &meteora_dlmm_program::id() {
        return Err(RouteError::InvalidProgram.into());
    }

    // 记录池地址
    swap_accounts.lb_pair.key().log();

    // 检查源和目标代币
    let swap_source_token = swap_accounts.swap_source_token.key();
    let swap_destination_token = swap_accounts.swap_destination_token.key();

    let mut data = Vec::with_capacity(ARGS_LEN);
    data.extend_from_slice(METEORA_SWAP_SELECTOR);
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&1u64.to_le_bytes()); // minimum_amount_out

    let mut accounts = vec![
        AccountMeta::new(swap_accounts.lb_pair.key(), false),
        AccountMeta::new_readonly(swap_accounts.bin_array_bitmap_extension.key(), false),
        AccountMeta::new(swap_accounts.reserve_x.key(), false),
        AccountMeta::new(swap_accounts.reserve_y.key(), false),
        AccountMeta::new(swap_source_token, false),
        AccountMeta::new(swap_destination_token, false),
        AccountMeta::new_readonly(swap_accounts.token_x_mint.key(), false),
        AccountMeta::new_readonly(swap_accounts.token_y_mint.key(), false),
        AccountMeta::new(swap_accounts.oracle.key(), false),
        AccountMeta::new(swap_accounts.host_fee_in.key(), false),
        AccountMeta::new_readonly(swap_accounts.swap_authority_pubkey.key(), true),
        AccountMeta::new_readonly(swap_accounts.token_x_program.key(), false),
        AccountMeta::new_readonly(swap_accounts.token_y_program.key(), false),
        AccountMeta::new_readonly(swap_accounts.event_authority.key(), false),
        AccountMeta::new_readonly(swap_accounts.dex_program_id.key(), false),
        AccountMeta::new(swap_accounts.bin_array0.key(), false),
    ];

    let mut account_infos = vec![
        swap_accounts.lb_pair.to_account_info(),
        swap_accounts.bin_array_bitmap_extension.to_account_info(),
        swap_accounts.reserve_x.to_account_info(),
        swap_accounts.reserve_y.to_account_info(),
        swap_accounts.swap_source_token.to_account_info(),
        swap_accounts.swap_destination_token.to_account_info(),
        swap_accounts.token_x_mint.to_account_info(),
        swap_accounts.token_y_mint.to_account_info(),
        swap_accounts.oracle.to_account_info(),
        swap_accounts.host_fee_in.to_account_info(),
        swap_accounts.swap_authority_pubkey.to_account_info(),
        swap_accounts.token_x_program.to_account_info(),
        swap_accounts.token_y_program.to_account_info(),
        swap_accounts.event_authority.to_account_info(),
        swap_accounts.dex_program_id.to_account_info(),
        swap_accounts.bin_array0.to_account_info(),
    ];

    // 添加可选的bin arrays
    let bin_array1 = swap_accounts.bin_array1.key();
    let bin_array2 = swap_accounts.bin_array2.key();
    if bin_array1 != ZERO_ADDRESS {
        accounts.push(AccountMeta::new(bin_array1, false));
        account_infos.push(swap_accounts.bin_array1.to_account_info());
    }
    if bin_array2 != ZERO_ADDRESS {
        accounts.push(AccountMeta::new(bin_array2, false));
        account_infos.push(swap_accounts.bin_array2.to_account_info());
    }

    let instruction = Instruction {
        program_id: swap_accounts.dex_program_id.key(),
        accounts,
        data,
    };

    let dex_processor = &MeteoraDlmmProcessor;
    let amount_out = invoke_process(
        amount_in,
        dex_processor,
        &account_infos,
        &mut swap_accounts.swap_source_token,
        &mut swap_accounts.swap_destination_token,
        instruction,
        offset,
        DLMM_ACCOUNTS_LEN,
    )?;
    Ok(amount_out)
}

/// Meteora DLMM Swap2交换（带memo程序）
pub fn swap_dlmm2<'a>(
    remaining_accounts: &'a [AccountInfo<'a>],
    amount_in: u64,
    offset: &mut usize,
    _from_account: Pubkey,
    _to_account: Pubkey,
) -> Result<u64> {
    msg!(
        "Dex::MeteoraDlmmSwap2 amount_in: {}, offset: {}",
        amount_in,
        offset
    );
    require!(
        remaining_accounts.len() >= *offset + DLMM_SWAP2_ACCOUNTS_LEN,
        RouteError::InvalidAccountsLength
    );

    let mut swap_accounts = MeteoraDlmmSwap2Accounts::parse_accounts(remaining_accounts, *offset)?;
    if swap_accounts.dex_program_id.key != &meteora_dlmm_program::id() {
        return Err(RouteError::InvalidProgram.into());
    }

    // 记录池地址
    swap_accounts.lb_pair.key().log();

    let swap_source_token = swap_accounts.swap_source_token.key();
    let swap_destination_token = swap_accounts.swap_destination_token.key();

    let mut data = Vec::with_capacity(DLMM_SWAP2_ARGS_LEN);
    data.extend_from_slice(METEORA_SWAP2_SELECTOR);
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&1u64.to_le_bytes()); // minimum_amount_out
    data.extend_from_slice(&0u32.to_le_bytes()); // additional parameter

    let mut accounts = vec![
        AccountMeta::new(swap_accounts.lb_pair.key(), false),
        AccountMeta::new_readonly(swap_accounts.bin_array_bitmap_extension.key(), false),
        AccountMeta::new(swap_accounts.reserve_x.key(), false),
        AccountMeta::new(swap_accounts.reserve_y.key(), false),
        AccountMeta::new(swap_source_token, false),
        AccountMeta::new(swap_destination_token, false),
        AccountMeta::new_readonly(swap_accounts.token_x_mint.key(), false),
        AccountMeta::new_readonly(swap_accounts.token_y_mint.key(), false),
        AccountMeta::new(swap_accounts.oracle.key(), false),
        AccountMeta::new(swap_accounts.host_fee_in.key(), false),
        AccountMeta::new_readonly(swap_accounts.swap_authority_pubkey.key(), true),
        AccountMeta::new_readonly(swap_accounts.token_x_program.key(), false),
        AccountMeta::new_readonly(swap_accounts.token_y_program.key(), false),
        AccountMeta::new_readonly(swap_accounts.memo_program.key(), false),
        AccountMeta::new_readonly(swap_accounts.event_authority.key(), false),
        AccountMeta::new_readonly(swap_accounts.dex_program_id.key(), false),
        AccountMeta::new(swap_accounts.bin_array0.key(), false),
    ];

    let mut account_infos = vec![
        swap_accounts.lb_pair.to_account_info(),
        swap_accounts.bin_array_bitmap_extension.to_account_info(),
        swap_accounts.reserve_x.to_account_info(),
        swap_accounts.reserve_y.to_account_info(),
        swap_accounts.swap_source_token.to_account_info(),
        swap_accounts.swap_destination_token.to_account_info(),
        swap_accounts.token_x_mint.to_account_info(),
        swap_accounts.token_y_mint.to_account_info(),
        swap_accounts.oracle.to_account_info(),
        swap_accounts.host_fee_in.to_account_info(),
        swap_accounts.swap_authority_pubkey.to_account_info(),
        swap_accounts.token_x_program.to_account_info(),
        swap_accounts.token_y_program.to_account_info(),
        swap_accounts.memo_program.to_account_info(),
        swap_accounts.event_authority.to_account_info(),
        swap_accounts.dex_program_id.to_account_info(),
        swap_accounts.bin_array0.to_account_info(),
    ];

    // 添加可选的bin arrays
    let bin_array1 = swap_accounts.bin_array1.key();
    let bin_array2 = swap_accounts.bin_array2.key();
    if bin_array1 != ZERO_ADDRESS {
        accounts.push(AccountMeta::new(bin_array1, false));
        account_infos.push(swap_accounts.bin_array1.to_account_info());
    }
    if bin_array2 != ZERO_ADDRESS {
        accounts.push(AccountMeta::new(bin_array2, false));
        account_infos.push(swap_accounts.bin_array2.to_account_info());
    }

    let instruction = Instruction {
        program_id: swap_accounts.dex_program_id.key(),
        accounts,
        data,
    };

    let dex_processor = &MeteoraDlmmProcessor;
    let amount_out = invoke_process(
        amount_in,
        dex_processor,
        &account_infos,
        &mut swap_accounts.swap_source_token,
        &mut swap_accounts.swap_destination_token,
        instruction,
        offset,
        DLMM_SWAP2_ACCOUNTS_LEN,
    )?;
    Ok(amount_out)
}

/// Meteora Dynamic AMM交换
pub fn swap_amm<'a>(
    remaining_accounts: &'a [AccountInfo<'a>],
    amount_in: u64,
    offset: &mut usize,
    _from_account: Pubkey,
    _to_account: Pubkey,
) -> Result<u64> {
    msg!(
        "Dex::MeteoraAMM amount_in: {}, offset: {}",
        amount_in,
        offset
    );
    require!(
        remaining_accounts.len() >= *offset + AMM_ACCOUNTS_LEN,
        RouteError::InvalidAccountsLength
    );

    let mut swap_accounts = MeteoraAmmAccounts::parse_accounts(remaining_accounts, *offset)?;
    if swap_accounts.dex_program_id.key != &meteora_dynamicpool_program::id() {
        return Err(RouteError::InvalidProgram.into());
    }

    // 记录池地址
    swap_accounts.pool.key().log();

    let swap_destination_token = swap_accounts.output_token_account.key();

    let mut data = Vec::with_capacity(ARGS_LEN);
    data.extend_from_slice(METEORA_SWAP2_DAMM_SELECTOR);
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&1u64.to_le_bytes()); // minimum_amount_out

    let accounts = vec![
        AccountMeta::new_readonly(swap_accounts.pool_authority.key(), false),
        AccountMeta::new(swap_accounts.pool.key(), false),
        AccountMeta::new(swap_accounts.input_token_account.key(), false),
        AccountMeta::new(swap_accounts.output_token_account.key(), false),
        AccountMeta::new(swap_accounts.token_a_vault.key(), false),
        AccountMeta::new(swap_accounts.token_b_vault.key(), false),
        AccountMeta::new_readonly(swap_accounts.token_a_mint.key(), false),
        AccountMeta::new_readonly(swap_accounts.token_b_mint.key(), false),
        AccountMeta::new(swap_accounts.swap_authority_pubkey.key(), true),
        AccountMeta::new_readonly(swap_accounts.token_a_program.key(), false),
        AccountMeta::new_readonly(swap_accounts.token_b_program.key(), false),
        if swap_accounts.referral_token_account.key() != swap_accounts.dex_program_id.key() {
            AccountMeta::new(swap_accounts.referral_token_account.key(), false)
        } else {
            AccountMeta::new_readonly(swap_accounts.referral_token_account.key(), false)
        },
        AccountMeta::new_readonly(swap_accounts.event_authority.key(), false),
        AccountMeta::new_readonly(swap_accounts.dex_program_id.key(), false),
    ];

    let account_infos = vec![
        swap_accounts.pool_authority.to_account_info(),
        swap_accounts.pool.to_account_info(),
        swap_accounts.input_token_account.to_account_info(),
        swap_accounts.output_token_account.to_account_info(),
        swap_accounts.token_a_vault.to_account_info(),
        swap_accounts.token_b_vault.to_account_info(),
        swap_accounts.token_a_mint.to_account_info(),
        swap_accounts.token_b_mint.to_account_info(),
        swap_accounts.swap_authority_pubkey.to_account_info(),
        swap_accounts.token_a_program.to_account_info(),
        swap_accounts.token_b_program.to_account_info(),
        swap_accounts.referral_token_account.to_account_info(),
        swap_accounts.event_authority.to_account_info(),
        swap_accounts.dex_program_id.to_account_info(),
    ];

    let instruction = Instruction {
        program_id: swap_accounts.dex_program_id.key(),
        accounts,
        data,
    };

    let dex_processor = &MeteoraAmmProcessor;
    let amount_out = invoke_process(
        amount_in,
        dex_processor,
        &account_infos,
        &mut swap_accounts.swap_source_token,
        &mut swap_accounts.swap_destination_token,
        instruction,
        offset,
        AMM_ACCOUNTS_LEN,
    )?;

    Ok(amount_out)
}
