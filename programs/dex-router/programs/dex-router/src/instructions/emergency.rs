use anchor_lang::prelude::*;
use crate::{
    constants::{Dex, seeds::*},
    state::{RouterConfig},
    error::RouteError,
};

/// EmergencyStop 指令参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct EmergencyStopArgs {
    /// 全局紧急停止（停止所有操作）
    pub global_stop: bool,
    /// 特定DEX停止列表
    pub dex_stops: Vec<Dex>,
    /// 操作原因（用于审计）
    pub reason: String,
}

/// EmergencyStop 指令账户结构
#[derive(Accounts)]
#[instruction(args: EmergencyStopArgs)]
pub struct EmergencyStop<'info> {
    /// 管理员账户（必须签名）
    #[account(
        mut,
        constraint = admin.key() == config.admin @ RouteError::UnauthorizedAdmin
    )]
    pub admin: Signer<'info>,

    /// 路由器全局配置
    #[account(
        mut,
        seeds = [CONFIG],
        bump
    )]
    pub config: Account<'info, RouterConfig>,

    /// 系统程序
    pub system_program: Program<'info, System>,
}

impl<'info> EmergencyStop<'info> {
    /// 验证紧急停止权限
    pub fn validate_emergency_permissions(&self) -> Result<()> {
        // 验证管理员签名
        require!(
            self.admin.is_signer,
            RouteError::UnauthorizedAdmin
        );

        // 验证管理员身份
        require!(
            self.admin.key() == self.config.admin,
            RouteError::UnauthorizedAdmin
        );

        Ok(())
    }

    /// 应用紧急停止设置
    pub fn apply_emergency_stops(&mut self, args: &EmergencyStopArgs) -> Result<()> {
        let config = &mut self.config;

        // 更新全局紧急停止状态
        if args.global_stop != config.emergency_stop {
            config.emergency_stop = args.global_stop;

            if args.global_stop {
                msg!("全局紧急停止已启动: {}", args.reason);
            } else {
                msg!("全局紧急停止已解除: {}", args.reason);
            }
        }

        Ok(())
    }

    /// 记录紧急停止事件
    pub fn log_emergency_event(&self, args: &EmergencyStopArgs) {
        msg!("紧急停止操作记录:");
        msg!("  管理员: {}", self.admin.key());
        msg!("  全局停止: {}", args.global_stop);
        msg!("  受影响DEX数量: {}", args.dex_stops.len());
        msg!("  操作原因: {}", args.reason);
        msg!("  操作时间: {}", Clock::get().unwrap().unix_timestamp);
    }
}

/// 解除紧急停止指令参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ResumeOperationsArgs {
    /// 恢复全局操作
    pub resume_global: bool,
    /// 恢复的DEX列表
    pub resume_dexes: Vec<Dex>,
    /// 恢复原因
    pub reason: String,
}

/// ResumeOperations 指令账户结构  
#[derive(Accounts)]
#[instruction(args: ResumeOperationsArgs)]
pub struct ResumeOperations<'info> {
    /// 管理员账户（必须签名）
    #[account(
        mut,
        constraint = admin.key() == config.admin @ RouteError::UnauthorizedAdmin
    )]
    pub admin: Signer<'info>,

    /// 路由器全局配置
    #[account(
        mut,
        seeds = [CONFIG],
        bump
    )]
    pub config: Account<'info, RouterConfig>,

    /// 系统程序
    pub system_program: Program<'info, System>,
}

impl<'info> ResumeOperations<'info> {
    /// 恢复操作
    pub fn resume_operations(&mut self, args: &ResumeOperationsArgs) -> Result<()> {
        let config = &mut self.config;

        // 恢复全局操作
        if args.resume_global && config.emergency_stop {
            config.emergency_stop = false;
            msg!("全局操作已恢复: {}", args.reason);
        }

        Ok(())
    }
}

/// UpdateEmergencyConfig 指令参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct UpdateEmergencyConfigArgs {
    /// 新的紧急联系人列表
    pub emergency_contacts: Option<Vec<Pubkey>>,
    /// 紧急停止冷却时间（秒）
    pub emergency_cooldown: Option<u64>,
    /// 是否启用自动紧急停止
    pub auto_emergency_enabled: Option<bool>,
    /// 自动紧急停止触发条件
    pub auto_trigger_conditions: Option<EmergencyTriggerConditions>,
}

/// 自动紧急停止触发条件
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct EmergencyTriggerConditions {
    /// 最大失败率阈值（基点）
    pub max_failure_rate_bps: u16,
    /// 监控时间窗口（秒）
    pub monitoring_window_seconds: u64,
    /// 最小交易量阈值
    pub min_transaction_threshold: u64,
}

/// UpdateEmergencyConfig 指令账户结构
#[derive(Accounts)]
#[instruction(args: UpdateEmergencyConfigArgs)]
pub struct UpdateEmergencyConfig<'info> {
    /// 管理员账户（必须签名）
    #[account(
        mut,
        constraint = admin.key() == config.admin @ RouteError::UnauthorizedAdmin
    )]
    pub admin: Signer<'info>,

    /// 路由器全局配置
    #[account(
        mut,
        seeds = [CONFIG], 
        bump
    )]
    pub config: Account<'info, RouterConfig>,
}

impl<'info> UpdateEmergencyConfig<'info> {
    /// 更新紧急配置
    pub fn update_emergency_config(&mut self, args: &UpdateEmergencyConfigArgs) -> Result<()> {
        let config = &mut self.config;

        // 更新紧急联系人
        if let Some(contacts) = &args.emergency_contacts {
            require!(
                contacts.len() <= 10, // 最多10个紧急联系人
                RouteError::TooManyEmergencyContacts
            );
            msg!("紧急联系人已更新，数量: {}", contacts.len());
        }

        // 更新冷却时间
        if let Some(cooldown) = args.emergency_cooldown {
            require!(
                cooldown >= 60 && cooldown <= 86400, // 1分钟到1天
                RouteError::InvalidCooldownPeriod
            );
            msg!("紧急停止冷却时间已更新: {} 秒", cooldown);
        }

        // 更新自动紧急停止设置
        if let Some(auto_enabled) = args.auto_emergency_enabled {
            msg!("自动紧急停止设置: {}", auto_enabled);
        }

        // 更新自动触发条件
        if let Some(conditions) = &args.auto_trigger_conditions {
            require!(
                conditions.max_failure_rate_bps <= 5000, // 最大50%失败率
                RouteError::InvalidFailureRateThreshold
            );

            require!(
                conditions.monitoring_window_seconds >= 300, // 至少5分钟监控窗口
                RouteError::InvalidMonitoringWindow
            );
            
            msg!("自动紧急停止触发条件已更新");
        }

        Ok(())
    }
}

// 指令处理器函数

/// EmergencyStop 指令处理器
pub fn emergency_stop_handler(
    ctx: Context<EmergencyStop>,
    args: EmergencyStopArgs,
) -> Result<()> {
    msg!("执行紧急停止操作");

    // 验证权限
    ctx.accounts.validate_emergency_permissions()?;

    // 应用紧急停止设置
    ctx.accounts.apply_emergency_stops(&args)?;

    // 记录事件
    ctx.accounts.log_emergency_event(&args);

    Ok(())
}

/// ResumeOperations 指令处理器
pub fn resume_operations_handler(
    ctx: Context<ResumeOperations>,
    args: ResumeOperationsArgs,
) -> Result<()> {
    msg!("恢复系统操作");

    // 验证权限
    require!(
        ctx.accounts.admin.is_signer,
        RouteError::UnauthorizedAdmin
    );

    // 恢复操作
    ctx.accounts.resume_operations(&args)?;

    msg!("系统操作恢复完成");

    Ok(())
}

/// UpdateEmergencyConfig 指令处理器
pub fn update_emergency_config_handler(
    ctx: Context<UpdateEmergencyConfig>,
    args: UpdateEmergencyConfigArgs,
) -> Result<()> {
    msg!("更新紧急配置");

    // 验证权限
    require!(
        ctx.accounts.admin.is_signer,
        RouteError::UnauthorizedAdmin
    );

    // 更新配置
    ctx.accounts.update_emergency_config(&args)?;

    msg!("紧急配置更新完成");

    Ok(())
}