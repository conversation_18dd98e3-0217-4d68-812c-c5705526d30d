use anchor_lang::prelude::*;
use crate::state::{RouterConfig, UserPosition, ConfigArgs};
use crate::constants::seeds;

/// 初始化路由器配置账户结构
#[derive(Accounts)]
pub struct InitializeConfig<'info> {
    /// 管理员账户（支付账户创建费用）
    #[account(mut)]
    pub admin: Signer<'info>,

    /// 路由器配置账户
    #[account(
        init,
        payer = admin,
        space = RouterConfig::LEN,
        seeds = [seeds::CONFIG],
        bump
    )]
    pub config: Account<'info, RouterConfig>,

    /// 系统程序
    pub system_program: Program<'info, System>,
}

/// 初始化用户位置账户结构
#[derive(Accounts)]
pub struct InitializeUserPosition<'info> {
    /// 用户账户（支付账户创建费用）
    #[account(mut)]
    pub user: Signer<'info>,

    /// 用户位置账户
    #[account(
        init,
        payer = user,
        space = UserPosition::LEN,
        seeds = [seeds::POSITION, user.key().as_ref()],
        bump
    )]
    pub position: Account<'info, UserPosition>,

    /// 系统程序
    pub system_program: Program<'info, System>,
}

/// 初始化路由器配置处理函数
pub fn initialize_config_handler(
    ctx: Context<InitializeConfig>,
    config_data: ConfigArgs,
) -> Result<()> {
    let config = &mut ctx.accounts.config;
    let admin = ctx.accounts.admin.key();
    let current_timestamp = Clock::get()?.unix_timestamp;

    // 使用默认参数初始化配置
    let mut default_config = RouterConfig::default_params();
    
    // 设置管理员和时间戳
    default_config.admin = admin;
    default_config.created_at = current_timestamp;
    default_config.updated_at = current_timestamp;

    // 应用自定义配置参数
    default_config.max_route_amount = config_data.max_route_amount;
    default_config.max_flash_loan_amount = config_data.max_flash_loan_amount;
    default_config.max_slippage_bps = config_data.max_slippage_bps;
    default_config.min_profit_threshold = config_data.min_profit_threshold;
    default_config.protocol_fee_bps = config_data.protocol_fee_bps;
    default_config.max_route_steps = config_data.max_route_steps;

    // 默认启用所有主流DEX
    default_config.enable_dex(crate::constants::dex_ids::RAYDIUM_CLMM);
    default_config.enable_dex(crate::constants::dex_ids::RAYDIUM_CPMM);
    default_config.enable_dex(crate::constants::dex_ids::METEORA_DLMM);
    default_config.enable_dex(crate::constants::dex_ids::METEORA_AMM);
    default_config.enable_dex(crate::constants::dex_ids::ORCA_WHIRLPOOL);
    default_config.enable_dex(crate::constants::dex_ids::PUMPSWAP);

    // 将配置复制到账户
    config.set_inner(default_config);

    msg!("路由器配置已初始化，管理员: {}", admin);
    Ok(())
}

/// 初始化用户位置处理函数
pub fn initialize_user_position_handler(
    ctx: Context<InitializeUserPosition>,
) -> Result<()> {
    let position = &mut ctx.accounts.position;
    let user = ctx.accounts.user.key();
    let current_timestamp = Clock::get()?.unix_timestamp;

    // 创建新的用户位置
    let new_position = UserPosition::new(user, current_timestamp);
    position.set_inner(new_position);

    msg!("用户位置已初始化，用户: {}", user);
    Ok(())
}
