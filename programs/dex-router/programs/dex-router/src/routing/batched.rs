//! 批量路由引擎
//!
//! 实现批量路由模式：[A1, A2] -> [B1, B2]
//! 支持并行处理多个独立路由，提高吞吐量

use anchor_lang::prelude::*;
use crate::constants::{Route, RouteConfig, RoutingMode, BatchRouteConfig, Dex};
use crate::error::RouteError;
use crate::{RouteExecuted, SwapCompleted, BatchExecuted, route_phases};
use crate::get_accounts_needed_for_dex;

/// 批量路由执行器
pub struct BatchedRouteExecutor;

impl BatchedRouteExecutor {
    /// 执行批量路由
    pub fn execute_batched_route<'info>(
        config: &BatchRouteConfig,
        accounts: &[AccountInfo<'info>],
        remaining_accounts: &[AccountInfo<'info>],
    ) -> Result<BatchExecutionResult> {
        // 验证批量路由配置
        Self::validate_batched_config(config)?;

        // 发出路由开始事件
        let total_amount_in: u64 = config.routes.iter().map(|r| r.amount_in).sum();
        let total_min_out: u64 = config.routes.iter().map(|r| r.min_amount_out).sum();

        emit!(RouteExecuted {
            phase: route_phases::STARTED,
            mode: RoutingMode::Batched,
            user: None,
            amount_in: total_amount_in,
            amount_out: 0,
            min_amount_out: total_min_out,
            routes_count: config.routes.len() as u8,
            routes_executed: 0,
            total_fees: 0,
            execution_time: 0,
            success: false,
            actual_slippage_bps: 0,
            timestamp: Clock::get()?.unix_timestamp,
        });

        let mut results = Vec::new();
        let mut total_fees = 0u64;
        let mut successful_routes = 0u8;
        let mut failed_routes = 0u8;
        let mut account_offset = 0;

        // 执行每个路由
        for (route_index, route_config) in config.routes.iter().enumerate() {
            msg!("执行批量路由 {}/{}", route_index + 1, config.routes.len());

            // 计算此路由需要的账户数量
            let accounts_needed = Self::calculate_accounts_needed(route_config);

            // 提取路由账户
            let route_accounts = if account_offset + accounts_needed <= remaining_accounts.len() {
                &remaining_accounts[account_offset..account_offset + accounts_needed]
            } else {
                return Err(RouteError::InvalidDexAccounts.into());
            };

            account_offset += accounts_needed;

            // 执行单个路由
            match Self::execute_single_route(route_config, route_accounts, route_index) {
                Ok((amount_out, fees)) => {
                    total_fees = total_fees.checked_add(fees)
                        .ok_or(RouteError::MathOverflow)?;
                    successful_routes += 1;

                    results.push(RouteExecutionResult {
                        route_index: route_index as u8,
                        success: true,
                        amount_in: route_config.amount_in,
                        amount_out,
                        fees,
                        error: None,
                    });
                },
                Err(e) => {
                    failed_routes += 1;

                    results.push(RouteExecutionResult {
                        route_index: route_index as u8,
                        success: false,
                        amount_in: route_config.amount_in,
                        amount_out: 0,
                        fees: 0,
                        error: Some(e.to_string()),
                    });

                    // 如果是原子性执行模式且有失败，立即停止
                    if config.atomic {
                        msg!("原子性批量执行失败，路由 {} 执行错误", route_index);
                        return Err(RouteError::BatchExecutionFailed.into());
                    }
                }
            }
        }

        // 计算总输出
        let total_amount_out: u64 = results.iter()
            .filter(|r| r.success)
            .map(|r| r.amount_out)
            .sum();

        // 在原子性模式下验证所有路由都成功
        if config.atomic && failed_routes > 0 {
            return Err(RouteError::BatchExecutionFailed.into());
        }

        // 发出批量执行完成事件
        emit!(BatchExecuted {
            total_routes: config.routes.len() as u8,
            successful_routes,
            failed_routes,
            total_amount_in,
            total_amount_out,
            total_fees,
            atomic_mode: config.atomic,
        });

        // 发出路由完成事件
        emit!(RouteExecuted {
            phase: 0,
            mode: RoutingMode::Batched,
            user: None,
            amount_in: total_amount_in,
            amount_out: total_amount_out,
            min_amount_out: 0,total_fees,
            routes_executed: successful_routes,
            success: failed_routes == 0 || !config.atomic,
            actual_slippage_bps: 0,
            routes_count: 0,
            execution_time: 0,
            timestamp: 0
        });

        msg!("批量路由执行完成: {}/{} 成功, 总输出: {}",
             successful_routes, config.routes.len(), total_amount_out);

        Ok(BatchExecutionResult {
            results,
            total_amount_in,
            total_amount_out,
            total_fees,
            successful_routes,
            failed_routes,
            atomic_mode: config.atomic,
        })
    }

    /// 执行单个路由
    fn execute_single_route<'info>(
        route_config: &RouteConfig,
        accounts: &[AccountInfo<'info>],
        route_index: usize,
    ) -> Result<(u64, u64)> {
        let mut current_amount = route_config.amount_in;
        let mut total_fees = 0u64;
        let mut account_offset = 0;
        
        let route_mode = route_config.routing_mode_id.try_into()?;

        // 根据路由模式选择执行方式
        match route_mode {
            RoutingMode::Linear => {
                // 执行线性路由
                for (step, route) in route_config.routes.iter().enumerate() {
                    let accounts_needed = get_accounts_needed_for_dex() as usize;
                    let step_accounts = &accounts[account_offset..account_offset + accounts_needed];
                    account_offset += accounts_needed;

                    let (amount_out, step_fee) = Self::execute_route_step(
                        route,
                        step_accounts,
                        current_amount,
                        step == route_config.routes.len() - 1,
                        route_config.min_amount_out,
                        route_index,
                        step,
                    )?;

                    current_amount = amount_out;
                    total_fees = total_fees.checked_add(step_fee)
                        .ok_or(RouteError::MathOverflow)?;
                }
            },
            RoutingMode::Circular => {
                // 循环路由验证
                let first_input = route_config.routes[0].input_mint;
                let last_output = route_config.routes.last().unwrap().output_mint;
                require!(
                    first_input == last_output,
                    RouteError::InvalidCircularRoute
                );

                // 执行循环路由步骤
                for (step, route) in route_config.routes.iter().enumerate() {
                    let accounts_needed = get_accounts_needed_for_dex() as usize;
                    let step_accounts = &accounts[account_offset..account_offset + accounts_needed];
                    account_offset += accounts_needed;

                    let min_step_output = if step == route_config.routes.len() - 1 {
                        route_config.min_amount_out // 最后一步必须满足最小输出
                    } else {
                        current_amount.checked_mul(95)
                            .and_then(|x| x.checked_div(100))
                            .ok_or(RouteError::MathOverflow)?
                    };

                    let (amount_out, step_fee) = Self::execute_route_step(
                        route,
                        step_accounts,
                        current_amount,
                        false, // 循环路由中间步骤不使用最终最小输出
                        min_step_output,
                        route_index,
                        step,
                    )?;

                    current_amount = amount_out;
                    total_fees = total_fees.checked_add(step_fee)
                        .ok_or(RouteError::MathOverflow)?;
                }
            },
            _ => {
                return Err(RouteError::UnsupportedBatchRouteMode.into());
            }
        }

        Ok((current_amount, total_fees))
    }

    /// 执行路由步骤
    fn execute_route_step<'info>(
        route: &Route,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        is_final_step: bool,
        min_final_output: u64,
        route_index: usize,
        step: usize,
    ) -> Result<(u64, u64)> {
        // 创建DEX处理器
        
        // 计算最小输出
        let min_amount_out = if is_final_step {
            min_final_output
        } else {
            amount_in.checked_mul(95)
                .and_then(|x| x.checked_div(100))
                .ok_or(RouteError::MathOverflow)?
        };

        // 计算费用
        let fee_bps = 0;
        let fee_amount = amount_in.checked_mul(fee_bps as u64)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        // 执行交换
        let amount_out = 0;

        // 发出单步完成事件
        emit!(SwapCompleted {
            dex: route.dex_id.try_into()?,
            input_mint: route.input_mint,
            output_mint: route.output_mint,
            amount_in: if step == 0 { amount_in } else { 0 },
            amount_out,
            fee_paid: fee_amount,
            step: (route_index * 100 + step) as u8, // 编码路由索引和步骤
        });

        Ok((amount_out, fee_amount))
    }

    /// 计算路由配置需要的账户数量
    fn calculate_accounts_needed(route_config: &RouteConfig) -> usize {
        route_config.routes.iter()
            .map(|route| get_accounts_needed_for_dex() as usize)
            .sum()
    }



    /// 验证批量路由配置
    pub fn validate_batched_config(config: &BatchRouteConfig) -> Result<()> {
        require!(
            !config.routes.is_empty(),
            RouteError::EmptyRoute
        );

        require!(
            config.routes.len() <= 8, // 最大8个并行路由
            RouteError::TooManyBatchRoutes
        );

        // 验证每个路由配置
        for (i, route_config) in config.routes.iter().enumerate() {
            // 验证路由模式（批量中只支持线性和循环）
            require!(
                matches!(route_config.routing_mode_id.try_into()?, RoutingMode::Linear | RoutingMode::Circular),
                RouteError::UnsupportedBatchRouteMode
            );

            require!(
                !route_config.routes.is_empty(),
                RouteError::EmptyRoute
            );

            require!(
                route_config.amount_in > 0,
                RouteError::ZeroAmount
            );

            require!(
                route_config.min_amount_out > 0,
                RouteError::ZeroAmount
            );

            // 验证路由连续性
            for j in 1..route_config.routes.len() {
                require!(
                    route_config.routes[j - 1].output_mint == route_config.routes[j].input_mint,
                    RouteError::InvalidRouteConfig
                );
            }

            // 如果是循环路由，验证闭环
            if route_config.routing_mode_id == RoutingMode::Circular as u8 {
                let first_input = route_config.routes[0].input_mint;
                let last_output = route_config.routes.last().unwrap().output_mint;
                require!(
                    first_input == last_output,
                    RouteError::InvalidCircularRoute
                );
            }
        }

        Ok(())
    }
}

/// 批量执行结果
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct BatchExecutionResult {
    pub results: Vec<RouteExecutionResult>,
    pub total_amount_in: u64,
    pub total_amount_out: u64,
    pub total_fees: u64,
    pub successful_routes: u8,
    pub failed_routes: u8,
    pub atomic_mode: bool,
}

/// 单个路由执行结果
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RouteExecutionResult {
    pub route_index: u8,
    pub success: bool,
    pub amount_in: u64,
    pub amount_out: u64,
    pub fees: u64,
    pub error: Option<String>,
}

/// DEX使用统计
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct DexUsageCount {
    pub dex: Dex,
    pub count: u32,
}

/// 批量性能分析
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct BatchPerformanceAnalysis {
    pub total_routes: u8,
    pub total_amount_in: u64,
    pub total_fee_bps: u16,
    pub max_steps: u8,
    pub estimated_execution_time: u32,
    pub dex_usage_count: Vec<DexUsageCount>,
    pub atomic_mode: bool,
}

/// 批量路由优化器
pub struct BatchOptimizer;

impl BatchOptimizer {
    /// 优化批量路由配置
    pub fn optimize_batch_config(
        configs: &[RouteConfig],
        atomic_required: bool,
    ) -> Result<BatchRouteConfig> {
        require!(
            !configs.is_empty() && configs.len() <= 8,
            RouteError::InvalidBatchSize
        );

        // 按预期输出排序（优先执行预期收益高的路由）
        let mut sorted_configs: Vec<_> = configs.iter().cloned().collect();
        sorted_configs.sort_by(|a, b| {
            let a_ratio = (a.min_amount_out as f64) / (a.amount_in as f64);
            let b_ratio = (b.min_amount_out as f64) / (b.amount_in as f64);
            b_ratio.partial_cmp(&a_ratio).unwrap_or(std::cmp::Ordering::Equal)
        });

        Ok(BatchRouteConfig {
            routes: sorted_configs,
            atomic: atomic_required,
        })
    }

    /// 检查批量路由的资源使用情况
    pub fn check_resource_usage(config: &BatchRouteConfig) -> Result<ResourceUsage> {
        let mut total_accounts = 0;
        let mut total_instructions = 0;
        let mut compute_units = 0u64;

        for route_config in &config.routes {
            for route in &route_config.routes {
                total_accounts += get_accounts_needed_for_dex();
                total_instructions += 1;

                // 估算计算单元使用（根据DEX复杂度）
                compute_units += 50_000;
            }
        }

        Ok(ResourceUsage {
            total_accounts,
            total_instructions,
            estimated_compute_units: compute_units,
            within_limits: total_accounts <= 255 && total_instructions <= 64 && compute_units <= 1_400_000,
        })
    }
}

/// 资源使用情况
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ResourceUsage {
    pub total_accounts: u8,
    pub total_instructions: u32,
    pub estimated_compute_units: u64,
    pub within_limits: bool,
}