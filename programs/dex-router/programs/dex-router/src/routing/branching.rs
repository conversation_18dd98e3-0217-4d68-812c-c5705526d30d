//! 分支路由引擎
//!
//! 实现分支路由模式：A -> [B, C] -> D
//! 支持分散-聚合模式，将输入分配到多个路径然后聚合输出

use anchor_lang::prelude::*;
use crate::constants::{Route, RouteConfig, RoutingMode, BranchRouteConfig};
use crate::error::RouteError;
use crate::state::event::{RouteExecuted, SwapCompleted, BranchExecuted, route_phases};
use crate::utils::get_accounts_needed_for_dex;

/// 分支路由执行器
pub struct BranchingRouteExecutor;

impl BranchingRouteExecutor {
    /// 执行分支路由
    pub fn execute_branching_route<'info>(
        config: &BranchRouteConfig,
        accounts: &[AccountInfo<'info>],
        remaining_accounts: &[AccountInfo<'info>],
    ) -> Result<u64> {
        // 验证分支路由配置
        Self::validate_branching_config(config)?;

        // 发出路由开始事件
        let total_amount_in = config.branches.iter().map(|b| b.amount_in).sum();
        let total_min_out = config.branches.iter().map(|b| b.min_amount_out).sum();

        emit!(RouteExecuted {
            phase: route_phases::STARTED,
            mode: RoutingMode::Branching,
            user: None,
            amount_in: total_amount_in,
            amount_out: 0,
            min_amount_out: total_min_out,
            routes_count: config.branches.len() as u8,
            routes_executed: 0,
            total_fees: 0,
            execution_time: 0,
            success: false,
            actual_slippage_bps: 0,
            timestamp: Clock::get()?.unix_timestamp,
        });

        let mut total_amount_out = 0u64;
        let mut total_fees = 0u64;
        let mut executed_branches = 0u8;

        // 计算每个分支的分配金额
        let total_allocation_bps: u16 = config.allocation_bps.iter().sum();
        require!(
            total_allocation_bps == 10000,
            RouteError::InvalidAllocation
        );

        // 计算总输入金额
        let total_amount_in: u64 = config.branches.iter().map(|b| b.amount_in).sum();

        // 执行每个分支
        for (branch_index, (branch_config, &allocation_bps)) in
            config.branches.iter().zip(config.allocation_bps.iter()).enumerate() {

            msg!("执行分支 {}/{}: 分配比例 {}%",
                branch_index + 1,
                config.branches.len(),
                allocation_bps as f64 / 100.0
            );

            // 计算此分支的实际输入金额
            let branch_amount_in = total_amount_in.checked_mul(allocation_bps as u64)
                .and_then(|x| x.checked_div(10000))
                .ok_or(RouteError::MathOverflow)?;

            // 获取此分支的账户
            let branch_accounts = Self::extract_branch_accounts(
                remaining_accounts,
                branch_index,
                &branch_config.routes,
            )?;

            // 执行分支路由
            let (branch_output, branch_fees) = Self::execute_single_branch(
                branch_config,
                &branch_accounts,
                branch_amount_in,
            )?;

            // 累计结果
            total_amount_out = total_amount_out.checked_add(branch_output)
                .ok_or(RouteError::MathOverflow)?;

            total_fees = total_fees.checked_add(branch_fees)
                .ok_or(RouteError::MathOverflow)?;

            executed_branches += 1;

            // 发出分支执行完成事件
            emit!(BranchExecuted {
                branch_index: branch_index as u8,
                input_token: config.input_mint,
                output_token: config.output_mint,
                amount_in: branch_amount_in,
                amount_out: branch_output,
                allocation_bps,
                routes_in_branch: branch_config.routes.len() as u8,
            });
        }

        // 验证最终输出是否满足要求
        let min_total_output: u64 = config.branches.iter().map(|b| b.min_amount_out).sum();
        require!(
            total_amount_out >= min_total_output,
            RouteError::InsufficientOutput
        );

        // 发出路由完成事件
        emit!(RouteExecuted {
            phase: route_phases::COMPLETED,
            mode: RoutingMode::Branching,
            user: None,
            amount_in: total_amount_in,
            amount_out: total_amount_out,
            min_amount_out: total_min_out,
            routes_count: config.branches.len() as u8,
            routes_executed: executed_branches,
            total_fees,
            execution_time: 0,
            success: true,
            actual_slippage_bps: 0,
            timestamp: Clock::get()?.unix_timestamp,
        });

        msg!("分支路由执行成功: {} 个分支, 总输出: {}", executed_branches, total_amount_out);

        Ok(total_amount_out)
    }

    /// 执行单个分支
    fn execute_single_branch<'info>(
        branch_config: &RouteConfig,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
    ) -> Result<(u64, u64)> {
        let mut current_amount = amount_in;
        let mut branch_fees = 0u64;
        let mut account_offset: usize = 0;

        // 执行分支中的每个步骤
        for (step, route) in branch_config.routes.iter().enumerate() {
            msg!("  执行分支步骤 {}/{}: {:?} -> {:?}",
                step + 1,
                branch_config.routes.len(),
                route.input_mint,
                route.output_mint
            );

            // 获取此步骤需要的账户数量
            let accounts_needed = get_accounts_needed_for_dex() as usize;

            // 提取步骤账户
            let step_accounts = &accounts[account_offset..account_offset + accounts_needed];
            account_offset += accounts_needed;

            // 计算最小输出（最后一步使用配置的最小输出，其他步骤允许滑点）
            let min_amount_out = if step == branch_config.routes.len() - 1 {
                branch_config.min_amount_out
            } else {
                current_amount.checked_mul(95)
                    .and_then(|x| x.checked_div(100))
                    .ok_or(RouteError::MathOverflow)?
            };

            // 计算费用
            let fee_bps = 0;
            let step_fee = current_amount.checked_mul(fee_bps as u64)
                .and_then(|x| x.checked_div(10000))
                .ok_or(RouteError::MathOverflow)?;

            // 执行交换
            current_amount = 0;

            branch_fees = branch_fees.checked_add(step_fee)
                .ok_or(RouteError::MathOverflow)?;
        }

        Ok((current_amount, branch_fees))
    }

    /// 从remaining_accounts中提取特定分支的账户
    fn extract_branch_accounts<'info>(
        remaining_accounts: &[AccountInfo<'info>],
        branch_index: usize,
        routes: &[Route],
    ) -> Result<Vec<AccountInfo<'info>>> {
        let mut start_offset: usize = 0;

        // 计算前面分支使用的账户数量
        for _i in 0..branch_index {
            // 这里需要知道每个前面分支的路由配置
            // 简化实现：假设每个分支最多使用固定数量的账户
            start_offset += 20; // 假设每个分支最多20个账户
        }

        // 计算当前分支需要的账户数量
        let mut accounts_needed: usize = 0;
        for route in routes {
            accounts_needed += get_accounts_needed_for_dex() as usize;
        }

        let end_offset = start_offset + accounts_needed;

        require!(
            end_offset <= remaining_accounts.len(),
            RouteError::InvalidDexAccounts
        );

        let branch_accounts: Vec<AccountInfo<'info>> = remaining_accounts[start_offset..end_offset]
            .iter()
            .cloned()
            .collect();

        Ok(branch_accounts)
    }



    /// 验证分支路由配置
    pub fn validate_branching_config(config: &BranchRouteConfig) -> Result<()> {
        require!(
            !config.branches.is_empty(),
            RouteError::EmptyRoute
        );

        require!(
            config.branches.len() <= 4, // 最大4个分支
            RouteError::TooManyBranches
        );

        require!(
            config.allocation_bps.len() == config.branches.len(),
            RouteError::InvalidAllocation
        );

        // 验证分配比例总和为100%
        let total_allocation: u16 = config.allocation_bps.iter().sum();
        require!(
            total_allocation == 10000,
            RouteError::InvalidAllocation
        );

        // 验证每个分配比例都大于0
        for &allocation in &config.allocation_bps {
            require!(
                allocation > 0,
                RouteError::InvalidAllocation
            );
        }

        // 验证所有分支的输入代币相同，输出代币相同
        let first_input = config.input_mint;
        let first_output = config.output_mint;

        for (i, branch) in config.branches.iter().enumerate() {
            // 验证分支路由配置
            require!(
                branch.routing_mode_id == RoutingMode::Linear as u8, // 分支内部必须是线性的
                RouteError::InvalidBranchConfig
            );

            require!(
                !branch.routes.is_empty(),
                RouteError::EmptyRoute
            );

            // 验证输入输出代币一致性
            require!(
                branch.routes[0].input_mint == first_input,
                RouteError::InvalidBranchConfig
            );

            require!(
                branch.routes.last().unwrap().output_mint == first_output,
                RouteError::InvalidBranchConfig
            );

            // 验证分支内部路由连续性
            for j in 1..branch.routes.len() {
                require!(
                    branch.routes[j - 1].output_mint == branch.routes[j].input_mint,
                    RouteError::InvalidRouteConfig
                );
            }
        }

        Ok(())
    }

    /// 预估分支路由输出
    pub fn estimate_branching_output(
        config: &BranchRouteConfig,
        additional_data: &[Vec<Vec<u8>>], // 每个分支每步的数据
    ) -> Result<u64> {
        Self::validate_branching_config(config)?;

        require!(
            additional_data.len() >= config.branches.len(),
            RouteError::InsufficientData
        );

        let mut total_output = 0u64;
        let total_amount_in: u64 = config.branches.iter().map(|b| b.amount_in).sum();

        for (i, (branch, &allocation_bps)) in
            config.branches.iter().zip(config.allocation_bps.iter()).enumerate() {

            let branch_amount_in = total_amount_in.checked_mul(allocation_bps as u64)
                .and_then(|x| x.checked_div(10000))
                .ok_or(RouteError::MathOverflow)?;

            let mut current_amount = branch_amount_in;

            // 模拟执行分支中的每一步
            for (j, route) in branch.routes.iter().enumerate() {
                require!(
                    j < additional_data[i].len(),
                    RouteError::InsufficientData
                );

                current_amount = 0;
            }

            total_output = total_output.checked_add(current_amount)
                .ok_or(RouteError::MathOverflow)?;
        }

        Ok(total_output)
    }

    /// 优化分支分配比例
    pub fn optimize_allocation(
        config: &mut BranchRouteConfig,
        market_data: &[Vec<Vec<u8>>],
    ) -> Result<()> {
        Self::validate_branching_config(config)?;

        // 计算每个分支的效率（输出/输入比率）
        let mut branch_efficiencies = Vec::new();
        let total_amount_in: u64 = config.branches.iter().map(|b| b.amount_in).sum();

        for (i, branch) in config.branches.iter().enumerate() {
            let test_amount = 1_000_000u64; // 使用固定测试金额
            let mut current_amount = test_amount;

            for (j, route) in branch.routes.iter().enumerate() {
                current_amount = 0;
            }

            let efficiency = (current_amount as f64) / (test_amount as f64);
            branch_efficiencies.push(efficiency);
        }

        // 根据效率重新分配比例
        let total_efficiency: f64 = branch_efficiencies.iter().sum();

        for (i, efficiency) in branch_efficiencies.iter().enumerate() {
            let new_allocation = ((efficiency / total_efficiency) * 10000.0) as u16;
            config.allocation_bps[i] = new_allocation;
        }

        // 确保总和为10000
        let current_total: u16 = config.allocation_bps.iter().sum();
        if current_total != 10000 {
            let diff = 10000i32 - current_total as i32;
            config.allocation_bps[0] = (config.allocation_bps[0] as i32 + diff) as u16;
        }

        Ok(())
    }
}

/// 分支路由优化器
pub struct BranchingOptimizer;

impl BranchingOptimizer {
    /// 为给定的代币对找到最优分支配置
    pub fn find_optimal_branches(
        input_mint: Pubkey,
        output_mint: Pubkey,
        amount_in: u64,
        max_branches: u8,
    ) -> Result<BranchRouteConfig> {
        let supported_dexes = [];
        let mut best_config = None;
        let mut best_estimated_output = 0u64;

        // 尝试不同的分支数量
        for branch_count in 2..=max_branches.min(4) {
            let mut branches = Vec::new();
            let allocation_per_branch = 10000 / branch_count as u16;
            let mut allocation_bps = vec![allocation_per_branch; branch_count as usize];

            // 调整最后一个分支的分配，确保总和为10000
            let remainder = 10000 - (allocation_per_branch * branch_count as u16);
            if let Some(last) = allocation_bps.last_mut() {
                *last += remainder;
            }

            // 为每个分支生成路由
            for i in 0..branch_count {
                let dex_index = (i as usize) % supported_dexes.len();
                let branch_route = RouteConfig {
                    routing_mode_id: 1,
                    routes: vec![Route {
                        dex_id: supported_dexes[dex_index],
                        input_mint,
                        output_mint,
                    }],
                    amount_in: amount_in / branch_count as u64,
                    min_amount_out: 0,
                    max_slippage_bps: 300,
                    flash_loan: None,
                };
                branches.push(branch_route);
            }

            let config = BranchRouteConfig {
                input_mint,
                output_mint,
                branches,
                allocation_bps,
            };

            // 这里应该实际估算输出，暂时使用简化版本
            let estimated_output = amount_in; // 简化假设

            if estimated_output > best_estimated_output {
                best_estimated_output = estimated_output;
                best_config = Some(config);
            }
        }

        best_config.ok_or(RouteError::NoOptimalRoute.into())
    }
}
