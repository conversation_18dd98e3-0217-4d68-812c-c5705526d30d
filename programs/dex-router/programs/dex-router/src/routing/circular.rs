//! 循环套利路由引擎
//!
//! 实现循环路由模式：A -> B -> C -> A
//! 专门用于套利机会，支持闪电贷零成本套利

use anchor_lang::prelude::*;
use crate::constants::{RouteConfig, Dex};
use crate::{distribute_swap};
use crate::error::RouteError;
use crate::state::event::{SwapCompleted};

/// 套利预测结果
#[derive(Debug, Clone)]
pub struct ArbitragePrediction {
    /// 预测利润
    pub predicted_profit: u64,
    /// 总费用
    pub total_fees: u64,
    /// 价格影响
    pub price_impact: u64,
    /// 风险评分 (1-10)
    pub risk_score: u8,
    /// 是否盈利
    pub is_profitable: bool,
    /// 置信度 (0-100%)
    pub confidence_level: u8,
}

/// 循环套利路由执行器
pub struct CircularRouteExecutor;

impl CircularRouteExecutor {
    /// 执行循环套利路由
    /// 注意：这个函数不再处理闪电贷逻辑，闪电贷在更高层处理
    pub fn execute_circular_route<'info>(
        config: &RouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
    ) -> Result<u64> {
        // 验证路由配置
        Self::validate_circular_config(config)?;

        // 注意：闪电贷逻辑已移至上层，这里只处理纯粹的循环套利
        require!(
            config.flash_loan.is_none(),
            RouteError::InvalidFlashLoanConfig
        );

        let initial_token = config.routes[0].input_mint;
        let mut current_amount = config.amount_in;
        let mut total_fees = 0u64;
        let mut executed_routes = Vec::new();

        // 执行循环路由的每一步
        let mut offset: usize = 0;
        for (step, route) in config.routes.iter().enumerate() {
            msg!("执行循环套利步骤 {}/{}: {:?} -> {:?}",
                step + 1,
                config.routes.len(),
                route.input_mint,
                route.output_mint
            );

            // 执行单步交换
            let amount_out = distribute_swap(
                &route.dex_id.try_into()?,
                remaining_accounts,
                current_amount,
                &mut offset,
                route.input_mint,
                route.output_mint,
                None
            )?;

            // 计算交换费用（基于DEX类型估算）
            let step_fee = Self::calculate_swap_fee(&route.dex_id.try_into()?, current_amount)?;

            // 更新状态
            current_amount = amount_out;
            total_fees = total_fees.checked_add(step_fee)
                .ok_or(RouteError::MathOverflow)?;

            executed_routes.push(route.clone());

            // 发出单步完成事件
            emit!(SwapCompleted {
                dex: route.dex_id.try_into()?,
                input_mint: route.input_mint,
                output_mint: route.output_mint,
                amount_in: if step == 0 {
                    config.amount_in
                } else {
                    0
                },
                amount_out,
                fee_paid: step_fee,
                step: step as u8,
            });
        }

        // 计算利润（不涉及闪电贷）
        require!(
            current_amount >= config.amount_in,
            RouteError::InsufficientOutput
        );

        let profit = current_amount.checked_sub(config.amount_in)
            .ok_or(RouteError::MathOverflow)?;

        msg!("循环套利执行成功: 利润 {} tokens", profit);

        Ok(profit)
    }

    /// 计算交换费用（基于DEX类型）
    fn calculate_swap_fee(dex: &Dex, amount_in: u64) -> Result<u64> {
        let fee_bps = match dex {
            Dex::RaydiumClmm => 25,    // 0.25%
            Dex::RaydiumCpmm => 25,    // 0.25%
            Dex::MeteoraDlmm => 25,    // 0.25%
            Dex::MeteoraAmm => 30,     // 0.30%
            Dex::Orca => 30,           // 0.30%
            Dex::PumpSwapBuy => 100,   // 1.00%
            Dex::PumpSwapSell => 100,  // 1.00%
        };

        let fee = amount_in.checked_mul(fee_bps as u64)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        Ok(fee)
    }

    /// 计算实际滑点（基点）
    fn calculate_actual_slippage(amount_in: u64, amount_out: u64) -> Result<u16> {
        if amount_in == 0 {
            return Ok(0);
        }

        // 计算滑点百分比
        let slippage = if amount_out < amount_in {
            let loss = amount_in.checked_sub(amount_out)
                .ok_or(RouteError::MathOverflow)?;
            loss.checked_mul(10000)
                .and_then(|x| x.checked_div(amount_in))
                .ok_or(RouteError::MathOverflow)?
        } else {
            // 如果输出大于输入，滑点为0（实际上是盈利）
            0
        };

        // 确保滑点在合理范围内
        let slippage_bps = (slippage as u16).min(10000);

        Ok(slippage_bps)
    }


    /// 预测循环路由的盈利性
    pub fn predict_arbitrage_profitability(config: &RouteConfig) -> Result<ArbitragePrediction> {
        msg!("开始预测循环套利盈利性");

        // 基本验证
        require!(
            config.routes.len() >= 3,
            RouteError::InvalidRouteSteps
        );

        // 注意：这里不再处理闪电贷逻辑
        require!(
            config.flash_loan.is_none(),
            RouteError::InvalidFlashLoanConfig
        );

        let mut estimated_amount = config.amount_in;

        let mut total_fees = 0u64;
        let mut price_impact = 0u64;

        // 模拟每个步骤的交换
        for (i, route) in config.routes.iter().enumerate() {
            msg!("预测步骤 {}: {} -> {}, 数量: {}",
                 i + 1, route.input_mint, route.output_mint, estimated_amount);

            // 计算交换费用
            let step_fee = Self::calculate_swap_fee(&route.dex_id.try_into()?, estimated_amount)?;
            total_fees = total_fees.checked_add(step_fee)
                .ok_or(RouteError::MathOverflow)?;

            // 估算价格影响（基于交换数量和DEX类型）
            let step_price_impact = Self::estimate_price_impact(&route.dex_id.try_into()?, estimated_amount)?;
            price_impact = price_impact.checked_add(step_price_impact)
                .ok_or(RouteError::MathOverflow)?;

            // 模拟输出（简化模型：扣除费用和价格影响）
            let net_amount = estimated_amount.checked_sub(step_fee)
                .and_then(|x| x.checked_sub(step_price_impact))
                .ok_or(RouteError::MathOverflow)?;

            estimated_amount = net_amount;
        }

        // 计算预期利润（不涉及闪电贷）
        let predicted_profit = if estimated_amount > config.amount_in {
            estimated_amount.checked_sub(config.amount_in)
                .ok_or(RouteError::MathOverflow)?
        } else {
            0
        };

        // 计算风险评分
        let risk_score = Self::calculate_arbitrage_risk_score(config, total_fees, price_impact)?;

        let prediction = ArbitragePrediction {
            predicted_profit,
            total_fees,
            price_impact,
            risk_score,
            is_profitable: predicted_profit > 0,
            confidence_level: Self::calculate_confidence_level(config, risk_score)?,
        };

        msg!("套利预测完成: 预期利润: {}, 风险评分: {}, 可盈利: {}",
             predicted_profit, risk_score, prediction.is_profitable);

        Ok(prediction)
    }

    /// 估算价格影响
    fn estimate_price_impact(dex: &Dex, amount: u64) -> Result<u64> {
        // 基于DEX类型和交换数量估算价格影响
        let impact_bps = match dex {
            Dex::RaydiumClmm => {
                // CLMM 池通常有较低的价格影响
                if amount < 1_000_000 { 5 } else if amount < 10_000_000 { 15 } else { 50 }
            },
            Dex::RaydiumCpmm => {
                // CPMM 池价格影响稍高
                if amount < 1_000_000 { 10 } else if amount < 10_000_000 { 30 } else { 100 }
            },
            Dex::MeteoraDlmm => {
                // DLMM 动态费用，影响中等
                if amount < 1_000_000 { 8 } else if amount < 10_000_000 { 25 } else { 80 }
            },
            Dex::MeteoraAmm => {
                // AMM 池，影响较高
                if amount < 1_000_000 { 15 } else if amount < 10_000_000 { 45 } else { 150 }
            },
            Dex::Orca => {
                // Orca 池，影响中等
                if amount < 1_000_000 { 12 } else if amount < 10_000_000 { 35 } else { 120 }
            },
            Dex::PumpSwapBuy | Dex::PumpSwapSell => {
                // PumpFun 池通常流动性较小，价格影响较大
                if amount < 100_000 { 50 } else if amount < 1_000_000 { 200 } else { 500 }
            },
        };

        let impact = amount.checked_mul(impact_bps as u64)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        Ok(impact)
    }

    /// 计算套利风险评分 (1-10，10为最高风险)
    fn calculate_arbitrage_risk_score(
        config: &RouteConfig,
        total_fees: u64,
        price_impact: u64,
    ) -> Result<u8> {
        let mut risk_score = 1u8;

        // 基于路由步骤数增加风险
        risk_score = risk_score.saturating_add((config.routes.len() as u8).saturating_sub(3));

        // 基于交换金额增加风险
        let amount = config.amount_in;

        if amount > 100_000_000 { // > 100 tokens
            risk_score = risk_score.saturating_add(2);
        } else if amount > 10_000_000 { // > 10 tokens
            risk_score = risk_score.saturating_add(1);
        }

        // 基于费用和价格影响增加风险
        let total_cost = total_fees.saturating_add(price_impact);
        if total_cost > amount / 100 { // > 1% 成本
            risk_score = risk_score.saturating_add(2);
        } else if total_cost > amount / 200 { // > 0.5% 成本
            risk_score = risk_score.saturating_add(1);
        }

        // 注意：闪电贷逻辑已移除，这里不再考虑闪电贷风险

        // 确保风险评分在合理范围内
        risk_score = risk_score.min(10);

        Ok(risk_score)
    }

    /// 计算预测置信度
    fn calculate_confidence_level(config: &RouteConfig, risk_score: u8) -> Result<u8> {
        let mut confidence = 90u8; // 基础置信度90%

        // 基于风险评分降低置信度
        confidence = confidence.saturating_sub(risk_score * 5);

        // 基于路由复杂度降低置信度
        confidence = confidence.saturating_sub((config.routes.len() as u8).saturating_sub(3) * 3);

        // 注意：闪电贷逻辑已移除，这里不再考虑闪电贷对置信度的影响

        // 确保置信度在合理范围内
        confidence = confidence.max(10).min(95);

        Ok(confidence)
    }

    /// 验证循环路由配置
    pub fn validate_circular_config(config: &RouteConfig) -> Result<()> {

        require!(
            config.routes.len() >= 3 && config.routes.len() <= 6,
            RouteError::InvalidRouteSteps
        );

        require!(
            config.amount_in > 0,
            RouteError::ZeroAmount
        );

        // 验证路由形成闭环：最后输出应该等于第一个输入
        let first_input = config.routes[0].input_mint;
        let last_output = config.routes.last().unwrap().output_mint;
        require!(
            first_input == last_output,
            RouteError::InvalidCircularRoute
        );

        // 验证路由连续性
        for i in 1..config.routes.len() {
            require!(
                config.routes[i - 1].output_mint == config.routes[i].input_mint,
                RouteError::InvalidRouteConfig
            );
        }

        // 注意：闪电贷验证已移至上层，这里不再处理闪电贷配置

        Ok(())
    }
}
