//! 线性路由引擎
//!
//! 实现线性路由模式：A -> B -> C -> D
//! 支持多步骤跳转的直线交换

use anchor_lang::prelude::*;
use crate::constants::{RouteConfig, RoutingMode};
use crate::{distribute_swap, Dex, Route};
use crate::error::RouteError;
use crate::state::event::{RouteExecuted, route_phases};

/// 线性路由执行器
pub struct LinearRouteExecutor;

impl LinearRouteExecutor {
    /// 执行线性路由
    pub fn execute_linear_route<'info>(
        config: &RouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
        payer: &AccountInfo<'info>,
    ) -> Result<u64> {
        // 使用增强的验证逻辑
        Self::validate_linear_config(config)?;

        // 发出路由开始事件
        emit!(RouteExecuted {
            phase: route_phases::STARTED,
            mode: config.routing_mode_id.try_into()?,
            user: None,
            amount_in: config.amount_in,
            amount_out: 0,
            min_amount_out: config.min_amount_out,
            routes_count: config.routes.len() as u8,
            routes_executed: 0,
            total_fees: 0,
            execution_time: 0,
            success: false,
            actual_slippage_bps: 0,
            timestamp: Clock::get()?.unix_timestamp,
        });

        let mut current_amount = config.amount_in;

        // 逐步执行每个路由
        let mut offset: usize = 0;
        for (step, route) in config.routes.iter().enumerate() {
            msg!("执行线性路由步骤 {}/{}: {:?} -> {:?}",
                step + 1,
                config.routes.len(),
                route.input_mint,
                route.output_mint
            );


            // 执行单步交换
            let amount_out = distribute_swap(
                &route.dex_id.try_into()?,
                remaining_accounts,
                current_amount,
                &mut offset,
                route.input_mint,
                route.output_mint,
                Some(payer)
            )?;

            // 更新状态
            current_amount = amount_out;
        }

        // 验证最终输出是否满足最小要求
        require!(
            current_amount >= config.min_amount_out,
            RouteError::InsufficientOutput
        );

        Ok(current_amount)
    }


    /// 验证线性路由配置
    pub fn validate_linear_config(config: &RouteConfig) -> Result<()> {
        // require!(
        //     config.mode == RoutingMode::Linear,
        //     RouteError::InvalidRouteConfig
        // );

        require!(
            !config.routes.is_empty(),
            RouteError::EmptyRoute
        );

        require!(
            config.routes.len() <= 6,
            RouteError::TooManyRouteSteps
        );

        require!(
            config.amount_in > 0,
            RouteError::ZeroAmount
        );

        require!(
            config.min_amount_out > 0,
            RouteError::ZeroAmount
        );

        // 增强的路由连续性验证
        Self::validate_route_continuity(&config.routes)?;

        Ok(())
    }

    /// 增强的路由连续性验证
    /// 验证相邻路由步骤之间的连续性
    pub fn validate_route_continuity(routes: &[Route]) -> Result<()> {
        if routes.is_empty() {
            return Ok(());
        }

        for i in 1..routes.len() {
            let prev_route = &routes[i - 1];
            let current_route = &routes[i];

            // 1. 基本的mint连续性验证
            if prev_route.output_mint != current_route.input_mint {
                msg!(
                    "路由连续性失败：步骤{}的输出({}) 与 步骤{}的输入({}) 不匹配",
                    i - 1,
                    prev_route.output_mint,
                    i,
                    current_route.input_mint
                );
                return Err(RouteError::RouteDiscontinuity.into());
            }
        }

        msg!("路由连续性验证通过：{}个步骤", routes.len());
        Ok(())
    }
}