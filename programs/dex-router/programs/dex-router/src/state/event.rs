//! 统一事件定义
//!
//! 定义智能合约发出的所有事件，包括路由、套利、闪电贷等

use anchor_lang::prelude::*;
use crate::constants::{RoutingMode, Dex};

// ===== 配置和初始化事件 =====

/// 路由器配置初始化事件
#[event]
pub struct ConfigInitialized {
    pub admin: Pubkey,
    pub supported_dex_flags: u64,
    pub max_route_amount: u64,
    pub default_slippage_bps: u16,
}


/// 配置更新事件
#[event]
pub struct ConfigUpdated {
    /// 管理员公钥
    pub admin: Pubkey,
    
    /// 更新的字段
    pub field: String,
    
    /// 旧值
    pub old_value: String,
    
    /// 新值
    pub new_value: String,
    
    /// 时间戳
    pub timestamp: i64,
}

// ===== 路由执行事件 =====

/// 统一路由执行事件（合并 RouteStarted, RouteExecuted, RouteExecutionStarted, RouteExecutionCompleted）
#[event]
pub struct RouteExecuted {
    /// 执行阶段：0=开始，1=完成，2=失败
    pub phase: u8,
    
    /// 路由模式
    pub mode: RoutingMode,
    
    /// 用户公钥
    pub user: Option<Pubkey>,
    
    /// 输入金额
    pub amount_in: u64,
    
    /// 输出金额（完成时有效）
    pub amount_out: u64,
    
    /// 最小预期输出
    pub min_amount_out: u64,
    
    /// 路由步骤数
    pub routes_count: u8,
    
    /// 成功执行的路由数
    pub routes_executed: u8,
    
    /// 总手续费
    pub total_fees: u64,
    
    /// 执行时间（毫秒）
    pub execution_time: u32,
    
    /// 是否成功
    pub success: bool,
    
    /// 实际滑点（基点）
    pub actual_slippage_bps: u16,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 单步交换完成事件
#[event]
pub struct SwapCompleted {
    pub dex: Dex,
    pub input_mint: Pubkey,
    pub output_mint: Pubkey,
    pub amount_in: u64,
    pub amount_out: u64,
    pub fee_paid: u64,
    pub step: u8,
}

/// 分支执行完成事件
#[event]
pub struct BranchExecuted {
    pub branch_index: u8,
    pub input_token: Pubkey,
    pub output_token: Pubkey,
    pub amount_in: u64,
    pub amount_out: u64,
    pub allocation_bps: u16,
    pub routes_in_branch: u8,
}

/// 批量执行完成事件
#[event]
pub struct BatchExecuted {
    pub total_routes: u8,
    pub successful_routes: u8,
    pub failed_routes: u8,
    pub total_amount_in: u64,
    pub total_amount_out: u64,
    pub total_fees: u64,
    pub atomic_mode: bool,
}


/// DEX操作日志事件
#[event]
pub struct DexOperationLogged {
    /// DEX类型
    pub dex_type: u8,
    
    /// 操作类型
    pub operation: String,
    
    /// 输入代币
    pub input_mint: Pubkey,
    
    /// 输出代币
    pub output_mint: Pubkey,
    
    /// 输入金额
    pub amount_in: u64,
    
    /// 输出金额
    pub amount_out: u64,
    
    /// 是否成功
    pub success: bool,
    
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 用户等级升级事件
#[event]
pub struct UserLevelUp {
    /// 用户公钥
    pub user: Pubkey,
    
    /// 旧等级
    pub old_level: u8,
    
    /// 新等级
    pub new_level: u8,
    
    /// 总交易次数
    pub total_trades: u32,
    
    /// 成功率（基点）
    pub success_rate_bps: u16,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 紧急停止事件
#[event]
pub struct EmergencyStopTriggered {
    /// 触发者
    pub triggered_by: Pubkey,
    
    /// 停止类型（0=全局，1=单个DEX）
    pub stop_type: u8,
    
    /// 受影响的DEX（如果是单个DEX停止）
    pub affected_dex: Option<u8>,
    
    /// 停止原因
    pub reason: String,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 紧急恢复事件
#[event]
pub struct EmergencyRecovered {
    /// 触发者
    pub triggered_by: Pubkey,
    
    /// 恢复类型（0=全局，1=单个DEX）
    pub recovery_type: u8,
    
    /// 恢复的DEX（如果是单个DEX恢复）
    pub recovered_dex: Option<u8>,
    
    /// 停止持续时间（秒）
    pub stop_duration_seconds: u64,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 路由执行阶段常量
pub mod route_phases {
    pub const STARTED: u8 = 0;
    pub const COMPLETED: u8 = 1;
    pub const FAILED: u8 = 2;
}



