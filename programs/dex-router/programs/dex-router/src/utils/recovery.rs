
/// 错误恢复工具
pub struct ErrorRecovery;

impl ErrorRecovery {
    pub fn attempt_recovery(error_code: u32) -> bool {
        // 简化的错误恢复逻辑
        match error_code {
            // 临时性错误可以恢复
            6100..=6200 => true,
            // 永久性错误无法恢复
            _ => false,
        }
    }
}

/// 重试策略
pub struct RetryStrategy {
    pub max_attempts: u8,
    pub delay_ms: u64,
    pub exponential_backoff: bool,
}

impl Default for RetryStrategy {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            delay_ms: 100,
            exponential_backoff: true,
        }
    }
}
